import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';
import { useQuery } from '@tanstack/react-query';

// Define the Document type for user documents
interface UserDocument {
  id: string;
  title: string;
  type: 'Will' | 'Trust' | 'POA' | 'Other';
  status: 'draft' | 'signed' | 'shipped' | 'received' | 'archived' | 'approved' | 'rejected';
  dateCreated: string;
  lastModified?: string;
  version: string;
  fileUrl?: string;
  fileName?: string;
  fileKey?: string;
  assignedWelonTrustId?: string;
  rejectionReason?: string;
  content: string;
}

export function useUserDocuments() {
  const [documents, setDocuments] = useState<UserDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const client = generateClient<Schema>();

  // Use React Query for fetching user documents
  const { refetch } = useQuery({
    queryKey: ['userDocuments'],
    queryFn: async () => {
      setLoading(true);
      setError(null);

      try {
        // Get current user
        const user = await getCurrentUser();

        // Fetch documents belonging to this user
        const result = await client.models.Document.list({
          filter: {
            or: [
              { assignedWelonTrustId: { eq: user.userId } },
              { userId: { eq: user.userId } }
            ]
          }
        });

        if (result.data) {
          
          // Transform documents to include file metadata
          const transformedDocuments = result.data.map(doc => {
            let fileMetadata = { fileName: `${doc.title}.pdf` };
            
            try {
              const content = JSON.parse(doc.content);
              fileMetadata = {
                fileName: content.fileMetadata?.fileName || `${doc.title}.pdf`,
              };
            } catch (e) {
              // If content is not JSON, use default
            }

            return {
              ...doc,
              fileName: fileMetadata.fileName,
              fileKey: doc.fileUrl, // fileUrl contains the S3 key
            } as UserDocument;
          });

          // Sort by dateCreated descending (newest first)
          const sortedDocuments = transformedDocuments.sort((a, b) =>
            new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime()
          );

          setDocuments(sortedDocuments);
        } else {
          setDocuments([]);
        }
        return result.data;
      } catch (err: any) {
        console.error('Error fetching user documents:', err);
        const errorMsg = `Failed to load documents: ${err.message || 'Unknown error'}`;
        setError(errorMsg);
        throw new Error(errorMsg);
      } finally {
        setLoading(false);
      }
    },
    enabled: false,
  });

  // Expose the fetchDocuments function that uses React Query's refetch
  const fetchUserDocuments = async () => {
    return refetch();
  };

  // Load documents on component mount
  useEffect(() => {
    fetchUserDocuments();
  }, []);

  return {
    documents,
    loading,
    error,
    fetchUserDocuments,
  };
}
