'use client';

import React from 'react';
import { useInterviewNew } from './interview-new-context';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Circle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

export const ProgressBarNew: React.FC = () => {
  const {
    questions,
    currentQuestionIndex,
    getProgress,
    getAnswerForQuestion,
    isQuestionAnswered,
    goToQuestion,
    getVisibleQuestions,
    shouldShowQuestion,
  } = useInterviewNew();

  const visibleQuestions = getVisibleQuestions();

  const progress = getProgress();

  if (questions.length === 0) {
    return null;
  }

  return (
    <Card className='mb-6'>
      <CardContent className='p-4'>
        <div className='space-y-4'>
          {/* Overall progress */}
          <div className='flex items-center justify-between'>
            <h3 className='text-sm font-medium text-gray-700'>
              Interview Progress
            </h3>
            <span className='text-sm text-gray-500'>{progress}% Complete</span>
          </div>

          {/* Progress bar */}
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-blue-600 h-2 rounded-full transition-all duration-500'
              style={{ width: `${progress}%` }}
            ></div>
          </div>

          {/* Question indicators - only show visible questions */}
          <div className='flex flex-wrap gap-2 mt-4'>
            {visibleQuestions.map((question, visibleIndex) => {
              const originalIndex = questions.findIndex(
                q => q.questionId === question.questionId
              );
              const isAnswered = isQuestionAnswered(question.questionId);
              const isCurrent = originalIndex === currentQuestionIndex;

              return (
                <button
                  key={question.questionId}
                  onClick={() => goToQuestion(originalIndex)}
                  className={cn(
                    'flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-colors',
                    'hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500',
                    {
                      'bg-blue-100 text-blue-700 border border-blue-200':
                        isCurrent,
                      'bg-green-100 text-green-700': isAnswered && !isCurrent,
                      'bg-gray-50 text-gray-500': !isAnswered && !isCurrent,
                    }
                  )}
                  title={`Question ${visibleIndex + 1}: ${question.text}`}
                >
                  {isAnswered ? (
                    <CheckCircle className='w-3 h-3' />
                  ) : isCurrent ? (
                    <Clock className='w-3 h-3' />
                  ) : (
                    <Circle className='w-3 h-3' />
                  )}
                  <span>{visibleIndex + 1}</span>
                </button>
              );
            })}
          </div>

          {/* Summary stats */}
          <div className='flex justify-between text-xs text-gray-500 pt-2 border-t'>
            <span>
              Answered:{' '}
              {
                visibleQuestions.filter(q => isQuestionAnswered(q.questionId))
                  .length
              }{' '}
              of {visibleQuestions.length} visible
            </span>
            <span>
              Current: Question{' '}
              {visibleQuestions.findIndex(
                q =>
                  q.questionId === questions[currentQuestionIndex]?.questionId
              ) + 1}{' '}
              of {visibleQuestions.length} visible
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
