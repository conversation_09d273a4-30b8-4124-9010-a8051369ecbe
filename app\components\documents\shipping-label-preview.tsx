'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Truck,
  Package,
  MapPin,
  Clock,
  CheckSquare,
  AlertTriangle,
  Phone,
} from 'lucide-react';

interface ShippingLabelPreviewProps {
  className?: string;
}

export function ShippingLabelPreview({
  className = '',
}: ShippingLabelPreviewProps) {
  return (
    <Card className={`max-w-2xl ${className}`}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Truck className='h-5 w-5 text-orange-600' />
          UPS Shipping Label Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='border-2 border-dashed border-gray-300 p-6 bg-background'>
          {/* UPS Header */}
          <div className='flex items-center justify-between mb-6'>
            <div className='text-2xl font-bold text-orange-600'>UPS</div>
            <Badge
              variant='secondary'
              className='bg-orange-100 text-orange-800'
            >
              PREPAID
            </Badge>
          </div>

          {/* Tracking Number */}
          <div className='mb-6'>
            <div className='text-xs text-[var(--custom-gray-medium)] mb-1'>
              TRACKING NUMBER
            </div>
            <div className='text-lg font-mono font-bold'>1Z999AA1234567890</div>
            <div className='mt-2'>
              <div className='h-8 bg-black flex items-center justify-center'>
                <div className='text-white text-xs font-mono'>
                  ||||| |||| ||||| |||| |||||
                </div>
              </div>
            </div>
          </div>

          {/* Ship To Section */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-6'>
            <div>
              <div className='text-xs font-semibold text-[var(--custom-gray-medium)] mb-2'>
                SHIP TO:
              </div>
              <div className='border p-3 bg-gray-50'>
                <div className='font-semibold'>WELON TRUST</div>
                <div className='text-sm'>Document Processing Center</div>
                <div className='text-sm'>1234 Trust Avenue</div>
                <div className='text-sm'>Suite 100</div>
                <div className='text-sm'>Legal City, LC 12345</div>
              </div>
            </div>

            <div>
              <div className='text-xs font-semibold text-[var(--custom-gray-medium)] mb-2'>
                SHIP FROM:
              </div>
              <div className='border p-3 bg-gray-50'>
                <div className='font-semibold'>[YOUR NAME]</div>
                <div className='text-sm'>[YOUR ADDRESS]</div>
                <div className='text-sm'>[CITY, STATE ZIP]</div>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div className='grid grid-cols-2 gap-4 mb-6'>
            <div>
              <div className='text-xs text-[var(--custom-gray-medium)]'>
                SERVICE
              </div>
              <div className='font-semibold'>UPS Ground</div>
            </div>
            <div>
              <div className='text-xs text-[var(--custom-gray-medium)]'>
                WEIGHT
              </div>
              <div className='font-semibold'>1.0 LB</div>
            </div>
          </div>

          {/* Special Instructions */}
          <div className='border-t pt-4'>
            <div className='text-xs font-semibold text-[var(--custom-gray-medium)] mb-2'>
              SPECIAL INSTRUCTIONS:
            </div>
            <div className='text-sm text-[var(--custom-gray-dark)]'>
              ESTATE PLANNING DOCUMENTS - HANDLE WITH CARE
            </div>
          </div>

          {/* Bottom Barcode */}
          <div className='mt-6 pt-4 border-t'>
            <div className='h-12 bg-black flex items-center justify-center'>
              <div className='text-white text-xs font-mono'>
                |||||| ||||| |||||| ||||| |||||| ||||| ||||||
              </div>
            </div>
          </div>
        </div>

        {/* Preview Note */}
        <div className='mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg'>
          <div className='flex items-start gap-2'>
            <Package className='h-4 w-4 text-yellow-600 mt-0.5' />
            <div className='text-sm text-yellow-800'>
              <div className='font-medium mb-1'>Preview Only</div>
              <div>
                This is a preview of what your shipping label will look like.
                The actual label will include your specific address and a unique
                tracking number.
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Component for showing what the complete execution package contains
export function ExecutionPackagePreview({
  className = '',
}: {
  className?: string;
}) {
  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Package className='h-5 w-5 text-green-600' />
          Execution Package Contents
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          {/* Page 1 */}
          <div className='flex items-start gap-3 p-3 border rounded-lg'>
            <div className='bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded'>
              PAGE 1
            </div>
            <div>
              <div className='font-medium'>Instructions & Checklist</div>
              <div className='text-sm text-[var(--custom-gray-medium)]'>
                Step-by-step signing, notarization, and mailing instructions
              </div>
            </div>
          </div>

          {/* Page 2 */}
          <div className='flex items-start gap-3 p-3 border rounded-lg'>
            <div className='bg-orange-100 text-orange-800 text-xs font-bold px-2 py-1 rounded'>
              PAGE 2
            </div>
            <div>
              <div className='font-medium'>Prepaid UPS Shipping Label</div>
              <div className='text-sm text-[var(--custom-gray-medium)]'>
                Ready-to-use shipping label for mailing to Welon Trust
              </div>
            </div>
          </div>

          {/* Pages 3+ */}
          <div className='flex items-start gap-3 p-3 border rounded-lg'>
            <div className='bg-green-100 text-green-800 text-xs font-bold px-2 py-1 rounded'>
              PAGE 3+
            </div>
            <div>
              <div className='font-medium'>Your Estate Planning Documents</div>
              <div className='text-sm text-[var(--custom-gray-medium)]'>
                Will, Trust, Power of Attorney, and other selected documents
              </div>
            </div>
          </div>
        </div>

        {/* Timeline */}
        <div className='mt-6 p-4 bg-gray-50 rounded-lg'>
          <div className='text-sm font-medium mb-3'>Expected Timeline:</div>
          <div className='space-y-2 text-sm text-[var(--custom-gray-medium)]'>
            <div className='flex items-center gap-2'>
              <Clock className='h-3 w-3' />
              <span>Print documents: 5-10 minutes</span>
            </div>
            <div className='flex items-center gap-2'>
              <MapPin className='h-3 w-3' />
              <span>Notarization visit: 15-30 minutes</span>
            </div>
            <div className='flex items-center gap-2'>
              <Truck className='h-3 w-3' />
              <span>UPS delivery: 3-5 business days</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Component for showing the instructions page preview
export function InstructionsPreview({
  className = '',
}: {
  className?: string;
}) {
  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <CheckSquare className='h-5 w-5 text-blue-600' />
          Execution Instructions Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-6 text-sm'>
          {/* Header */}
          <div className='text-center border-b pb-4'>
            <h2 className='text-xl font-bold text-[var(--custom-gray-dark)]'>
              Estate Planning Document Execution Instructions
            </h2>
            <p className='text-[var(--custom-gray-medium)] mt-2'>
              Please follow these steps carefully to ensure proper execution
            </p>
          </div>

          {/* Step 1: Printing */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-[var(--custom-gray-dark)] flex items-center gap-2'>
              <div className='bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full'>
                1
              </div>
              Printing Instructions
            </h3>
            <div className='ml-6 space-y-2'>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Print all pages on standard 8.5" x 11" white paper</span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>
                  Print single-sided only (do not use duplex printing)
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Ensure all text is clearly readable and not cut off</span>
              </div>
            </div>
          </div>

          {/* Step 2: Signing */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-[var(--custom-gray-dark)] flex items-center gap-2'>
              <div className='bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full'>
                2
              </div>
              Document Signing
            </h3>
            <div className='ml-6 space-y-2'>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>
                  Sign all documents in the designated signature areas
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Use blue or black ink only</span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Do not sign until you are in front of the notary</span>
              </div>
            </div>
          </div>

          {/* Step 3: Notarization */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-[var(--custom-gray-dark)] flex items-center gap-2'>
              <div className='bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full'>
                3
              </div>
              Notarization
            </h3>
            <div className='ml-6 space-y-2'>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>
                  Bring valid photo ID (driver's license, passport, or state ID)
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>
                  Find a notary: banks, UPS stores, libraries, or search online
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Typical cost: $5-$15 per document</span>
              </div>
              <div className='flex items-start gap-2'>
                <AlertTriangle className='h-4 w-4 text-yellow-600 mt-0.5' />
                <span>
                  Do not sign the documents before meeting with the notary
                </span>
              </div>
            </div>
          </div>

          {/* Step 4: Mailing */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-[var(--custom-gray-dark)] flex items-center gap-2'>
              <div className='bg-blue-100 text-blue-800 text-xs font-bold px-2 py-1 rounded-full'>
                4
              </div>
              Mailing Instructions
            </h3>
            <div className='ml-6 space-y-2'>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Use the prepaid UPS shipping label (Page 2)</span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Place documents in a manila envelope or folder</span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>
                  Attach the shipping label to the outside of the package
                </span>
              </div>
              <div className='flex items-start gap-2'>
                <CheckSquare className='h-4 w-4 text-green-600 mt-0.5' />
                <span>Drop off at any UPS location or schedule pickup</span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className='bg-gray-50 p-4 rounded-lg'>
            <h3 className='font-semibold text-[var(--custom-gray-dark)] mb-2 flex items-center gap-2'>
              <Phone className='h-4 w-4' />
              Need Help?
            </h3>
            <p className='text-sm text-[var(--custom-gray-medium)]'>
              If you have questions about these instructions, contact Welon
              Trust support at:
            </p>
            <div className='mt-2 text-sm'>
              <div>📞 Phone: 1-800-WELON-TRUST</div>
              <div>📧 Email: <EMAIL></div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
