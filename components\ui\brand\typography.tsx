'use client';

import React from 'react';

interface TypographyProps {
  children: React.ReactNode;
  className?: string;
}

export function Headline({ children, className = '' }: TypographyProps) {
  return (
    <h1
      className={`font-semibold text-4xl ${className}`}
      style={{ fontFamily: 'var(--font-dm-sans), sans-serif' }}
    >
      {children}
    </h1>
  );
}

export function Subhead({ children, className = '' }: TypographyProps) {
  return (
    <h2
      className={`font-medium text-3xl ${className}`}
      style={{ fontFamily: 'var(--font-dm-sans), sans-serif' }}
    >
      {children}
    </h2>
  );
}

export function SubheadBold({ children, className = '' }: TypographyProps) {
  return (
    <h3
      className={`font-bold text-2xl ${className}`}
      style={{ fontFamily: 'var(--font-dm-sans), sans-serif' }}
    >
      {children}
    </h3>
  );
}

export function BodyCopy({ children, className = '' }: TypographyProps) {
  return (
    <p
      className={`font-normal text-base ${className}`}
      style={{ fontFamily: 'var(--font-inter), sans-serif' }}
    >
      {children}
    </p>
  );
}

export function Callout({ children, className = '' }: TypographyProps) {
  return (
    <p
      className={`font-normal italic ${className}`}
      style={{ fontFamily: 'var(--font-inter), sans-serif' }}
    >
      {children}
    </p>
  );
}
