'use client';

import React, { useState, useEffect } from 'react';
import { LivingDocumentForm } from '@/components/living-documents/living-document-form';
import { LivingDocument } from '@/components/living-documents/types';
import { useParams } from 'next/navigation';

// Mock data for demonstration
const mockLivingDocuments: LivingDocument[] = [
  {
    id: '1',
    title: 'Emergency Contacts',
    type: 'emergency-contacts',
    description: 'List of emergency contacts and their information',
    content:
      'Emergency Contact 1:\nName: <PERSON>\nPhone: (*************\nRelationship: Friend\n\nEmergency Contact 2:\nName: <PERSON>\nPhone: (*************\nRelationship: Neighbor',
    createdAt: 'May 10, 2025',
    lastUpdated: 'June 15, 2025',
    nextReview: 'December 15, 2025',
    reviewFrequency: 'Every 6 months',
    status: 'current',
  },
  {
    id: '2',
    title: 'Pet Care Instructions',
    type: 'pet-care',
    description: 'Instructions for taking care of my pets',
    content:
      'Pet: Max (Dog)\nBreed: Golden Retriever\nAge: 5 years\nFeeding: 2 cups of dry food twice daily\nMedication: Heartworm pill on the 1st of each month\nVeterinarian: Dr. Smith at City Pet Clinic (*************\n\nPet: Whiskers (Cat)\nBreed: Siamese\nAge: 3 years\nFeeding: 1/2 cup of wet food twice daily\nLitter box: Clean daily\nVeterinarian: Dr. Jones at City Pet Clinic (*************',
    createdAt: 'April 5, 2025',
    lastUpdated: 'May 20, 2025',
    nextReview: 'August 20, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-soon',
  },
  {
    id: '3',
    title: 'Digital Asset Management',
    type: 'digital-assets',
    description: 'Inventory and access information for digital assets',
    content:
      "Email Accounts:\n1. <EMAIL> - Password stored in password manager\n2. <EMAIL> - Password stored in password manager\n\nSocial Media:\n1. Facebook - Username: myusername\n2. Twitter - Username: @myhandle\n\nFinancial Accounts:\n1. Online Banking - Access through password manager\n2. Investment Account - Access through password manager\n\nPassword Manager:\nLastPass - Master password hint: First pet's name + year of birth",
    createdAt: 'March 15, 2025',
    lastUpdated: 'March 15, 2025',
    nextReview: 'June 15, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-needed',
  },
];

export default function UpdateLivingDocumentPage() {
  const params = useParams();
  const documentId = params.id as string;
  const [document, setDocument] = useState<LivingDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    const fetchDocument = () => {
      setLoading(true);
      try {
        const foundDocument = mockLivingDocuments.find(
          doc => doc.id === documentId
        );
        if (foundDocument) {
          setDocument(foundDocument);
        } else {
          setError('Document not found');
        }
      } catch (err) {
        setError('Failed to load document');
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
  }, [documentId]);

  if (loading) {
    return (
      <div className='max-w-7xl mx-auto text-center'>
        <p>Loading document...</p>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='bg-red-50 p-6 rounded-lg border border-red-100'>
          <h3 className='text-lg font-medium text-red-800 mb-2'>Error</h3>
          <p className='text-red-700'>{error || 'Document not found'}</p>
        </div>
      </div>
    );
  }

  const initialData = {
    id: document.id,
    title: document.title,
    type: document.type,
    content: document.content,
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-black-6c mb-2'>
          Update Living Document
        </h1>
        <p className='text-[var(--custom-gray-medium)]'>
          Update your living document to keep the information current and
          relevant.
        </p>
      </div>

      <LivingDocumentForm initialData={initialData} mode='update' />

      <div className='bg-blue-50 p-6 rounded-lg border border-blue-100 mt-8'>
        <h3 className='text-lg font-medium text-blue-800 mb-2'>
          About Document Updates
        </h3>
        <p className='text-blue-700 mb-4'>
          Regular updates help ensure your living documents contain accurate and
          up-to-date information.
        </p>
        <ul className='list-disc list-inside text-blue-700 space-y-2'>
          <li>Each update creates a new version of your document</li>
          <li>Previous versions are archived and can be accessed if needed</li>
          <li>The system tracks when each document was last updated</li>
          <li>
            You'll receive reminders when it's time to review your documents
            again
          </li>
        </ul>
      </div>
    </div>
  );
}
