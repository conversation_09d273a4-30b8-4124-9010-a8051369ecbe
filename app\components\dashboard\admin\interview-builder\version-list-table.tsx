'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Edit,
  Copy,
  Trash2,
  Play,
  Clock,
  FileText,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  GitBranch,
} from 'lucide-react';
import {
  InterviewSetWithVersion,
  InterviewVersion,
} from '@/types/interview-builder';

interface VersionListTableProps {
  versions: InterviewSetWithVersion[];
  loading: boolean;
  actionLoading: string | null;
  onEdit: (version: InterviewSetWithVersion) => void;
  onBuild: (version: InterviewSetWithVersion) => void;
  onPreview: (version: InterviewSetWithVersion) => void;
  onDuplicate: (version: InterviewSetWithVersion) => void;
  onDelete: (version: InterviewSetWithVersion) => void;
  getStatusBadge: (status: InterviewVersion['status']) => React.ReactNode;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function VersionListTable({
  versions,
  loading,
  actionLoading,
  onEdit,
  onBuild,
  onPreview,
  onDuplicate,
  onDelete,
  getStatusBadge,
  currentPage,
  totalPages,
  onPageChange,
}: VersionListTableProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours}h ${remainingMinutes}m`
      : `${hours}h`;
  };

  if (loading) {
    return (
      <div className='space-y-3'>
        {[...Array(5)].map((_, i) => (
          <div key={i} className='h-16 bg-gray-100 rounded animate-pulse' />
        ))}
      </div>
    );
  }

  if (versions.length === 0) {
    return (
      <div className='text-center py-12'>
        <FileText className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
        <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
          No versions found
        </h3>
        <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
          Get started by creating your first interview version.
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Version & Name</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Questions</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Template</TableHead>
            <TableHead>Parent Version</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {versions.map(version => (
            <TableRow key={version.id} className='hover:bg-gray-50'>
              <TableCell>
                <div>
                  <div className='flex items-center space-x-2'>
                    <Badge variant='secondary' className='text-xs'>
                      v{version.latestVersionNumber}
                    </Badge>
                    <span className='font-medium text-[var(--custom-gray-dark)]'>
                      {version.name}
                    </span>
                  </div>
                  <div className='text-sm text-[var(--custom-gray-medium)] mt-1 max-w-xs truncate'>
                    {version.description}
                  </div>
                </div>
              </TableCell>
              {/*<TableCell>{getStatusBadge(version.status)}</TableCell>*/}
              <TableCell>{getStatusBadge('draft')}</TableCell>
              <TableCell>
                <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                  <FileText className='h-4 w-4 mr-1' />
                  {version.questionsCount}
                </div>
              </TableCell>
              <TableCell>
                <div className='flex items-center text-sm text-[var(--custom-gray-medium)]'>
                  <Clock className='h-4 w-4 mr-1' />
                  {/*{formatDuration(version.estimatedDuration)}*/}
                  {'15m'}
                </div>
              </TableCell>
              <TableCell>
                {/*<div className='text-sm text-[var(--custom-gray-medium)]'>*/}
                {/*  {version.targetTemplate ? (*/}
                {/*    <Badge variant='outline' className='text-xs'>*/}
                {/*      {version.targetTemplate}*/}
                {/*    </Badge>*/}
                {/*  ) : (*/}
                {/*    <span className='text-[var(--custom-gray-medium)]'>*/}
                {/*      No template*/}
                {/*    </span>*/}
                {/*  )}*/}
                {/*</div>*/}
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  <span className='text-[var(--custom-gray-medium)]'>
                    No template
                  </span>
                </div>
              </TableCell>
              <TableCell>
                {/*<div className='text-sm text-[var(--custom-gray-medium)]'>*/}
                {/*  {version.parentVersionId ? (*/}
                {/*    <div className='flex items-center'>*/}
                {/*      <GitBranch className='h-3 w-3 mr-1' />*/}
                {/*      <span className='text-xs'>{version.parentVersionId}</span>*/}
                {/*    </div>*/}
                {/*  ) : (*/}
                {/*    <span className='text-[var(--custom-gray-medium)] text-xs'>*/}
                {/*      Root*/}
                {/*    </span>*/}
                {/*  )}*/}
                {/*</div>*/}
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  <span className='text-[var(--custom-gray-medium)] text-xs'>
                    Root
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  {version?.updatedAt ? formatDate(version.updatedAt) : 'N/A'}
                  {/*{formatDate(version.updatedAt)}*/}
                </div>
                {/*<div className='text-xs text-[var(--custom-gray-medium)]'>*/}
                {/*  by {version.createdBy}*/}
                {/*</div>*/}
              </TableCell>
              <TableCell className='text-right'>
                <div className='flex items-center space-x-1'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onBuild(version)}
                    disabled={actionLoading?.includes(version.id)}
                    title='Edit Version'
                  >
                    <Edit className='h-4 w-4' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onPreview(version)}
                    disabled={actionLoading?.includes(version.id)}
                    title='Preview'
                  >
                    <Play className='h-4 w-4' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onDuplicate(version)}
                    disabled={actionLoading === `duplicate-${version.id}`}
                    title={
                      actionLoading === `duplicate-${version.id}`
                        ? 'Duplicating...'
                        : 'Duplicate Version'
                    }
                  >
                    <Copy className='h-4 w-4' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => onDelete(version)}
                    disabled={actionLoading === `delete-${version.id}`}
                    className='text-red-600 hover:text-red-700'
                    title={
                      actionLoading === `delete-${version.id}`
                        ? 'Deleting...'
                        : 'Delete Version'
                    }
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className='flex items-center justify-between'>
          <div className='text-sm text-[var(--custom-gray-medium)]'>
            Page {currentPage} of {totalPages}
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className='h-4 w-4' />
              Previous
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className='h-4 w-4' />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
