'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertCircle, Save, ArrowLeft, Check } from 'lucide-react';

// Define the document section type
export interface DocumentSection {
  id: string;
  title: string;
  description: string;
  fields: DocumentField[];
}

// Define the document field type
export interface DocumentField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number';
  value: string;
  options?: { value: string; label: string }[];
  validation?: {
    required?: boolean;
    pattern?: RegExp;
    message?: string;
  };
}

interface DocumentUpdateFormProps {
  documentId: string;
  documentType: string;
  sections: DocumentSection[];
  onSave: (documentId: string, sections: DocumentSection[]) => void;
}

export function DocumentUpdateForm({
  documentId,
  documentType,
  sections: initialSections,
  onSave,
}: DocumentUpdateFormProps) {
  const router = useRouter();
  const [sections, setSections] = useState<DocumentSection[]>(initialSections);
  const [activeTab, setActiveTab] = useState<string>(
    initialSections[0]?.id || ''
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [significantChanges, setSignificantChanges] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showNoChangesDialog, setShowNoChangesDialog] = useState(false);

  // Handle field change
  const handleFieldChange = (
    sectionId: string,
    fieldId: string,
    value: string
  ) => {
    setSections(prevSections =>
      prevSections.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            fields: section.fields.map(field => {
              if (field.id === fieldId) {
                // Check if this is a significant change (e.g., removing a beneficiary)
                if (
                  field.label.toLowerCase().includes('beneficiary') &&
                  field.value &&
                  !value
                ) {
                  setSignificantChanges(prev => [
                    ...prev,
                    `Removed ${field.value} as a beneficiary`,
                  ]);
                }
                return { ...field, value };
              }
              return field;
            }),
          };
        }
        return section;
      })
    );

    // Clear any error for this field
    if (errors[`${sectionId}-${fieldId}`]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${sectionId}-${fieldId}`];
        return newErrors;
      });
    }
  };

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.validation?.required && !field.value.trim()) {
          newErrors[`${section.id}-${field.id}`] =
            field.validation.message || 'This field is required';
          hasErrors = true;
        } else if (
          field.validation?.pattern &&
          field.value &&
          !field.validation.pattern.test(field.value)
        ) {
          newErrors[`${section.id}-${field.id}`] =
            field.validation.message || 'Invalid format';
          hasErrors = true;
        }
      });
    });

    setErrors(newErrors);
    return !hasErrors;
  };

  // Check if any changes were made
  const hasChanges = () => {
    return sections.some(section =>
      section.fields.some(field => {
        const originalField = initialSections
          .find(s => s.id === section.id)
          ?.fields.find(f => f.id === field.id);

        return originalField && originalField.value !== field.value;
      })
    );
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      // Scroll to the first error
      const firstErrorId = Object.keys(errors)[0];
      if (firstErrorId) {
        const [sectionId] = firstErrorId.split('-');
        setActiveTab(sectionId);
      }
      return;
    }

    // Check if there are any changes
    if (!hasChanges()) {
      setShowNoChangesDialog(true);
      return;
    }

    // Check if there are significant changes that need confirmation
    if (significantChanges.length > 0) {
      setShowConfirmDialog(true);
    } else {
      handleSave();
    }
  };

  // Handle save after confirmation
  const handleSave = () => {
    setIsSaving(true);

    // In a real implementation, this would call an API to save the document
    setTimeout(() => {
      onSave(documentId, sections);
      setIsSaving(false);
      router.push('/dashboard/member/documents/history');
    }, 1000);
  };

  // Handle cancel
  const handleCancel = () => {
    router.push('/dashboard/member/documents/history');
  };

  // Handle "No Changes" confirmation
  const handleNoChangesConfirm = () => {
    setShowNoChangesDialog(false);
    router.push('/dashboard/member/documents/history');
  };

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Update {documentType}</CardTitle>
            <CardDescription>
              Review and update your document information to keep it current
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className='mb-4'>
                {sections.map(section => (
                  <TabsTrigger key={section.id} value={section.id}>
                    {section.title}
                  </TabsTrigger>
                ))}
              </TabsList>

              {sections.map(section => (
                <TabsContent key={section.id} value={section.id}>
                  <div className='space-y-4'>
                    <div className='text-sm text-muted-foreground mb-4'>
                      {section.description}
                    </div>

                    {section.fields.map(field => (
                      <div key={field.id} className='space-y-2'>
                        <label className='text-sm font-medium'>
                          {field.label}
                          {field.validation?.required && (
                            <span className='text-red-500 ml-1'>*</span>
                          )}
                        </label>

                        {field.type === 'textarea' ? (
                          <Textarea
                            value={field.value}
                            onChange={e =>
                              handleFieldChange(
                                section.id,
                                field.id,
                                e.target.value
                              )
                            }
                            placeholder={`Enter ${field.label.toLowerCase()}`}
                            className={
                              errors[`${section.id}-${field.id}`]
                                ? 'border-red-500'
                                : ''
                            }
                          />
                        ) : field.type === 'select' && field.options ? (
                          <select
                            value={field.value}
                            onChange={e =>
                              handleFieldChange(
                                section.id,
                                field.id,
                                e.target.value
                              )
                            }
                            className={`w-full p-2 border rounded-md ${errors[`${section.id}-${field.id}`] ? 'border-red-500' : 'border-input'}`}
                          >
                            <option value=''>
                              Select {field.label.toLowerCase()}
                            </option>
                            {field.options.map(option => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                        ) : (
                          <Input
                            type={field.type}
                            value={field.value}
                            onChange={e =>
                              handleFieldChange(
                                section.id,
                                field.id,
                                e.target.value
                              )
                            }
                            placeholder={`Enter ${field.label.toLowerCase()}`}
                            className={
                              errors[`${section.id}-${field.id}`]
                                ? 'border-red-500'
                                : ''
                            }
                          />
                        )}

                        {errors[`${section.id}-${field.id}`] && (
                          <p className='text-xs text-red-500 mt-1'>
                            {errors[`${section.id}-${field.id}`]}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
          <CardFooter className='flex justify-between'>
            <Button type='button' variant='outline' onClick={handleCancel}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Cancel
            </Button>
            <Button
              type='submit'
              className='bg-green-2010c hover:bg-green-2010c/90 cursor-pointer'
              disabled={isSaving}
            >
              {isSaving ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className='h-4 w-4 mr-2' />
                  Save Updates
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>

      {/* Confirmation Dialog for Significant Changes */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Significant Changes</DialogTitle>
            <DialogDescription>
              You've made the following significant changes to your document:
            </DialogDescription>
          </DialogHeader>
          <div className='py-4'>
            <ul className='list-disc list-inside space-y-2'>
              {significantChanges.map((change, index) => (
                <li key={index} className='text-red-600'>
                  {change}
                </li>
              ))}
            </ul>
            <p className='mt-4'>
              Are you sure you want to proceed with these changes?
            </p>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setShowConfirmDialog(false)}
            >
              Review Again
            </Button>
            <Button
              className='bg-green-2010c hover:bg-green-2010c/90 cursor-pointer'
              onClick={() => {
                setShowConfirmDialog(false);
                handleSave();
              }}
            >
              Confirm Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* No Changes Dialog */}
      <Dialog open={showNoChangesDialog} onOpenChange={setShowNoChangesDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>No Changes Detected</DialogTitle>
            <DialogDescription>
              You haven't made any changes to your document. Would you like to
              mark it as reviewed without changes?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setShowNoChangesDialog(false)}
            >
              Continue Editing
            </Button>
            <Button variant='success' onClick={handleNoChangesConfirm}>
              <Check className='h-4 w-4 mr-2' />
              Mark as Reviewed
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
