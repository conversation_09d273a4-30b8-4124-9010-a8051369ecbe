'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface NavigationLink {
  title: string;
  description: string;
  path: string;
  category:
    | 'public'
    | 'authenticated'
    | 'member'
    | 'admin'
    | 'welon'
    | 'professional';
}

const navigationLinks: NavigationLink[] = [
  // Public Pages
  { title: 'Home', description: 'Landing page', path: '/', category: 'public' },
  {
    title: 'Login',
    description: 'User authentication',
    path: '/login',
    category: 'public',
  },
  {
    title: 'Register',
    description: 'New user registration',
    path: '/register',
    category: 'public',
  },
  {
    title: 'Forgot Password',
    description: 'Password recovery',
    path: '/auth/forgot-password',
    category: 'public',
  },
  {
    title: 'Brand Guidelines',
    description: 'Brand assets and usage',
    path: '/brand',
    category: 'public',
  },

  // Authenticated Pages
  {
    title: 'Dashboard',
    description: 'User dashboard',
    path: '/dashboard',
    category: 'authenticated',
  },
  {
    title: 'Settings',
    description: 'User settings',
    path: '/settings',
    category: 'authenticated',
  },
  {
    title: 'Onboarding',
    description: 'New user onboarding',
    path: '/onboarding',
    category: 'authenticated',
  },

  // Member Pages
  {
    title: 'Interview',
    description: 'Member interview process',
    path: '/member/interview',
    category: 'member',
  },
  {
    title: 'Review',
    description: 'Review interview responses',
    path: '/member/review',
    category: 'member',
  },
  {
    title: 'Documents',
    description: 'Manage estate planning documents',
    path: '/documents',
    category: 'member',
  },
  {
    title: 'Review Documents',
    description: 'Review documents before signing',
    path: '/documents/review',
    category: 'member',
  },
  {
    title: 'Sign Documents',
    description: 'Sign and execute documents',
    path: '/documents/sign',
    category: 'member',
  },
  {
    title: 'Create Documents',
    description: 'Create legal documents',
    path: '/dashboard/member/documents/create',
    category: 'member',
  },
  {
    title: 'Preview Documents',
    description: 'Review documents',
    path: '/dashboard/member/documents/preview',
    category: 'member',
  },
  {
    title: 'Document History',
    description: 'Manage document versions',
    path: '/dashboard/member/documents/history',
    category: 'member',
  },

  // Admin Pages
  {
    title: 'Templates',
    description: 'Template management',
    path: '/admin/templates',
    category: 'admin',
  },
  {
    title: 'Template Editor',
    description: 'Edit templates',
    path: '/admin/templates/edit/1',
    category: 'admin',
  },
  {
    title: 'Template History',
    description: 'Version history',
    path: '/admin/templates/history/1',
    category: 'admin',
  },
  {
    title: 'Template Propagate',
    description: 'Update propagation',
    path: '/admin/templates/propagate',
    category: 'admin',
  },
  {
    title: 'Legal Updates',
    description: 'Legal updates dashboard',
    path: '/admin/legal-updates',
    category: 'admin',
  },
  {
    title: 'Quarterly Review',
    description: 'Quarterly review',
    path: '/admin/quarterly-review',
    category: 'admin',
  },
  {
    title: 'Audit Documents',
    description: 'Audit reports',
    path: '/admin/audit/documents',
    category: 'admin',
  },
  {
    title: 'Emergency',
    description: 'Emergency management',
    path: '/admin/emergency',
    category: 'admin',
  },

  // Welon Trust Pages
  {
    title: 'Emergency Documents',
    description: 'Emergency access',
    path: '/emergency/documents',
    category: 'welon',
  },
  {
    title: 'Submit Evidence',
    description: 'Evidence submission',
    path: '/emergency/submit-evidence',
    category: 'welon',
  },

  // Professional Pages
  {
    title: 'Professional Dashboard',
    description: 'Professional portal',
    path: '/professional/dashboard',
    category: 'professional',
  },
];

export default function NavigationPage() {
  const router = useRouter();

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  const renderLinksByCategory = (
    category: NavigationLink['category'],
    title: string
  ) => {
    const links = navigationLinks.filter(link => link.category === category);

    if (links.length === 0) return null;

    return (
      <Card className='mb-8 border-2 border-soft-blue rounded-xl'>
        <CardHeader className='pb-2'>
          <CardTitle className='text-2xl text-dark-blue'>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {links.map(link => (
              <Button
                key={link.path}
                variant={
                  category === 'public'
                    ? 'default'
                    : category === 'admin'
                      ? 'destructive'
                      : category === 'welon'
                        ? 'outline'
                        : 'secondary'
                }
                className='h-auto flex flex-col items-center justify-center text-center w-full'
                onClick={() => handleNavigate(link.path)}
              >
                <span className='font-semibold text-lg'>{link.title}</span>
                <span className='text-sm mt-1 opacity-90'>
                  {link.description}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='container mx-auto p-6 py-8 bg-warm-gray'>
      <div className='mb-8 text-center'>
        <h1 className='text-3xl md:text-4xl font-bold text-text-black mb-4'>
          Site Navigation
        </h1>
        <p className='text-xl text-text-black max-w-2xl mx-auto'>
          Browse all available pages in the Childfree Legacy application
        </p>
      </div>

      <div className='bg-blue-gray p-4 rounded-xl mb-8 border-l-4 border-dark-blue'>
        <p className='text-lg'>
          Click on any button below to navigate to that page. The buttons are
          color-coded by section.
        </p>
      </div>

      {renderLinksByCategory('public', 'Main Pages')}
      {renderLinksByCategory('authenticated', 'Account Pages')}
      {renderLinksByCategory('member', 'Member Features')}
      {renderLinksByCategory('admin', 'Administrator Tools')}
      {renderLinksByCategory('welon', 'Welon Trust Access')}
      {renderLinksByCategory('professional', 'Professional Services')}

      <div className='mt-10 flex justify-center'>
        <Button
          variant='outline'
          size='lg'
          onClick={() => handleNavigate('/')}
          className='px-8'
        >
          Return to Home Page
        </Button>
      </div>
    </div>
  );
}
