'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Edit, Calendar } from 'lucide-react';
import { useRouter } from 'next/navigation';

export interface LivingDocument {
  id: string;
  title: string;
  type: string;
  description: string;
  lastUpdated: string;
  nextReview: string;
  status: 'current' | 'review-soon' | 'review-needed';
}

interface LivingDocumentCardProps {
  document: LivingDocument;
}

export function LivingDocumentCard({ document }: LivingDocumentCardProps) {
  const router = useRouter();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-green-2010c';
      case 'review-soon':
        return 'bg-amber-400 text-amber-900';
      case 'review-needed':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'current':
        return 'Current';
      case 'review-soon':
        return 'Review Soon';
      case 'review-needed':
        return 'Review Needed';
      default:
        return 'Unknown';
    }
  };

  const handleView = () => {
    router.push(`/dashboard/member/living-documents/${document.id}`);
  };

  const handleEdit = () => {
    router.push(`/dashboard/member/living-documents/update/${document.id}`);
  };

  return (
    <Card className='overflow-hidden h-full flex flex-col'>
      <CardHeader className='pb-2'>
        <div className='flex justify-between items-start'>
          <div>
            <CardTitle>{document.title}</CardTitle>
            <CardDescription>{document.description}</CardDescription>
          </div>
          <Badge className={getStatusColor(document.status)}>
            {getStatusText(document.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className='pb-2 flex-grow'>
        <div className='space-y-2 text-sm text-[var(--custom-gray-medium)]'>
          <div className='flex items-center'>
            <Calendar className='h-4 w-4 mr-2' />
            <span>Last updated: {document.lastUpdated}</span>
          </div>
          <div className='flex items-center'>
            <Calendar className='h-4 w-4 mr-2' />
            <span>Next review: {document.nextReview}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className='pt-2'>
        <div className='flex space-x-2 w-full'>
          <Button
            variant='outline'
            size='sm'
            className='flex-1'
            onClick={handleView}
          >
            <FileText className='h-4 w-4 mr-1' />
            View
          </Button>
          <Button
            variant='default'
            size='sm'
            className='flex-1'
            onClick={handleEdit}
          >
            <Edit className='h-4 w-4 mr-1' />
            Update
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
