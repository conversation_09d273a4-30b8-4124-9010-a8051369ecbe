import { Skeleton } from '@/components/ui/skeleton';

interface HeaderMenuSkeletonProps {
  variant?: 'simple' | 'detailed';
}

export function HeaderMenuSkeleton({
  variant = 'simple',
}: HeaderMenuSkeletonProps) {
  if (variant === 'detailed') {
    return (
      <div className='flex items-center gap-4'>
        <Skeleton className='h-8 w-8 rounded-full' />
        <Skeleton className='h-6 w-6 rounded-full' />
        <Skeleton className='h-8 w-8 rounded-full' />
        <Skeleton className='h-4 w-20' />
        <Skeleton className='h-4 w-4 rounded-full' />
      </div>
    );
  }

  return (
    <div className='flex items-center gap-4'>
      <Skeleton className='h-8 w-8 rounded-full' />
      <Skeleton className='h-4 w-20' />
      <Skeleton className='h-4 w-4 rounded-full' />
    </div>
  );
}
