{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/amplify/*": ["./amplify/*"], "@/components/ui/*": ["./components/ui/*"], "@/context/*": ["./app/context/*"], "@/components/*": ["./app/components/*"], "@workspace/ui/*": ["./components/ui/*"], "@/utils/*": ["./app/utils/*"], "@/lib/*": ["./lib/*"], "@/types/*": ["./app/types/*"], "@/mock/*": ["./app/mock/*"], "@/hooks/*": ["./hooks/*"], "@/app/*": ["./app/*"], "@/*": ["./*"]}, "target": "ES2017", "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}