'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  MessageSquare,
  HelpCircle,
  ArrowRight,
  MoreHorizontal,
  Copy,
  AlertTriangle,
} from 'lucide-react';
// Temporarily removed dropdown menu - will implement later
import {
  InterviewQuestion,
  QuestionType,
  QuestionCategory,
  InterviewQuestionNew,
  QuestionOption,
} from '@/types/interview-builder';
import { deleteQuestion, reorderQuestions } from '@/lib/api/interview-builder';
import { QuestionFormDialog } from './question-form-dialog';

interface QuestionBuilderProps {
  interviewId: string;
  questions: InterviewQuestionNew[];
  onQuestionsUpdated: () => void;
  versionId: string;
  refetch: () => void;
}

// Helper function to convert InterviewQuestionNew to InterviewQuestion
const convertToInterviewQuestion = (
  questionNew: InterviewQuestionNew
): InterviewQuestion => {
  let parsedOptions: QuestionOption[] = [];
  if (questionNew.options && typeof questionNew.options === 'string') {
    try {
      parsedOptions = JSON.parse(questionNew.options);
    } catch (error) {
      console.error('Error parsing question options:', error);
      parsedOptions = [];
    }
  }

  return {
    id: questionNew.id,
    text: questionNew.questionTitle,
    type: questionNew.type as QuestionType,
    category: 'personal' as QuestionCategory, // Default category since it's not in InterviewQuestionNew
    required: false, // Default value since it's not in InterviewQuestionNew
    helpText: questionNew.questionDescription || '',
    placeholder: questionNew.placeholder || '',
    options: parsedOptions,
    validation: [],
    conditionalLogic: [],
    defaultNextQuestionId: '',
    templateMapping: '',
    order: questionNew.order || 1,
    createdAt: new Date().toISOString(), // Default value since it's not in InterviewQuestionNew
    updatedAt: new Date().toISOString(), // Default value since it's not in InterviewQuestionNew
  };
};

export function QuestionBuilder({
  interviewId,
  questions,
  onQuestionsUpdated,
  versionId,
  refetch,
}: QuestionBuilderProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingQuestion, setEditingQuestion] =
    useState<InterviewQuestionNew | null>(null);
  const [deletingQuestionId, setDeletingQuestionId] = useState<string | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);

  const handleCreateQuestion = () => {
    setEditingQuestion(null);
    setShowCreateDialog(true);
  };

  const handleEditQuestion = (question: InterviewQuestionNew) => {
    setEditingQuestion(question);
    setShowCreateDialog(true);
  };

  const handleDeleteQuestion = async (question: InterviewQuestionNew) => {
    if (
      !confirm(
        `Are you sure you want to delete "${question.questionTitle}"? This action cannot be undone.`
      )
    ) {
      return;
    }
    try {
      setDeletingQuestionId(question.id);
      setError(null);
      const success = await deleteQuestion(interviewId, question.id);
      if (success) {
        onQuestionsUpdated();
      }
    } catch (err) {
      setError('Failed to delete question. Please try again.');
      console.error('Error deleting question:', err);
    } finally {
      refetch();
      setDeletingQuestionId(null);
    }
  };

  const handleQuestionSaved = () => {
    setShowCreateDialog(false);
    setEditingQuestion(null);
    onQuestionsUpdated();
  };

  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'text':
      case 'email':
      case 'phone':
        return '📝';
      case 'radio':
        return '⚪';
      case 'select':
        return '📋';
      case 'checkbox':
        return '☑️';
      case 'date':
        return '📅';
      case 'number':
        return '🔢';
      default:
        return '❓';
    }
  };

  const getCategoryBadge = (category: QuestionCategory) => {
    const colors = {
      personal: 'bg-blue-100 text-blue-800',
      financial: 'bg-green-100 text-green-800',
      estate: 'bg-purple-100 text-purple-800',
      emergency: 'bg-red-100 text-red-800',
      medical: 'bg-orange-100 text-orange-800',
    };

    return (
      <Badge variant='secondary' className={colors[category]}>
        {category.charAt(0).toUpperCase() + category.slice(1)}
      </Badge>
    );
  };

  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
            Questions
          </h2>
          <p className='text-[var(--custom-gray-medium)]'>
            Manage interview questions and their flow logic
          </p>
        </div>
        <Button onClick={handleCreateQuestion}>
          <Plus className='mr-2 h-4 w-4' />
          Add Question
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Questions List */}
      {sortedQuestions.length === 0 ? (
        <Card>
          <CardContent className='text-center py-12'>
            <MessageSquare className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
            <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
              No questions yet
            </h3>
            <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
              Get started by adding your first interview question.
            </p>
            <div className='mt-6'>
              <Button onClick={handleCreateQuestion}>
                <Plus className='mr-2 h-4 w-4' />
                Add First Question
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Question Flow</CardTitle>
            <CardDescription>
              Questions are presented to members in the order shown below
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead>Question</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead>Logic</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedQuestions.map((question, index) => (
                  <TableRow key={question.id} className='hover:bg-gray-50'>
                    <TableCell>
                      <GripVertical className='h-4 w-4 text-[var(--custom-gray-medium)] cursor-move' />
                    </TableCell>
                    <TableCell>
                      <Badge variant='outline' className='text-xs'>
                        {question.order}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className='max-w-md'>
                        <div className='font-medium text-[var(--custom-gray-dark)] truncate'>
                          {question.questionTitle}
                        </div>
                        {question.questionDescription && (
                          <div className='text-sm text-[var(--custom-gray-medium)] truncate mt-1 flex items-center'>
                            <HelpCircle className='h-3 w-3 mr-1' />
                            {question.questionDescription}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center'>
                        <span className='mr-2'>
                          {getQuestionTypeIcon(question.type)}
                        </span>
                        <span className='text-sm capitalize'>
                          {question.type}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{getCategoryBadge('personal')}</TableCell>
                    <TableCell>
                      {/*{question.required ? (*/}
                      {/*  <Badge variant='destructive' className='text-xs'>*/}
                      {/*    Required*/}
                      {/*  </Badge>*/}
                      {/*) : (*/}
                      {/*  <Badge variant='secondary' className='text-xs'>*/}
                      {/*    Optional*/}
                      {/*  </Badge>*/}
                      {/*)}*/}
                      <Badge variant='secondary' className='text-xs'>
                        Optional
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {/*{question.conditionalLogic &&*/}
                      {/*question.conditionalLogic.length > 0 ? (*/}
                      {/*  <div className='flex items-center text-sm text-blue-600'>*/}
                      {/*    <ArrowRight className='h-3 w-3 mr-1' />*/}
                      {/*    {question.conditionalLogic.length} rule*/}
                      {/*    {question.conditionalLogic.length > 1 ? 's' : ''}*/}
                      {/*  </div>*/}
                      {/*) : (*/}
                      {/*  <span className='text-sm text-[var(--custom-gray-medium)]'>*/}
                      {/*    None*/}
                      {/*  </span>*/}
                      {/*)}*/}
                      <span className='text-sm text-[var(--custom-gray-medium)]'>
                        None
                      </span>
                    </TableCell>
                    <TableCell className='text-right'>
                      <div className='flex items-center space-x-1'>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => handleEditQuestion(question)}
                          disabled={deletingQuestionId === question.id}
                          title='Edit Question'
                        >
                          <Edit className='h-4 w-4' />
                        </Button>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => {
                            /* TODO: Implement duplicate */
                          }}
                          disabled={deletingQuestionId === question.id}
                          title='Duplicate'
                        >
                          <Copy className='h-4 w-4' />
                        </Button>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => handleDeleteQuestion(question)}
                          disabled={deletingQuestionId === question.id}
                          className='text-red-600 hover:text-red-700'
                          title={
                            deletingQuestionId === question.id
                              ? 'Deleting...'
                              : 'Delete'
                          }
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Question Form Dialog */}
      <QuestionFormDialog
        interviewId={interviewId}
        versionId={versionId}
        question={
          editingQuestion ? convertToInterviewQuestion(editingQuestion) : null
        }
        refetch={refetch}
        isOpen={showCreateDialog}
        onClose={() => {
          setShowCreateDialog(false);
          setEditingQuestion(null);
        }}
        onSave={handleQuestionSaved}
      />
    </div>
  );
}
