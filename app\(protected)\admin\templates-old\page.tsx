'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  PlusCircle,
  FileEdit,
  History,
  Archive,
  AlertTriangle,
  Send,
  Filter,
  Search,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Trash2,
  RefreshCw,
  Download,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination';

// Mock data for templates
const activeTemplates = [
  {
    id: '1',
    state: 'California',
    type: 'Will',
    version: '1.0',
    startDate: '2023-01-01',
    status: 'Active',
    lastUpdated: '2023-06-15',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 245,
  },
  {
    id: '2',
    state: 'California',
    type: 'Trust',
    version: '2.1',
    startDate: '2023-02-15',
    status: 'Active',
    lastUpdated: '2023-07-20',
    updatedBy: '<EMAIL>',
    propagationStatus: 'pending',
    affectedUsers: 189,
  },
  {
    id: '3',
    state: 'New York',
    type: 'Healthcare POA',
    version: '1.2',
    startDate: '2023-03-10',
    status: 'Active',
    lastUpdated: '2023-08-05',
    updatedBy: '<EMAIL>',
    propagationStatus: 'in-progress',
    affectedUsers: 156,
  },
  {
    id: '4',
    state: 'Texas',
    type: 'Financial POA',
    version: '1.0',
    startDate: '2023-04-05',
    status: 'Active',
    lastUpdated: '2023-05-12',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 203,
  },
  {
    id: '5',
    state: 'Florida',
    type: 'Advance Directive',
    version: '1.1',
    startDate: '2023-05-20',
    status: 'Active',
    lastUpdated: '2023-06-30',
    updatedBy: '<EMAIL>',
    propagationStatus: 'failed',
    affectedUsers: 178,
  },
  {
    id: '6',
    state: 'New York',
    type: 'Will',
    version: '1.3',
    startDate: '2023-06-01',
    status: 'Active',
    lastUpdated: '2023-08-15',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 312,
  },
  {
    id: '7',
    state: 'Texas',
    type: 'Trust',
    version: '1.5',
    startDate: '2023-06-15',
    status: 'Active',
    lastUpdated: '2023-09-01',
    updatedBy: '<EMAIL>',
    propagationStatus: 'pending',
    affectedUsers: 267,
  },
  {
    id: '8',
    state: 'Florida',
    type: 'Healthcare POA',
    version: '2.0',
    startDate: '2023-07-01',
    status: 'Active',
    lastUpdated: '2023-09-10',
    updatedBy: '<EMAIL>',
    propagationStatus: 'in-progress',
    affectedUsers: 198,
  },
  {
    id: '9',
    state: 'Illinois',
    type: 'Financial POA',
    version: '1.2',
    startDate: '2023-07-15',
    status: 'Active',
    lastUpdated: '2023-09-20',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 234,
  },
  {
    id: '10',
    state: 'Pennsylvania',
    type: 'Advance Directive',
    version: '1.0',
    startDate: '2023-08-01',
    status: 'Active',
    lastUpdated: '2023-09-25',
    updatedBy: '<EMAIL>',
    propagationStatus: 'failed',
    affectedUsers: 145,
  },
  {
    id: '11',
    state: 'Ohio',
    type: 'Will',
    version: '1.1',
    startDate: '2023-08-15',
    status: 'Active',
    lastUpdated: '2023-10-01',
    updatedBy: '<EMAIL>',
    propagationStatus: 'pending',
    affectedUsers: 289,
  },
  {
    id: '12',
    state: 'Michigan',
    type: 'Trust',
    version: '1.0',
    startDate: '2023-09-01',
    status: 'Active',
    lastUpdated: '2023-10-05',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 176,
  },
  {
    id: '13',
    state: 'Georgia',
    type: 'Healthcare POA',
    version: '1.4',
    startDate: '2023-09-15',
    status: 'Active',
    lastUpdated: '2023-10-10',
    updatedBy: '<EMAIL>',
    propagationStatus: 'in-progress',
    affectedUsers: 223,
  },
  {
    id: '14',
    state: 'North Carolina',
    type: 'Financial POA',
    version: '1.1',
    startDate: '2023-10-01',
    status: 'Active',
    lastUpdated: '2023-10-15',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 201,
  },
  {
    id: '15',
    state: 'Virginia',
    type: 'Advance Directive',
    version: '1.2',
    startDate: '2023-10-15',
    status: 'Active',
    lastUpdated: '2023-10-20',
    updatedBy: '<EMAIL>',
    propagationStatus: 'pending',
    affectedUsers: 167,
  },
  {
    id: '16',
    state: 'Washington',
    type: 'Will',
    version: '1.0',
    startDate: '2023-11-01',
    status: 'Active',
    lastUpdated: '2023-11-05',
    updatedBy: '<EMAIL>',
    propagationStatus: 'failed',
    affectedUsers: 298,
  },
  {
    id: '17',
    state: 'Arizona',
    type: 'Trust',
    version: '1.3',
    startDate: '2023-11-15',
    status: 'Active',
    lastUpdated: '2023-11-20',
    updatedBy: '<EMAIL>',
    propagationStatus: 'completed',
    affectedUsers: 187,
  },
  {
    id: '18',
    state: 'Massachusetts',
    type: 'Healthcare POA',
    version: '1.1',
    startDate: '2023-12-01',
    status: 'Active',
    lastUpdated: '2023-12-05',
    updatedBy: '<EMAIL>',
    propagationStatus: 'in-progress',
    affectedUsers: 256,
  },
];

const archivedTemplates = [
  {
    id: '6',
    state: 'California',
    type: 'Will',
    version: '0.9',
    startDate: '2022-06-10',
    endDate: '2023-01-01',
    status: 'Archived',
  },
  {
    id: '7',
    state: 'New York',
    type: 'Healthcare POA',
    version: '1.1',
    startDate: '2022-09-15',
    endDate: '2023-03-10',
    status: 'Archived',
  },
  {
    id: '8',
    state: 'California',
    type: 'Trust',
    version: '2.0',
    startDate: '2022-11-20',
    endDate: '2023-02-15',
    status: 'Archived',
  },
];

// Mock data for states and document types
const states = [
  'All States',
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
];

const documentTypes = [
  'All Types',
  'Will',
  'Trust',
  'Healthcare POA',
  'Financial POA',
  'Advance Directive',
];

type SortField =
  | 'state'
  | 'type'
  | 'version'
  | 'lastUpdated'
  | 'propagationStatus'
  | 'affectedUsers';
type SortDirection = 'asc' | 'desc' | null;

export default function TemplatesPage() {
  const router = useRouter();
  const [selectedState, setSelectedState] = useState<string>('All States');
  const [selectedType, setSelectedType] = useState<string>('All Types');
  const [activeTab, setActiveTab] = useState<string>('active');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [isAllAcrossPagesSelected, setIsAllAcrossPagesSelected] =
    useState<boolean>(false);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction or reset
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort function
  const sortTemplates = (templates: any[]) => {
    if (!sortField || !sortDirection) return templates;

    return [...templates].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle different data types
      if (sortField === 'lastUpdated') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (sortField === 'affectedUsers') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  // Filter and search templates
  const filterAndSearchTemplates = (templates: any[]) => {
    return templates.filter(template => {
      const matchesState =
        selectedState === 'All States' || template.state === selectedState;
      const matchesType =
        selectedType === 'All Types' || template.type === selectedType;
      const matchesSearch =
        searchQuery === '' ||
        template.state.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.updatedBy?.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesState && matchesType && matchesSearch;
    });
  };

  // Apply filtering, searching, and sorting
  const allFilteredActiveTemplates = sortTemplates(
    filterAndSearchTemplates(activeTemplates)
  );
  const allFilteredArchivedTemplates = sortTemplates(
    filterAndSearchTemplates(archivedTemplates)
  );

  // Pagination logic
  const getTotalPages = (totalItems: number) =>
    Math.ceil(totalItems / itemsPerPage);

  const getPaginatedData = (data: any[], page: number) => {
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  // Get paginated data for current page
  const filteredActiveTemplates = getPaginatedData(
    allFilteredActiveTemplates,
    currentPage
  );
  const filteredArchivedTemplates = getPaginatedData(
    allFilteredArchivedTemplates,
    currentPage
  );

  // Get total pages for current tab
  const totalActivePages = getTotalPages(allFilteredActiveTemplates.length);
  const totalArchivedPages = getTotalPages(allFilteredArchivedTemplates.length);

  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [
    searchQuery,
    selectedState,
    selectedType,
    sortField,
    sortDirection,
    itemsPerPage,
  ]);

  // Reset to page 1 when switching tabs
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeTab]);

  // Clear selections when switching tabs or changing filters
  React.useEffect(() => {
    setSelectedItems(new Set());
    setIsAllSelected(false);
    setIsAllAcrossPagesSelected(false);
  }, [activeTab, searchQuery, selectedState, selectedType, currentPage]);

  // Handle individual item selection
  const handleItemSelect = (itemId: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }
    setSelectedItems(newSelected);

    // Update "select all" state
    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : filteredArchivedTemplates;
    const allCurrentPageSelected = currentPageItems.every(item =>
      newSelected.has(item.id)
    );
    setIsAllSelected(allCurrentPageSelected && currentPageItems.length > 0);
  };

  // Handle select all for current page
  const handleSelectAll = (checked: boolean) => {
    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : filteredArchivedTemplates;
    const newSelected = new Set(selectedItems);

    if (checked) {
      currentPageItems.forEach(item => newSelected.add(item.id));
    } else {
      currentPageItems.forEach(item => newSelected.delete(item.id));
      setIsAllAcrossPagesSelected(false);
    }

    setSelectedItems(newSelected);
    setIsAllSelected(checked);
  };

  // Handle select all across all pages
  const handleSelectAllAcrossPages = () => {
    const allItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates
        : allFilteredArchivedTemplates;
    const newSelected = new Set<string>();
    allItems.forEach(item => newSelected.add(item.id));

    setSelectedItems(newSelected);
    setIsAllSelected(true);
    setIsAllAcrossPagesSelected(true);
  };

  // Bulk action handlers
  const handleBulkDelete = async () => {
    if (selectedItems.size === 0) return;

    if (
      confirm(
        `Are you sure you want to delete ${selectedItems.size} template(s)?`
      )
    ) {
      // In real implementation, this would call an API
      console.log('Deleting templates:', Array.from(selectedItems));
      setSelectedItems(new Set());
      setIsAllSelected(false);
      // Show success toast
    }
  };

  const handleBulkArchive = async () => {
    if (selectedItems.size === 0) return;

    if (
      confirm(
        `Are you sure you want to archive ${selectedItems.size} template(s)?`
      )
    ) {
      // In real implementation, this would call an API
      console.log('Archiving templates:', Array.from(selectedItems));
      setSelectedItems(new Set());
      setIsAllSelected(false);
      // Show success toast
    }
  };

  const handleBulkUpdate = async () => {
    if (selectedItems.size === 0) return;

    if (
      confirm(
        `Are you sure you want to propagate updates for ${selectedItems.size} template(s)?`
      )
    ) {
      // In real implementation, this would call an API
      console.log('Updating templates:', Array.from(selectedItems));
      setSelectedItems(new Set());
      setIsAllSelected(false);
      // Show success toast
    }
  };

  const handleBulkExport = async () => {
    if (selectedItems.size === 0) return;

    // In real implementation, this would generate and download a file
    console.log('Exporting templates:', Array.from(selectedItems));
    // Show success toast
  };

  const handleCreateTemplate = () => {
    router.push('/admin/templates/edit/new');
  };

  const handleEditTemplate = (id: string) => {
    router.push(`/admin/templates/edit/${id}`);
  };

  const handleViewHistory = (id: string) => {
    router.push(`/admin/templates/history/${id}`);
  };

  const handlePropagateUpdate = (id: string) => {
    router.push(`/admin/templates/propagate?templateId=${id}`);
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      completed: 'bg-green-600 text-white border border-green-700',
      pending: 'bg-amber-500 text-white border border-amber-600',
      'in-progress': 'bg-blue-600 text-white border border-blue-700',
      failed: 'bg-red-600 text-white border border-red-700',
    };

    const statusIcons = {
      completed: '✓',
      pending: '⏳',
      'in-progress': '⟳',
      failed: '✗',
    };

    return (
      <span
        className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-semibold ${statusColors[status as keyof typeof statusColors] || 'bg-gray-600 text-white border border-gray-700'}`}
      >
        <span className='text-xs'>
          {statusIcons[status as keyof typeof statusIcons] || '•'}
        </span>
        {status}
      </span>
    );
  };

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return (
        <ArrowUpDown className='h-3 w-3 text-[var(--custom-gray-medium)]' />
      );
    }
    if (sortDirection === 'asc') {
      return <ArrowUp className='h-3 w-3 text-blue-600' />;
    }
    if (sortDirection === 'desc') {
      return <ArrowDown className='h-3 w-3 text-blue-600' />;
    }
    return <ArrowUpDown className='h-3 w-3 text-[var(--custom-gray-medium)]' />;
  };

  // Icon action button component with tooltip
  const IconActionButton = ({
    onClick,
    icon: Icon,
    label,
    variant = 'outline',
    className = '',
  }: {
    onClick: () => void;
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    variant?: 'outline' | 'default' | 'destructive';
    className?: string;
  }) => (
    <Button
      variant={variant}
      size='sm'
      onClick={onClick}
      className={`h-6 w-6 p-0 min-w-0 flex-shrink-0 hover:scale-110 hover:shadow-md transition-all duration-200 ${className}`}
      title={label}
    >
      <Icon className='h-3 w-3' />
      <span className='sr-only'>{label}</span>
    </Button>
  );

  // Sortable table header component
  const SortableTableHead = ({
    field,
    children,
    className = '',
  }: {
    field: SortField;
    children: React.ReactNode;
    className?: string;
  }) => (
    <TableHead
      className={`cursor-pointer hover:bg-gray-50 select-none font-semibold py-4 ${className}`}
      onClick={() => handleSort(field)}
    >
      <div className='flex items-center gap-1'>
        {children}
        {getSortIcon(field)}
      </div>
    </TableHead>
  );

  // Bulk actions bar component
  const BulkActionsBar = () => {
    if (selectedItems.size === 0) return null;

    const currentPageItems =
      activeTab === 'active'
        ? filteredActiveTemplates
        : filteredArchivedTemplates;
    const allItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates
        : allFilteredArchivedTemplates;
    const allCurrentPageSelected = currentPageItems.every(item =>
      selectedItems.has(item.id)
    );
    const someCurrentPageSelected = currentPageItems.some(item =>
      selectedItems.has(item.id)
    );

    return (
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>
        <div className='flex flex-col gap-3'>
          <div className='flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4'>
            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2'>
                <span className='text-sm font-medium text-blue-900'>
                  {selectedItems.size} item{selectedItems.size !== 1 ? 's' : ''}{' '}
                  selected
                  {isAllAcrossPagesSelected &&
                    ` (all ${allItems.length} items)`}
                </span>
                {someCurrentPageSelected &&
                  !allCurrentPageSelected &&
                  !isAllAcrossPagesSelected && (
                    <span className='text-xs text-white bg-blue-600 border border-blue-700 px-2 py-1 rounded-full font-medium'>
                      ⚪ Partial page selection
                    </span>
                  )}
                {allCurrentPageSelected &&
                  currentPageItems.length > 0 &&
                  !isAllAcrossPagesSelected && (
                    <span className='text-xs text-white bg-blue-600 border border-blue-700 px-2 py-1 rounded-full font-medium'>
                      ✓ All on page selected
                    </span>
                  )}
                {isAllAcrossPagesSelected && (
                  <span className='text-xs text-white bg-green-600 border border-green-700 px-2 py-1 rounded-full font-medium'>
                    ✓✓ All pages selected
                  </span>
                )}
              </div>
              <Button
                variant='outline'
                size='sm'
                onClick={() => {
                  setSelectedItems(new Set());
                  setIsAllSelected(false);
                  setIsAllAcrossPagesSelected(false);
                }}
                className='h-8 text-xs'
              >
                Clear selection
              </Button>
            </div>
          </div>

          {/* Select all across pages option */}
          {allCurrentPageSelected &&
            currentPageItems.length > 0 &&
            !isAllAcrossPagesSelected &&
            allItems.length > currentPageItems.length && (
              <div className='flex items-center gap-2 text-sm'>
                <span className='text-blue-700'>
                  All {currentPageItems.length} items on this page are selected.
                </span>
                <Button
                  variant='link'
                  size='sm'
                  onClick={handleSelectAllAcrossPages}
                  className='h-auto p-0 text-blue-600 hover:text-blue-800 underline'
                >
                  Select all {allItems.length} items across all pages
                </Button>
              </div>
            )}
        </div>

        <div className='flex items-center gap-2 flex-wrap'>
          {activeTab === 'active' && (
            <>
              <Button
                variant='outline'
                size='sm'
                onClick={handleBulkUpdate}
                className='h-8 text-xs'
                title='Propagate updates to all selected templates'
              >
                <RefreshCw className='mr-1 h-3 w-3' />
                Propagate Updates
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={handleBulkArchive}
                className='h-8 text-xs'
                title='Archive all selected templates'
              >
                <Archive className='mr-1 h-3 w-3' />
                Archive
              </Button>
            </>
          )}
          <Button
            variant='outline'
            size='sm'
            onClick={handleBulkExport}
            className='h-8 text-xs'
            title='Export selected templates to file'
          >
            <Download className='mr-1 h-3 w-3' />
            Export
          </Button>
          <Button
            variant='destructive'
            size='sm'
            onClick={handleBulkDelete}
            className='h-8 text-xs'
            title='Permanently delete selected templates'
          >
            <Trash2 className='mr-1 h-3 w-3' />
            Delete
          </Button>
        </div>
      </div>
    );
  };

  // Pagination component
  const PaginationComponent = ({
    currentPage,
    totalPages,
    onPageChange,
  }: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
  }) => {
    const totalItems =
      activeTab === 'active'
        ? allFilteredActiveTemplates.length
        : allFilteredArchivedTemplates.length;

    if (totalItems === 0) return null;

    const getVisiblePages = () => {
      const delta = 2;
      const range = [];
      const rangeWithDots = [];

      for (
        let i = Math.max(2, currentPage - delta);
        i <= Math.min(totalPages - 1, currentPage + delta);
        i++
      ) {
        range.push(i);
      }

      if (currentPage - delta > 2) {
        rangeWithDots.push(1, '...');
      } else {
        rangeWithDots.push(1);
      }

      rangeWithDots.push(...range);

      if (currentPage + delta < totalPages - 1) {
        rangeWithDots.push('...', totalPages);
      } else {
        rangeWithDots.push(totalPages);
      }

      return rangeWithDots;
    };

    return (
      <div className='flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mt-6'>
        <div className='flex items-center gap-4'>
          <div className='text-sm text-[var(--custom-gray-medium)]'>
            Showing {(currentPage - 1) * itemsPerPage + 1} to{' '}
            {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems}{' '}
            results
          </div>

          <div className='flex items-center gap-2'>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              Show:
            </span>
            <Select
              value={itemsPerPage.toString()}
              onValueChange={value => setItemsPerPage(Number(value))}
            >
              <SelectTrigger className='w-20 h-8'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='5'>5</SelectItem>
                <SelectItem value='10'>10</SelectItem>
                <SelectItem value='20'>20</SelectItem>
                <SelectItem value='50'>50</SelectItem>
              </SelectContent>
            </Select>
            <span className='text-sm text-[var(--custom-gray-medium)]'>
              per page
            </span>
          </div>
        </div>

        {totalPages > 1 && (
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => onPageChange(Math.max(1, currentPage - 1))}
                  className={
                    currentPage === 1
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>

              {getVisiblePages().map((page, index) => (
                <PaginationItem key={index}>
                  {page === '...' ? (
                    <PaginationEllipsis />
                  ) : (
                    <PaginationLink
                      onClick={() => onPageChange(page as number)}
                      isActive={currentPage === page}
                      className='cursor-pointer'
                    >
                      {page}
                    </PaginationLink>
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    onPageChange(Math.min(totalPages, currentPage + 1))
                  }
                  className={
                    currentPage === totalPages
                      ? 'pointer-events-none opacity-50'
                      : 'cursor-pointer'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    );
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
            Template Management
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            Manage legal document templates for different U.S. states
          </p>
        </div>
        <Button onClick={handleCreateTemplate} variant='default' size='sm'>
          <PlusCircle className='mr-2 h-4 w-4' />
          Create New Template
        </Button>
      </div>

      <Card className='mb-8'>
        <CardHeader>
          <CardTitle>Search & Filter Templates</CardTitle>
          <CardDescription>
            Search by state, document type, or updated by user. Use filters to
            narrow down results.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid  md:grid-cols-3 gap-4'>
            <div>
              <label className='text-sm font-medium mb-2 block'>Search</label>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--custom-gray-medium)]' />
                <Input
                  placeholder='Search by state, type, or user...'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>
            <div>
              <label className='text-sm font-medium mb-2 block'>State</label>
              <Select value={selectedState} onValueChange={setSelectedState}>
                <SelectTrigger>
                  <SelectValue placeholder='Select state' />
                </SelectTrigger>
                <SelectContent>
                  {states.map(state => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className='text-sm font-medium mb-2 block'>
                Document Type
              </label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder='Select document type' />
                </SelectTrigger>
                <SelectContent>
                  {documentTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {(searchQuery ||
            selectedState !== 'All States' ||
            selectedType !== 'All Types') && (
            <div className='mt-4 flex items-center gap-2'>
              <span className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                Active filters:
              </span>
              {searchQuery && (
                <span className='inline-flex items-center px-2.5 py-1 bg-blue-600 text-white rounded-full text-xs font-medium border border-blue-700'>
                  🔍 Search: "{searchQuery}"
                </span>
              )}
              {selectedState !== 'All States' && (
                <span className='inline-flex items-center px-2.5 py-1 bg-green-600 text-white rounded-full text-xs font-medium border border-green-700'>
                  📍 State: {selectedState}
                </span>
              )}
              {selectedType !== 'All Types' && (
                <span className='inline-flex items-center px-2.5 py-1 bg-purple-600 text-white rounded-full text-xs font-medium border border-purple-700'>
                  📄 Type: {selectedType}
                </span>
              )}
              <Button
                variant='outline'
                size='sm'
                onClick={() => {
                  setSearchQuery('');
                  setSelectedState('All States');
                  setSelectedType('All Types');
                }}
                className='h-6 text-xs'
              >
                Clear all
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-2 mb-6'>
          <TabsTrigger value='active'>Active Templates</TabsTrigger>
          <TabsTrigger value='archived'>Archived Templates</TabsTrigger>
        </TabsList>

        <TabsContent value='active'>
          <Card>
            <CardHeader>
              <CardTitle>Active Templates</CardTitle>
              <CardDescription>
                Currently active document templates
                {allFilteredActiveTemplates.length !== activeTemplates.length &&
                  ` (${allFilteredActiveTemplates.length} of ${activeTemplates.length} matching filters)`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkActionsBar />

              {filteredActiveTemplates.length === 0 ? (
                <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                  No active templates found for the selected filters.
                </div>
              ) : (
                <div className='overflow-x-auto'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className='w-12 font-semibold py-4'>
                          <Checkbox
                            checked={isAllSelected}
                            onCheckedChange={handleSelectAll}
                            aria-label='Select all templates'
                          />
                        </TableHead>
                        <SortableTableHead field='state'>
                          State
                        </SortableTableHead>
                        <SortableTableHead field='type'>
                          Document Type
                        </SortableTableHead>
                        <SortableTableHead field='version'>
                          Version
                        </SortableTableHead>
                        <SortableTableHead field='lastUpdated'>
                          Last Updated
                        </SortableTableHead>
                        <TableHead className='font-semibold py-4'>
                          Updated By
                        </TableHead>
                        <SortableTableHead field='propagationStatus'>
                          Propagation Status
                        </SortableTableHead>
                        <SortableTableHead field='affectedUsers'>
                          Affected Users
                        </SortableTableHead>
                        <TableHead className='font-semibold py-4 w-20'>
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredActiveTemplates.map(template => (
                        <TableRow
                          key={template.id}
                          className='border-b border-gray-100 hover:bg-gray-50/50'
                        >
                          <TableCell className='py-4'>
                            <Checkbox
                              checked={selectedItems.has(template.id)}
                              onCheckedChange={checked =>
                                handleItemSelect(
                                  template.id,
                                  checked as boolean
                                )
                              }
                              aria-label={`Select ${template.state} ${template.type} template`}
                            />
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.state}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.type}
                          </TableCell>
                          <TableCell className='py-4'>
                            v{template.version}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.lastUpdated}
                          </TableCell>
                          <TableCell className='py-4 text-sm text-[var(--custom-gray-medium)]'>
                            {template.updatedBy}
                          </TableCell>
                          <TableCell className='py-4'>
                            {getStatusBadge(template.propagationStatus)}
                          </TableCell>
                          <TableCell className='py-4 text-sm'>
                            {template.affectedUsers} users
                          </TableCell>
                          <TableCell className='py-4'>
                            <div className='flex gap-1 w-20'>
                              <IconActionButton
                                onClick={() => handleEditTemplate(template.id)}
                                icon={FileEdit}
                                label='Edit template'
                                variant='outline'
                                className='hover:bg-blue-50 hover:border-blue-300'
                              />
                              <IconActionButton
                                onClick={() => handleViewHistory(template.id)}
                                icon={History}
                                label='View history'
                                variant='outline'
                                className='hover:bg-gray-50 hover:border-gray-300'
                              />
                              <IconActionButton
                                onClick={() =>
                                  handlePropagateUpdate(template.id)
                                }
                                icon={Send}
                                label='Propagate updates'
                                variant='default'
                                className='bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700'
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Pagination for Active Templates */}
              <PaginationComponent
                currentPage={currentPage}
                totalPages={totalActivePages}
                onPageChange={setCurrentPage}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='archived'>
          <Card>
            <CardHeader>
              <CardTitle>Archived Templates</CardTitle>
              <CardDescription>
                Previously active document templates
                {allFilteredArchivedTemplates.length !==
                  archivedTemplates.length &&
                  ` (${allFilteredArchivedTemplates.length} of ${archivedTemplates.length} matching filters)`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkActionsBar />

              {filteredArchivedTemplates.length === 0 ? (
                <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                  No archived templates found for the selected filters.
                </div>
              ) : (
                <div className='overflow-x-auto'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className='w-12 font-semibold py-4'>
                          <Checkbox
                            checked={isAllSelected}
                            onCheckedChange={handleSelectAll}
                            aria-label='Select all templates'
                          />
                        </TableHead>
                        <SortableTableHead field='state'>
                          State
                        </SortableTableHead>
                        <SortableTableHead field='type'>
                          Document Type
                        </SortableTableHead>
                        <SortableTableHead field='version'>
                          Version
                        </SortableTableHead>
                        <TableHead className='font-semibold py-4'>
                          Start Date
                        </TableHead>
                        <TableHead className='font-semibold py-4'>
                          End Date
                        </TableHead>
                        <TableHead className='font-semibold py-4'>
                          Status
                        </TableHead>
                        <TableHead className='font-semibold py-4 w-20'>
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredArchivedTemplates.map(template => (
                        <TableRow
                          key={template.id}
                          className='border-b border-gray-100 hover:bg-gray-50/50'
                        >
                          <TableCell className='py-4'>
                            <Checkbox
                              checked={selectedItems.has(template.id)}
                              onCheckedChange={checked =>
                                handleItemSelect(
                                  template.id,
                                  checked as boolean
                                )
                              }
                              aria-label={`Select ${template.state} ${template.type} template`}
                            />
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.state}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.type}
                          </TableCell>
                          <TableCell className='py-4'>
                            v{template.version}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.startDate}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.endDate}
                          </TableCell>
                          <TableCell className='py-4'>
                            {template.status}
                          </TableCell>
                          <TableCell className='py-4'>
                            <div className='flex gap-1 w-20'>
                              <IconActionButton
                                onClick={() => handleViewHistory(template.id)}
                                icon={History}
                                label='View history'
                                variant='outline'
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Pagination for Archived Templates */}
              <PaginationComponent
                currentPage={currentPage}
                totalPages={totalArchivedPages}
                onPageChange={setCurrentPage}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
