'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  BarChart3,
  CreditCard,
  DollarSign,
  Download,
  Edit,
  Eye,
  FileText,
  Search,
  Users,
  AlertTriangle,
  CheckCircle2,
  RefreshCw,
} from 'lucide-react';
import {
  mockAdminBillingStats,
  mockUserSubscriptions,
  mockTransactions,
} from '@/components/billing/mock-data';
import {
  AdminBillingStats,
  Subscription,
  SubscriptionStatus,
  Transaction,
} from '@/components/billing/types';

export default function AdminBillingPage() {
  const router = useRouter();
  const [stats, setStats] = useState<AdminBillingStats | null>(null);
  const [subscriptions, setSubscriptions] = useState<
    (Subscription & { userName: string; userEmail: string })[]
  >([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedSubscription, setSelectedSubscription] = useState<
    (Subscription & { userName: string; userEmail: string }) | null
  >(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [refundReason, setRefundReason] = useState('');
  const [refundAmount, setRefundAmount] = useState('');

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    setStats(mockAdminBillingStats);
    setSubscriptions(mockUserSubscriptions);
    setTransactions(mockTransactions);
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    // Apply search filter
    const matchesSearch =
      searchQuery === '' ||
      sub.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sub.userEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sub.id.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply status filter
    const matchesStatus = statusFilter === 'all' || sub.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleEditSubscription = (
    subscription: Subscription & { userName: string; userEmail: string }
  ) => {
    setSelectedSubscription(subscription);
    setIsEditModalOpen(true);
  };

  const handleSaveSubscription = () => {
    if (!selectedSubscription) return;

    // In a real implementation, this would call an API to update the subscription
    const updatedSubscriptions = subscriptions.map(sub =>
      sub.id === selectedSubscription.id ? selectedSubscription : sub
    );

    setSubscriptions(updatedSubscriptions);
    setIsEditModalOpen(false);
    setSelectedSubscription(null);
  };

  const handleInitiateRefund = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setRefundAmount(transaction.amount.toString());
    setIsRefundModalOpen(true);
  };

  const handleProcessRefund = () => {
    if (!selectedTransaction) return;

    // In a real implementation, this would call an API to process the refund
    const updatedTransactions = transactions.map(tx =>
      tx.id === selectedTransaction.id
        ? { ...tx, status: 'Refunded' as const }
        : tx
    );

    setTransactions(updatedTransactions);
    setIsRefundModalOpen(false);
    setSelectedTransaction(null);
    setRefundReason('');
    setRefundAmount('');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case 'Active':
        return <Badge className='bg-green-2010c'>Active</Badge>;
      case 'Canceled':
        return <Badge variant='secondary'>Canceled</Badge>;
      case 'Delinquent':
        return <Badge variant='destructive'>Delinquent</Badge>;
      case 'Suspended':
        return <Badge variant='destructive'>Suspended</Badge>;
      case 'In Trust':
        return (
          <Badge
            variant='outline'
            className='text-blue-2157c border-blue-2157c'
          >
            In Trust
          </Badge>
        );
      default:
        return null;
    }
  };

  if (!stats || !subscriptions.length) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='flex items-center justify-center h-64'>
          <p className='text-muted-foreground'>
            Loading billing information...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className='flex justify-between items-center mb-6'>
        <h1 className='text-3xl font-geologica font-semibold'>
          Billing Management
        </h1>
        <Button variant='outline' onClick={() => router.push('/admin')}>
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Admin
        </Button>
      </div>

      <div className='grid  md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Total Active Subscriptions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <Users className='h-5 w-5 text-blue-2157c mr-2' />
              <div className='text-2xl font-bold'>
                {stats.totalActiveSubscriptions}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <DollarSign className='h-5 w-5 text-green-2010c mr-2' />
              <div className='text-2xl font-bold'>
                {formatCurrency(stats.totalRevenue)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Revenue This Month
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <BarChart3 className='h-5 w-5 text-blue-500 mr-2' />
              <div className='text-2xl font-bold'>
                {formatCurrency(stats.revenueThisMonth)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-muted-foreground'>
              Delinquent Accounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='flex items-center'>
              <AlertTriangle className='h-5 w-5 text-destructive mr-2' />
              <div className='text-2xl font-bold'>
                {stats.delinquentAccounts}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='subscriptions' className='mb-8'>
        <TabsList className='mb-4'>
          <TabsTrigger value='subscriptions'>Subscriptions</TabsTrigger>
          <TabsTrigger value='transactions'>Transactions</TabsTrigger>
        </TabsList>

        <TabsContent value='subscriptions'>
          <Card>
            <CardHeader>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>
                View and manage user subscriptions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='flex flex-col md:flex-row gap-4 mb-6'>
                <div className='relative flex-1'>
                  <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='Search by name, email, or ID...'
                    className='pl-8'
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                </div>
                <div className='w-full md:w-64'>
                  <Select
                    value={statusFilter}
                    onValueChange={handleStatusFilterChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Filter by status' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Statuses</SelectItem>
                      <SelectItem value='Active'>Active</SelectItem>
                      <SelectItem value='Delinquent'>Delinquent</SelectItem>
                      <SelectItem value='Suspended'>Suspended</SelectItem>
                      <SelectItem value='Canceled'>Canceled</SelectItem>
                      <SelectItem value='In Trust'>In Trust</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Billing Cycle</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Next Billing</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='text-right'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSubscriptions.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={7}
                          className='text-center py-6 text-muted-foreground'
                        >
                          No subscriptions found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredSubscriptions.map(subscription => (
                        <TableRow key={subscription.id}>
                          <TableCell>
                            <div>
                              <div className='font-medium'>
                                {subscription.userName}
                              </div>
                              <div className='text-sm text-muted-foreground'>
                                {subscription.userEmail}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{subscription.tier}</TableCell>
                          <TableCell>{subscription.billingCycle}</TableCell>
                          <TableCell>
                            {formatCurrency(subscription.amount)}
                          </TableCell>
                          <TableCell>
                            {new Date(
                              subscription.nextBillingDate
                            ).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(subscription.status)}
                          </TableCell>
                          <TableCell className='text-right'>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() =>
                                handleEditSubscription(subscription)
                              }
                              className='h-8'
                            >
                              <Edit className='h-4 w-4' />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='transactions'>
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                View and manage payment transactions.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='text-right'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {transactions.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={6}
                          className='text-center py-6 text-muted-foreground'
                        >
                          No transactions found
                        </TableCell>
                      </TableRow>
                    ) : (
                      transactions.map(transaction => (
                        <TableRow key={transaction.id}>
                          <TableCell>
                            {new Date(transaction.date).toLocaleDateString(
                              'en-US',
                              {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                              }
                            )}
                          </TableCell>
                          <TableCell>
                            User #{transaction.userId.split('_')[1]}
                          </TableCell>
                          <TableCell>{transaction.description}</TableCell>
                          <TableCell>
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                transaction.status === 'Successful'
                                  ? 'bg-green-2010c'
                                  : transaction.status === 'Failed'
                                    ? 'bg-destructive'
                                    : transaction.status === 'Refunded'
                                      ? 'bg-secondary'
                                      : 'bg-blue-2157c'
                              }
                            >
                              {transaction.status}
                            </Badge>
                          </TableCell>
                          <TableCell className='text-right'>
                            <div className='flex justify-end space-x-2'>
                              <Button
                                variant='outline'
                                size='sm'
                                className='h-8 w-8 p-0'
                              >
                                <Eye className='h-4 w-4' />
                              </Button>
                              {transaction.status === 'Successful' && (
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    handleInitiateRefund(transaction)
                                  }
                                  className='h-8 w-8 p-0'
                                >
                                  <RefreshCw className='h-4 w-4' />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Subscription Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className='sm:max-w-[500px]'>
          <DialogHeader>
            <DialogTitle>Edit Subscription</DialogTitle>
            <DialogDescription>
              Update subscription details for {selectedSubscription?.userName}.
            </DialogDescription>
          </DialogHeader>

          {selectedSubscription && (
            <div className='space-y-4 py-4'>
              <div className='space-y-2'>
                <Label htmlFor='status'>Subscription Status</Label>
                <Select
                  value={selectedSubscription.status}
                  onValueChange={value =>
                    setSelectedSubscription({
                      ...selectedSubscription,
                      status: value as SubscriptionStatus,
                    })
                  }
                >
                  <SelectTrigger id='status'>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Active'>Active</SelectItem>
                    <SelectItem value='Delinquent'>Delinquent</SelectItem>
                    <SelectItem value='Suspended'>Suspended</SelectItem>
                    <SelectItem value='Canceled'>Canceled</SelectItem>
                    <SelectItem value='In Trust'>In Trust</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='tier'>Subscription Tier</Label>
                <Select
                  value={selectedSubscription.tier}
                  onValueChange={value =>
                    setSelectedSubscription({
                      ...selectedSubscription,
                      tier: value as 'Basic' | 'Enterprise',
                    })
                  }
                >
                  <SelectTrigger id='tier'>
                    <SelectValue placeholder='Select tier' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Basic'>Basic</SelectItem>
                    <SelectItem value='Enterprise'>Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='billingCycle'>Billing Cycle</Label>
                <Select
                  value={selectedSubscription.billingCycle}
                  onValueChange={value =>
                    setSelectedSubscription({
                      ...selectedSubscription,
                      billingCycle: value as 'Monthly' | 'Annual',
                    })
                  }
                >
                  <SelectTrigger id='billingCycle'>
                    <SelectValue placeholder='Select billing cycle' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Monthly'>Monthly</SelectItem>
                    <SelectItem value='Annual'>Annual</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='amount'>Amount</Label>
                <Input
                  id='amount'
                  type='number'
                  step='0.01'
                  value={selectedSubscription.amount}
                  onChange={e =>
                    setSelectedSubscription({
                      ...selectedSubscription,
                      amount: parseFloat(e.target.value),
                    })
                  }
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='nextBillingDate'>Next Billing Date</Label>
                <Input
                  id='nextBillingDate'
                  type='date'
                  value={selectedSubscription.nextBillingDate}
                  onChange={e =>
                    setSelectedSubscription({
                      ...selectedSubscription,
                      nextBillingDate: e.target.value,
                    })
                  }
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant='outline' onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveSubscription}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Refund Modal */}
      <Dialog open={isRefundModalOpen} onOpenChange={setIsRefundModalOpen}>
        <DialogContent className='sm:max-w-[500px]'>
          <DialogHeader>
            <DialogTitle>Process Refund</DialogTitle>
            <DialogDescription>
              Issue a refund for transaction {selectedTransaction?.id}.
            </DialogDescription>
          </DialogHeader>

          {selectedTransaction && (
            <div className='space-y-4 py-4'>
              <div className='space-y-2'>
                <Label htmlFor='refundAmount'>Refund Amount</Label>
                <Input
                  id='refundAmount'
                  type='number'
                  step='0.01'
                  value={refundAmount}
                  onChange={e => setRefundAmount(e.target.value)}
                  max={selectedTransaction.amount}
                />
                <p className='text-xs text-muted-foreground'>
                  Maximum refund amount:{' '}
                  {formatCurrency(selectedTransaction.amount)}
                </p>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='refundReason'>Reason for Refund</Label>
                <Textarea
                  id='refundReason'
                  value={refundReason}
                  onChange={e => setRefundReason(e.target.value)}
                  placeholder='Enter reason for refund'
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => setIsRefundModalOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleProcessRefund}>Process Refund</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
