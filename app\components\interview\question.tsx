'use client';

import React, { useState, useEffect } from 'react';
import { Question, useInterview } from './interview-context';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

interface QuestionTooltipProps {
  content: string;
}

const QuestionTooltip: React.FC<QuestionTooltipProps> = ({ content }) => {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className='relative inline-block ml-2'>
      <button
        type='button'
        className='text-blue-500 rounded-full w-5 h-5 flex items-center justify-center border border-blue-500 text-xs hover:bg-blue-50'
        onClick={() => setIsVisible(!isVisible)}
        aria-label='Show information'
      >
        i
      </button>
      {isVisible && (
        <div className='absolute z-10 w-64 p-3 mt-2 bg-background rounded-md shadow-lg border border-gray-200 text-sm'>
          {content}
          <button
            type='button'
            className='absolute top-1 right-1 text-[var(--custom-gray-medium)] hover:text-[var(--custom-gray-dark)] h-5 w-5'
            onClick={() => setIsVisible(false)}
            aria-label='Close tooltip'
          >
            ✕
          </button>
        </div>
      )}
    </div>
  );
};

interface QuestionInputProps {
  question: Question;
  value: any;
  onChange: (value: any) => void;
  isSubmitting?: boolean;
}

const QuestionInput: React.FC<QuestionInputProps> = ({
  question,
  value,
  onChange,
  isSubmitting = false,
}) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Parse options if they exist
  const options = (() => {
    if (!question.options) return [];

    if (typeof question.options === 'string') {
      try {
        return JSON.parse(question.options);
      } catch (e) {
        console.error('Error parsing options:', e);
        return [];
      }
    }

    // If options is already an object/array, return it directly
    if (typeof question.options === 'object') {
      return question.options;
    }

    return [];
  })();

  // Initialize checkbox selections
  useEffect(() => {
    if (question.type === 'checkbox' && Array.isArray(value)) {
      setSelectedOptions(value);
    }
  }, [value, question.type]);

  // Handle checkbox changes
  const handleCheckboxChange = (optionValue: string) => {
    setSelectedOptions(prev => {
      const isSelected = prev.includes(optionValue);
      let newSelected: string[];

      if (isSelected) {
        newSelected = prev.filter(item => item !== optionValue);
      } else {
        newSelected = [...prev, optionValue];
      }

      onChange(newSelected);
      return newSelected;
    });
  };

  switch (question.type) {
    case 'text':
      return (
        <Input
          type='text'
          value={value || ''}
          onChange={e => onChange(e.target.value)}
          placeholder={`Enter your answer`}
          className='w-full text-lg'
          disabled={isSubmitting}
        />
      );

    case 'textarea':
      return (
        <Textarea
          value={value || ''}
          onChange={e => onChange(e.target.value)}
          placeholder='Type your answer here'
          rows={4}
          className='w-full text-lg'
          disabled={isSubmitting}
        />
      );

    case 'date':
      return (
        <Input
          type='date'
          value={value || ''}
          onChange={e => onChange(e.target.value)}
          className='w-full text-lg'
          disabled={isSubmitting}
        />
      );

    case 'radio':
      return (
        <RadioGroup
          value={value || ''}
          onValueChange={onChange}
          className='space-y-3'
          disabled={isSubmitting}
        >
          {options.map((option: any) => {
            const optionValue =
              typeof option === 'string' ? option : option.value;
            const optionLabel =
              typeof option === 'string' ? option : option.label;

            return (
              <div
                key={optionValue}
                className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${
                  value === optionValue
                    ? 'bg-accent border-primary'
                    : 'hover:bg-accent/50'
                }`}
              >
                <RadioGroupItem
                  value={optionValue}
                  id={`${question.id}-${optionValue}`}
                />
                <Label
                  htmlFor={`${question.id}-${optionValue}`}
                  className='flex-grow cursor-pointer'
                >
                  {optionLabel}
                </Label>
              </div>
            );
          })}
        </RadioGroup>
      );

    case 'select':
      return (
        <Select
          value={value || ''}
          onValueChange={onChange}
          disabled={isSubmitting}
        >
          <SelectTrigger>
            <SelectValue placeholder='Select an option' />
          </SelectTrigger>
          <SelectContent>
            {options.map((option: any) => {
              const optionValue =
                typeof option === 'string' ? option : option.value;
              const optionLabel =
                typeof option === 'string' ? option : option.label;

              return (
                <SelectItem key={optionValue} value={optionValue}>
                  {optionLabel}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      return (
        <div className='space-y-3'>
          {options.map((option: any) => {
            const optionValue =
              typeof option === 'string' ? option : option.value;
            const optionLabel =
              typeof option === 'string' ? option : option.label;

            return (
              <div
                key={optionValue}
                className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${
                  selectedOptions.includes(optionValue)
                    ? 'bg-accent border-primary'
                    : 'hover:bg-accent/50'
                }`}
              >
                <Checkbox
                  id={`${question.id}-${optionValue}`}
                  checked={selectedOptions.includes(optionValue)}
                  onCheckedChange={() => handleCheckboxChange(optionValue)}
                  disabled={isSubmitting}
                />
                <Label
                  htmlFor={`${question.id}-${optionValue}`}
                  className='flex-grow cursor-pointer'
                >
                  {optionLabel}
                </Label>
              </div>
            );
          })}
        </div>
      );

    default:
      return <div>Unsupported question type: {question.type}</div>;
  }
};

export const QuestionCard: React.FC = () => {
  const {
    questions,
    responses,
    currentQuestionIndex,
    userProgress,
    isLoading,
    setResponse,
    goToNextQuestion,
    goToPreviousQuestion,
    isComplete,
  } = useInterview();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentAnswer, setCurrentAnswer] = useState<any>('');

  // Get current question (needed for useEffect)
  const currentQuestion = questions[currentQuestionIndex];

  // Get saved answer from user progress
  const savedAnswer =
    userProgress?.answers &&
    typeof userProgress.answers === 'object' &&
    currentQuestion
      ? (userProgress.answers as Record<string, any>)[currentQuestion.id]
      : undefined;

  // Initialize current answer when question changes - MUST be before any conditional returns
  useEffect(() => {
    if (currentQuestion) {
      const initialAnswer =
        responses[currentQuestion.id]?.value || savedAnswer || '';
      setCurrentAnswer(initialAnswer);
    }
  }, [currentQuestion?.id, responses, savedAnswer]);

  console.log('!!! QUESTIONS', questions);
  console.log('!!! CURRENT INDEX', currentQuestionIndex);
  console.log('!!! USER PROGRESS', userProgress);

  if (isLoading) {
    return (
      <div className='bg-background rounded-lg shadow-md p-6 max-w-2xl mx-auto'>
        <div className='text-center'>Loading questions...</div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className='bg-background rounded-lg shadow-md p-6 max-w-2xl mx-auto'>
        <div className='text-center'>No questions available</div>
      </div>
    );
  }

  if (isComplete) {
    return (
      <div className='bg-background rounded-lg shadow-md p-6 max-w-2xl mx-auto'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold text-green-600 mb-4'>
            Interview Complete!
          </h2>
          <p className='text-[var(--custom-gray-medium)]'>
            Thank you for completing the interview.
          </p>
        </div>
      </div>
    );
  }

  if (!currentQuestion) {
    return (
      <div className='bg-background rounded-lg shadow-md p-6 max-w-2xl mx-auto'>
        <div className='text-center'>Question not found</div>
      </div>
    );
  }

  // Handle form field changes (no database save)
  const handleChange = (value: any) => {
    setCurrentAnswer(value);
  };

  // Handle next button click with database save
  const handleNext = async () => {
    setIsSubmitting(true);
    try {
      // Save current answer to database
      await setResponse(currentQuestion.id, currentAnswer);
      // Navigate to next question
      goToNextQuestion();
    } catch (error) {
      console.error('Error saving response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className='bg-background rounded-lg shadow-md p-6 max-w-2xl mx-auto'>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-2'>
          <span className='text-sm text-[var(--custom-gray-medium)]'>
            Question {currentQuestionIndex + 1} of {questions.length}
          </span>
        </div>

        <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
          {currentQuestion.questionTitle}
          {currentQuestion.questionDescription && (
            <QuestionTooltip content={currentQuestion.questionDescription} />
          )}
        </h2>

        {currentQuestion.questionDescription && (
          <p className='text-[var(--custom-gray-medium)] mt-2'>
            {currentQuestion.questionDescription}
          </p>
        )}
      </div>

      <div className='mb-8'>
        <QuestionInput
          question={currentQuestion}
          value={currentAnswer}
          onChange={handleChange}
          isSubmitting={isSubmitting}
        />
      </div>

      <div className='flex justify-between'>
        <Button
          variant='outline'
          size='default'
          onClick={goToPreviousQuestion}
          disabled={currentQuestionIndex === 0 || isSubmitting}
        >
          Back
        </Button>
        <Button
          onClick={handleNext}
          variant='default'
          size='default'
          disabled={isSubmitting || !currentAnswer}
        >
          {currentQuestionIndex === questions.length - 1 ? 'Finish' : 'Next'}
        </Button>
      </div>
    </div>
  );
};
