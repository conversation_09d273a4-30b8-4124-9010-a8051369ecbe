// Types for Joint Account Management & Shared Asset Planning

// Link invitation types
export interface LinkInvitation {
  id: string;
  inviterUserId: string;
  inviterName: string;
  inviterEmail: string;
  inviteeEmail: string;
  token: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: string;
  expiresAt: string;
  sharedProfileFields?: SharedProfileFields;
}

// Shared profile fields that can be copied during invitation
export interface SharedProfileFields {
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  pets?: string[];
  householdDetails?: string;
  emergencyContacts?: EmergencyContact[];
}

// Emergency contact for shared profiles
export interface EmergencyContact {
  id: string;
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

// Account link types
export interface AccountLink {
  id: string;
  member1Id: string;
  member2Id: string;
  member1Name: string;
  member2Name: string;
  member1Email: string;
  member2Email: string;
  status: 'active' | 'pending' | 'revoked';
  linkType: 'spouse' | 'partner' | 'family' | 'friend' | 'other';
  permissions: LinkPermission[];
  createdAt: string;
  updatedAt: string;
  invitationToken?: string;
  tokenExpiry?: string;
}

// Link permissions
export type LinkPermission =
  | 'view_shared_documents'
  | 'edit_shared_documents'
  | 'create_shared_documents'
  | 'approve_shared_documents'
  | 'view_emergency_contacts'
  | 'edit_emergency_contacts'
  | 'emergency_access'
  | 'view_billing'
  | 'edit_billing';

// Shared document types
export type SharedDocumentType =
  | 'joint_trust'
  | 'joint_will'
  | 'shared_property'
  | 'joint_power_of_attorney'
  | 'joint_healthcare_directive'
  | 'shared_financial_account';

// Shared document status
export type SharedDocumentStatus =
  | 'draft'
  | 'review'
  | 'approved'
  | 'executed'
  | 'archived';

// Shared document interface
export interface SharedDocument {
  id: string;
  title: string;
  description: string;
  type: SharedDocumentType;
  status: SharedDocumentStatus;
  createdAt: string;
  updatedAt: string;
  lastModifiedBy: string;

  // Co-ownership information
  coOwners: DocumentCoOwner[];
  linkedAccountIds: string[];

  // Document content (varies by type)
  content: SharedDocumentContent;

  // Review and approval tracking
  reviewHistory: DocumentReview[];
  approvals: DocumentApproval[];

  // Version control
  version: number;
  previousVersions?: string[];

  // Legal and compliance
  notarized?: boolean;
  notaryInfo?: NotaryInfo;
  legalReviewRequired?: boolean;
  legalReviewCompleted?: boolean;

  // Metadata
  tags?: string[];
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  nextReviewDate?: string;
}

// Document co-owner
export interface DocumentCoOwner {
  userId: string;
  name: string;
  email: string;
  role: 'creator' | 'co_owner' | 'reviewer';
  permissions: DocumentPermission[];
  hasReviewed: boolean;
  hasApproved: boolean;
  reviewedAt?: string;
  approvedAt?: string;
  comments?: string;
}

// Document permissions
export type DocumentPermission =
  | 'view'
  | 'edit'
  | 'comment'
  | 'approve'
  | 'execute'
  | 'share'
  | 'delete';

// Document review
export interface DocumentReview {
  id: string;
  documentId: string;
  reviewerId: string;
  reviewerName: string;
  reviewType: 'content' | 'legal' | 'final';
  status: 'pending' | 'completed' | 'rejected';
  comments: string;
  changes?: DocumentChange[];
  createdAt: string;
  completedAt?: string;
}

// Document changes
export interface DocumentChange {
  field: string;
  oldValue: any;
  newValue: any;
  reason?: string;
  timestamp: string;
}

// Document approval
export interface DocumentApproval {
  id: string;
  documentId: string;
  approverId: string;
  approverName: string;
  status: 'pending' | 'approved' | 'rejected';
  comments?: string;
  digitalSignature?: string;
  approvedAt?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Notary information
export interface NotaryInfo {
  notaryId: string;
  notaryName: string;
  notaryLicense: string;
  notaryState: string;
  notarizedAt: string;
  notarySignature: string;
  notarySeal: string;
}

// Shared document content (varies by type)
export interface SharedDocumentContent {
  // Joint Trust specific
  trustName?: string;
  trustees?: TrustParty[];
  beneficiaries?: TrustParty[];
  assets?: TrustAsset[];
  distributions?: TrustDistribution[];

  // Shared Property specific
  propertyAddress?: string;
  propertyType?:
    | 'primary_residence'
    | 'vacation_home'
    | 'investment'
    | 'commercial'
    | 'land';
  propertyValue?: number;
  ownershipPercentages?: { [userId: string]: number };
  mortgageInfo?: MortgageInfo;

  // Joint Will specific
  executor?: string;
  alternateExecutor?: string;
  guardians?: Guardian[];
  bequests?: Bequest[];

  // Joint Power of Attorney specific
  agents?: PowerOfAttorneyAgent[];
  powers?: PowerType[];
  limitations?: string[];
  effectiveDate?: string;
  expirationDate?: string;

  // Healthcare Directive specific
  healthcareAgents?: HealthcareAgent[];
  medicalPreferences?: MedicalPreference[];
  endOfLifeDirectives?: EndOfLifeDirective[];

  // General fields
  customClauses?: CustomClause[];
  attachments?: DocumentAttachment[];
  legalDisclosures?: string[];
}

// Supporting interfaces
export interface TrustParty {
  id: string;
  name: string;
  relationship: string;
  address: string;
  phone?: string;
  email?: string;
  percentage?: number;
}

export interface TrustAsset {
  id: string;
  type:
    | 'real_estate'
    | 'financial_account'
    | 'investment'
    | 'personal_property'
    | 'business_interest';
  description: string;
  estimatedValue?: number;
  accountNumber?: string;
  institution?: string;
}

export interface TrustDistribution {
  id: string;
  beneficiaryId: string;
  assetId?: string;
  percentage?: number;
  amount?: number;
  conditions?: string[];
  timing: 'immediate' | 'age_based' | 'event_based' | 'discretionary';
}

export interface MortgageInfo {
  lender: string;
  accountNumber: string;
  balance: number;
  monthlyPayment: number;
  interestRate: number;
  maturityDate: string;
}

export interface Guardian {
  id: string;
  name: string;
  relationship: string;
  address: string;
  phone: string;
  email?: string;
  isPrimary: boolean;
}

export interface Bequest {
  id: string;
  beneficiaryName: string;
  relationship: string;
  description: string;
  type: 'specific' | 'percentage' | 'residual';
  value?: number;
  percentage?: number;
  conditions?: string[];
}

export interface PowerOfAttorneyAgent {
  id: string;
  name: string;
  relationship: string;
  address: string;
  phone: string;
  email?: string;
  isPrimary: boolean;
  limitations?: string[];
}

export type PowerType =
  | 'financial'
  | 'real_estate'
  | 'business'
  | 'legal'
  | 'tax'
  | 'insurance'
  | 'government_benefits'
  | 'healthcare_decisions'
  | 'all_powers';

export interface HealthcareAgent {
  id: string;
  name: string;
  relationship: string;
  address: string;
  phone: string;
  email?: string;
  isPrimary: boolean;
  canMakeFinancialDecisions: boolean;
}

export interface MedicalPreference {
  id: string;
  category:
    | 'life_support'
    | 'pain_management'
    | 'organ_donation'
    | 'autopsy'
    | 'burial_cremation';
  preference: string;
  conditions?: string[];
  additionalInstructions?: string;
}

export interface EndOfLifeDirective {
  id: string;
  scenario: string;
  directive: string;
  conditions: string[];
}

export interface CustomClause {
  id: string;
  title: string;
  content: string;
  category?: string;
  isRequired: boolean;
}

export interface DocumentAttachment {
  id: string;
  filename: string;
  fileType: string;
  fileSize: number;
  uploadedAt: string;
  uploadedBy: string;
  description?: string;
  url: string;
}

// API request/response types
export interface CreateLinkInvitationRequest {
  inviteeEmail: string;
  linkType: AccountLink['linkType'];
  permissions: LinkPermission[];
  includeSharedFields: boolean;
  message?: string;
}

export interface AcceptLinkInvitationRequest {
  token: string;
  accept: boolean;
  profileData?: {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    phone?: string;
    // Shared fields (pre-filled but editable)
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    pets?: string;
    householdDetails?: string;
  };
}

export interface CreateSharedDocumentRequest {
  type: SharedDocumentType;
  title: string;
  description: string;
  linkedAccountIds: string[];
  content: Partial<SharedDocumentContent>;
  permissions?: { [userId: string]: DocumentPermission[] };
}

export interface UpdateSharedDocumentRequest {
  documentId: string;
  content: Partial<SharedDocumentContent>;
  comments?: string;
  requestReview?: boolean;
}

export interface ReviewSharedDocumentRequest {
  documentId: string;
  reviewType: DocumentReview['reviewType'];
  status: 'completed' | 'rejected';
  comments: string;
  changes?: DocumentChange[];
}

export interface ApproveSharedDocumentRequest {
  documentId: string;
  approve: boolean;
  comments?: string;
  digitalSignature?: string;
}

// Utility types
export type SharedDocumentSummary = Pick<
  SharedDocument,
  'id' | 'title' | 'type' | 'status' | 'createdAt' | 'updatedAt' | 'coOwners'
>;

export type LinkInvitationSummary = Pick<
  LinkInvitation,
  'id' | 'inviterName' | 'inviteeEmail' | 'status' | 'createdAt' | 'expiresAt'
>;

export type AccountLinkSummary = Pick<
  AccountLink,
  'id' | 'member1Name' | 'member2Name' | 'status' | 'linkType' | 'createdAt'
>;
