# 2 \- User Registration and Onboarding

# **Document 2 \- Functional Specification: User Registration and Onboarding**

## **Overview**

The User Registration and Onboarding process enables secure, compliant, and role-specific account creation and setup for three primary user types: Members, Administrators, and Welon Trust members, with a future phase for Professionals (not in MVP). Leveraging AWS Amplify, this specification outlines registration workflows—self-registration for Members, admin-initiated registration for Administrators, and invitation-based registration for Welon Trust and Professional users—alongside onboarding steps to guide users through initial account configuration. It emphasizes security, accessibility for the 65+ demographic, and a seamless experience to foster trust in the estate planning platform. Members can opt into optional services during onboarding, and integration with marketing tools tracks user acquisition channels.

---

## **1\. User Registration**

The registration process is tailored by role to ensure proper verification and access control.

### **Key Requirements**

#### **User Roles**

- **Members**: End users who self-register to manage estate plans.
- **Administrators**: System managers added by existing Administrators with internal approval.
- **Welon Trust**: Trust administrators invited by Administrators to execute estate plans.
- **Professionals** (Future Phase \- Not MVP): Authorized individuals invited by Members or Administrators, requiring credential verification.

#### **Security Measures**

- Email verification for all users via **AWS Simple Email Service (SES)**.
- Role-based access control (RBAC) enforced using **AWS Cognito User Groups** from account creation.

#### **Data Collection**

- **Members**: Name, email, password, phone (optional), eligibility questionnaire responses, optional service selections.
- **Administrators**: Email, internal ID, role assignment.
- **Welon Trust**: Email, professional credentials, affiliation, invitation code.
- **Professionals**: Email, affiliation, invitation code.

#### **Optional Services**

- Members can opt into services (e.g., quarterly legal update newsletters) during registration or onboarding.

#### **Marketing Integration**

- Optionally sync with CRM systems (e.g., Salesforce, HubSpot) via **AWS Lambda** to track acquisition channels (e.g., referral links, campaigns).

#### **Accessibility**

- Simplified forms with large text, clear instructions, and optional voice guidance for the 65+ demographic.
- Adhere to **WCAG 2.1 Level AA** with keyboard navigation and ARIA labels.

### **Registration Flows by Role**

#### **1.1 Member Registration**

- **User Flow**:
  - User navigates to /register.
  - Enters name, email, password, and optional phone using **Amplify Authenticator UI**.
  - Completes eligibility questionnaire (e.g., "Are you childfree?") via a custom React form.
  - **Ineligibility Handling**: If ineligible, display: "We’re sorry, you don’t meet our criteria. Here’s why: \[reason\]. Consider these alternatives: \[links\]."
  - **Optional Services**: Select services (e.g., newsletters) via checkboxes.
  - System sends verification email via **AWS SES**.
  - User verifies email, activating the account and redirecting to /member/onboarding.
  - **Marketing Integration**: Track acquisition via URL parameters or cookies, storing in **DynamoDB** or syncing to CRM via **Lambda**.
- **Edge Cases**:
  - Ineligible Member: Show rejection message with resources.
  - Unverified Email: Block login until verified.
  - Optional Services: Allow opt-in/opt-out without impacting registration.

#### **1.2 Administrator Registration**

- **User Flow**:
  - Existing Administrator navigates to /admin/users.
  - Selects "Add Administrator," entering email and role.
  - System sends invitation email with setup link via **AWS SES**.
  - New Administrator sets password and confirms account.
  - Redirects to /admin/dashboard upon activation.
- **Edge Cases**:
  - Expired Invitation: Display "This invitation has expired. Contact your administrator."
  - Duplicate Email: Prevent addition, showing "This email is already in use."

#### **1.3 Welon Trust Registration**

- **User Flow**:
  - Administrator invites via /settings/invite.
  - Invitee receives email with invitation code and setup link via **AWS SES**.
  - Invitee enters professional details and sets password.
  - System verifies credentials (manual or automated via **Lambda**).
  - Upon approval, activates account and redirects to /welon/onboarding.
- **Edge Cases**:
  - Invalid Credentials: Notify user to resubmit.
  - Expired Invitation: Prompt to request a new invitation.

#### **1.4 Professional Registration (Future Phase)**

- **User Flow**:
  - Administrator or Member invites via /settings/invite.
  - Invitee receives email with invitation code and setup link via **AWS SES**.
  - Invitee enters professional details and sets password.
  - System verifies credentials.
  - Upon approval, activates account and redirects to /professional/onboarding.
- **Edge Cases**:
  - Invalid Credentials: Notify user to resubmit.
  - Expired Invitation: Prompt to request a new invitation.

### **Compliance Considerations**

- **Data Privacy**: Encrypt data with **AWS KMS** and TLS per HIPAA and SOC2.
- **Audit Trails**: Log actions in **CloudWatch**.
- **User Consent**: Require explicit consent for data collection and optional services.

### **UI Components (Member Registration Example)**

| Element                    | Description                                    |
| -------------------------- | ---------------------------------------------- |
| Name Field                 | Required input for full name                   |
| Email Field                | Required, validated email format               |
| Password Field             | Required, with strength indicator              |
| Phone Field                | Optional, for SMS notifications                |
| Questionnaire Form         | Multi-step eligibility questions               |
| Optional Services Checkbox | Select additional features (e.g., newsletters) |
| "Register" Button          | Submits registration data                      |
| Error Message              | Shows validation or ineligibility errors       |
| Voice Guidance Toggle      | Optional voice narration for instructions      |

---

## **2\. Onboarding**

The onboarding process guides users through initial setup, ensuring platform understanding and role-specific task completion, with options to confirm optional services.

### **Key Requirements**

#### **Role-Specific Onboarding**

- **Members**: Set up profile, add emergency contacts, explore educational content, confirm optional services.
- **Administrators**: Configure security settings, review tools.
- **Welon Trust**: Link to members, set preferences, access data.
- **Professionals**: Link to members, set preferences, access reporting.

#### **Educational Content**

- Short videos or tooltips (e.g., "How to Plan Your Legacy") with accessibility features (large text, high contrast, screen-reader support).

#### **Progress Tracking**

- Visual checklist or progress bar.

#### **Optional Services**

- Members can review and confirm optional service selections.

### **Onboarding Flows by Role**

#### **2.1 Member Onboarding**

- **User Flow**:
  - User lands on /member/onboarding.
  - Displays welcome message and checklist:
    - Complete Profile (e.g., address, preferences)
    - Watch "Introduction to Estate Planning" Video
    - Confirm Optional Services
  - User completes steps, tracked visually.
  - Review and adjust optional services.
  - Redirects to /member/dashboard with "Setup Complete\!"
- **Edge Cases**:
  - Skipped Steps: Allow skipping non-essential steps but require profile and service confirmation.
  - Incomplete Onboarding: Send reminders via **SES**.
  - Optional Services: Allow post-onboarding changes via settings.

#### **2.2 Administrator Onboarding**

- **User Flow**:
  - User lands on /admin/onboarding.
  - Prompts to:
    - Enable MFA (mandatory)
    - Review management tools
    - Set notification preferences
  - Redirects to /admin/dashboard.
- **Edge Cases**:
  - MFA Declined: Block progression until enabled.

#### **2.3 Welon Trust Onboarding**

- **User Flow**:
  - User lands on /welon/onboarding.
  - Prompts to:
    - Link to member(s)
    - Set preferences
    - Review "How to Access Member Documents" guide
  - Redirects to /welon/dashboard.
- **Edge Cases**:
  - Unlinked Member: Prompt to request a new invitation.

#### **2.4 Professional Onboarding (Future Phase)**

- **User Flow**:
  - User lands on /professional/onboarding.
  - Prompts to:
    - Link to member(s)
    - Set preferences
  - Redirects to /professional/dashboard.
- **Edge Cases**:
  - Unlinked Member: Prompt to request a new invitation.

### **Compliance Considerations**

- **Data Accuracy**: Require profile confirmation.
- **Educational Consent**: Log engagement for SOC2.
- **User Consent**: Obtain explicit consent for data and services.

### **UI Components (Member Onboarding Example)**

| Element                   | Description                      |
| ------------------------- | -------------------------------- |
| Welcome Message           | Personalized greeting            |
| Progress Checklist        | Visual tracker for steps         |
| Profile Form              | Fields for address, preferences  |
| Video Player              | Embedded educational video       |
| Optional Services Section | Review and confirm services      |
| "Next" Button             | Advances to next step            |
| "Skip" Button             | Optional for non-essential steps |

---

## **3\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **Amplify Hosting**.
- **Routes**:
  - /register (Member self-registration)
  - /admin/users (Administrator management)
  - /settings/invite (Welon Trust invitation)
  - /member/onboarding, /admin/onboarding, /welon/onboarding
- **Accessibility**: WCAG 2.1 Level AA with ARIA labels.

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - registerMember: Registers Member with eligibility and services.
  - addUser: Adds Administrator or Welon Trust user.
  - updateOnboardingStatus: Updates progress and services.
- **Verification Services**: Integrate Onfido via **Lambda** for AML/KYC.
- **Email Service**: **AWS SES** for verification and invitations.
- **Marketing Integration**: Sync to CRM via **Lambda**.
- **Database (DynamoDB)**:
  - users: id, email, password_hash, role, status
  - members: user_id, questionnaire_responses, aml_kyc_status, optional_services
  - welon_trust: user_id, credentials, linked_members
  - marketing_data: user_id, acquisition_channel, campaign_id
- **Logging**: Record actions in **CloudWatch**.

---

## **4\. Testing and Validation**

- **Unit Tests**: Validate inputs, logic, and services.
- **Integration Tests**: Confirm end-to-end flows and integrations.
- **Security Testing**: Test verification and invitation security.
- **UAT**: Verify usability with 65+ demographic.

### **Test Cases**

| Scenario                 | Expected Outcome                              |
| ------------------------ | --------------------------------------------- |
| Member Self-Registration | Account created, email verified, services set |
| Ineligible Member        | Rejection message displayed                   |
| Administrator Addition   | New admin activated                           |
| Welon Trust Invitation   | Invitee sets up account                       |
| Onboarding Completion    | Redirect to dashboard, services confirmed     |

---

## **5\. Compliance and Security**

- **Data Privacy**: Encrypt with **AWS KMS** and TLS per HIPAA and SOC2.
- **Accessibility**: WCAG 2.1 for senior-friendly design.
- **Audit Trails**: Log actions in **CloudWatch**.
- **User Consent**: Require explicit consent for data and services.

---

## **Summary**

This specification delivers a secure, compliant, and accessible User Registration and Onboarding process for the Childfree Legacy platform, tailored to the 65+ demographic. AWS Amplify ensures scalability and security, supporting role-specific workflows, educational content, and optional services, with marketing integrations enhancing user acquisition tracking.
