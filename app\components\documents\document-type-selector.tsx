'use client';

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DocumentTypeSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function DocumentTypeSelector({
  value,
  onChange,
  className = '',
}: DocumentTypeSelectorProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className='text-sm font-medium'>Document Type</label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className='w-full'>
          <SelectValue placeholder='Select document type' />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='will'>Last Will and Testament</SelectItem>
          <SelectItem value='trust'>Revocable Living Trust</SelectItem>
          <SelectItem value='poa-healthcare'>
            Healthcare Power of Attorney
          </SelectItem>
          <SelectItem value='poa-financial'>
            Financial Power of Attorney
          </SelectItem>
          <SelectItem value='advance-directive'>
            Advance Healthcare Directive
          </SelectItem>
        </SelectContent>
      </Select>
      <p className='text-xs text-[var(--custom-gray-medium)]'>
        Select the type of legal document you want to create
      </p>
    </div>
  );
}
