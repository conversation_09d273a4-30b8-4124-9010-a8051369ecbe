# 7 \- Emergency Features and Dead Man’s Switch

# **Document 7 \- Functional Specification: Emergency Features and Dead Man’s Switch**

## **Overview**

The Emergency Features and Dead Man’s Switch ensure that Members of the Childfree Legacy Web Application can designate emergency contacts and configure automated safety checks to address scenarios of incapacitation or death. Tailored for users aged 65+, this specification outlines processes for setting up emergency contacts, configuring the Dead Man’s Switch to detect potential incapacitation, and enabling authorized individuals to access estate planning documents during emergencies. The system differentiates workflows for incapacitation (temporary or permanent) and death, with distinct verification and access controls. Leveraging AWS Amplify, these features prioritize usability, security (e.g., HIPAA and SOC2 compliance), and scalability, providing peace of mind and safeguarding Member well-being through serverless architecture, real-time data management, and robust encryption.

---

## **1\. Emergency Contact Setup**

Members can designate trusted individuals as emergency contacts to receive specific information in emergencies. Two types are supported:

- **Emergency Contact – Medical**: Receives medical details (e.g., allergies, medical power of attorney).
- **Emergency Contact – Other**: Receives non-medical details (e.g., pet information, access instructions).

All emergency information requests are routed through a call center for verification and dissemination.

### **Key Requirements**

#### **User Roles**

- **Members**: Manage emergency contacts via the application.
- **Administrators**: Override or assist with contact setup.
- **Emergency Contacts**: Designated recipients of specific information.
- **Welon-Emergency**: Verifies and processes emergency requests.

#### **Contact Information**

- Name, relationship, phone number, email, and type (Medical or Other).

#### **Security Measures**

- Contacts confirm their role via email or SMS using **AWS SES** or **Amazon SNS**.
- Data encrypted at rest and in transit with **AWS KMS**.
- Access restricted via **AWS Cognito** authentication.
- Requests initiated solely through the call center.

#### **Call Center Process**

- Verifies requester identity and request legitimacy.
- Sends information to the appropriate contact type.
- Logs actions in **Amazon CloudWatch** for auditing.

### **User Flow**

1. Member logs in via **AWS Cognito** and navigates to /Member/emergency-contacts.
2. Clicks "Add Contact," enters details, and selects type.
3. System sends a verification link/code via **AWS SES/SNS**.
4. Contact verifies by clicking the link or entering the code.
5. Verified contact appears on the Member’s dashboard with type indicated.

### **Emergency Request Process**

1. A request is submitted to the call center (e.g., by a family member or medical professional).
2. Call center verifies the requester using predefined protocols.
3. Based on request type:
   - Medical: Sends data to "Emergency Contact – Medical."
   - Non-medical: Sends data to "Emergency Contact – Other."
4. Logs details in **CloudWatch**.

### **Edge Cases**

- **Unverified Contacts**: Sends reminders via **AWS SES** after 7 days.
- **Duplicates**: Blocks duplicate email/phone entries with an error message.
- **Revoked Access**: Members can remove contacts, triggering a notification via **SES**.
- **Multiple Contacts**: Supports multiple contacts per type, with primary/secondary options.

### **Compliance Considerations**

- **HIPAA**: Encrypts medical data with **KMS**; restricts access via **Cognito**.
- **SOC2**: Logs all actions (additions, verifications, removals) in **CloudWatch**.

### **UI Components**

| Element                 | Description                                    |
| ----------------------- | ---------------------------------------------- |
| Contact Form            | Fields: name, relationship, phone, email, type |
| "Add Contact" Button    | Submits contact details                        |
| Verification Status     | Shows "Pending," "Verified," or "Expired"      |
| "Remove Contact" Button | Deletes contact                                |

---

## **2\. Dead Man’s Switch Configuration**

The Dead Man’s Switch (DMS) is an automated feature that triggers notifications and potentially grants document access if a Member misses check-ins, indicating possible incapacitation. Death is managed separately via manual processes.

### **Key Requirements**

#### **Customization Options**

- **Check-In Frequency**: Weekly, bi-weekly, monthly, or custom (e.g., every X days).
- **Communication Method**: Email (**AWS SES**), SMS (**Amazon SNS**), or both.
- **Escalation Protocol**:
  - **Presets**: Standard (3 misses), Sensitive (2 misses), Relaxed (4 misses).
  - **Custom**: Define misses per step.
  - **Notifications**: Specify contacts or services (e.g., wellness checks) per step.
  - **Content**: Optional personal message (e.g., "Check my home at \[address\]").

#### **Pause Options**

- **Pause Switch**: Disables DMS for up to 90 days, extendable to 180 days with a reactivation date.
- **Reminders**: Sent 3 days before and on reactivation via **AWS SES**.
- **Resumption**: Auto-resumes if reactivation date passes.

#### **Testing**

- **Test Mode**: Simulates missed check-ins, sending test notifications to a designated contact via **SES/SNS**.

#### **Check-In Mechanism**

- **Current**: Notification with "Confirm I’m Okay" button resets the timer.
- **Future**: Daily questions (e.g., "Are you well today?") requiring responses via **AWS AppSync**.

#### **Security Measures**

- Encrypts data with **AWS KMS**.
- Rate-limits check-ins via **AWS Lambda**.
- Access controlled by **AWS IAM**.

#### **Accessibility**

- High-contrast UI and large buttons.
- Guided setup with **AWS Polly** narration.

### **User Flow**

1. Member navigates to /Member/deadmanswitch.
2. Configures frequency, method, and escalation protocol.
3. Adds an optional message and clicks "Enable Switch."
4. **AWS Lambda** and **EventBridge** schedule check-ins.
5. Member receives notifications and resets the timer via "Confirm I’m Okay."
6. If missed:
   - 1st miss: Reminder to Member.
   - 2nd miss: Alerts contacts.
   - Final miss: Triggers incapacitation process.
7. Manual check-in available at /Member/dashboard.

#### **Post-Trigger Check-In**

- Member check-in post-escalation resets DMS and notifies all parties via **SES**.

#### **Pause Functionality**

- Member selects "Pause Switch," sets reason and date.
- **Lambda** pauses notifications; **SES** sends reminders.
- Resumes automatically if date passes.

#### **Test Mode**

- "Test Switch" simulates escalations, sending test alerts.

### **Disabling the Dead Man’s Switch**

- **Member Check-In**: Resets DMS automatically.
- **POA Intervention**: POA requests disable via call center with verification.
- **Welon-Emergency**: Disables with medical evidence.
- **Admin Override**: Manual disable via **Cognito MFA**.

#### **Rules for Disabling**

- POA requires legal proof.
- Welon-Emergency needs medical certification.
- Member check-in needs no verification.
- Logs all actions (except check-in) in **CloudWatch**.

#### **Post-Disabling Behavior**

- Stops notifications/escalations.
- Updates status (e.g., "Disabled by POA").
- Notifies parties via **SES**.

### **Edge Cases**

- **False Alarms**: Member resets post-trigger.
- **No Contacts**: Escalates to Welon-Emergency.
- **Downtime**: SMS fallback via **SNS**.
- **Pause Expiry**: POA/Welon-Emergency can extend/disable.

### **Compliance Considerations**

- **Consent**: Opt-in required.
- **HIPAA**: Encrypts data with **KMS**.
- **SOC2**: Logs all actions in **CloudWatch**.

### **UI Components**

| Element                   | Description                    |
| ------------------------- | ------------------------------ |
| Frequency Dropdown        | Selects check-in interval      |
| Communication Toggle      | Email, SMS, or both            |
| Escalation Selector       | Preset or custom protocol      |
| "Enable Switch" Button    | Activates DMS                  |
| "Confirm I’m Okay" Button | Resets timer                   |
| "Pause Switch" Button     | Pauses DMS                     |
| "Test Switch" Button      | Simulates escalations          |
| Status Indicator          | Shows next check-in and status |

---

## **3\. Emergency Access Mechanisms**

Authorized individuals access critical documents in emergencies, with separate workflows for incapacitation and death.

### **Key Requirements**

#### **Access Triggers**

- **Incapacitation**: DMS final escalation or Welon-Emergency notification.
- **Death**: Manual trigger by Welon-Basic with death certificate.

#### **Verification Process**

- **Incapacitation**: Medical certification via **AWS S3** upload.
- **Death**: Death certificate upload.

#### **Document Access**

- **Incapacitation**: Temporary (6 months), renewable.
- **Death**: Permanent.

#### **Reversal**

- Member login or legal representative resets incapacitation access.

#### **Security Measures**

- Identity verification via **SNS** code.
- Documents stored in **S3** with **KMS** encryption.
- HTTPS via **Amplify**.

#### **Notifications**

- Alerts sent via **SES** to Members, contacts, and Admins.

### **User Flow for Incapacitation**

1. Triggered by DMS or Welon-Emergency.
2. Welon-Emergency validates and uploads certification to **S3**.
3. Contact receives secure link via **SES/SNS**.
4. Enters verification code and accesses documents at /emergency/documents.
5. Member recovery revokes access via login.

### **User Flow for Death**

- See Document 13 \- Upon Death.

### **Edge Cases**

- **Multiple Requests**: Manages concurrent access without duplication.
- **Revoked Access**: Auto-revokes on Member recovery.
- **Expired Tokens**: Prompts new link request.
- **Transition**: Updates to permanent access with death certificate.

### **Compliance Considerations**

- **HIPAA**: Restricts access; logs in **CloudWatch**.
- **SOC2**: Secure token generation and auditing.

### **UI Components**

| Element                 | Description                    |
| ----------------------- | ------------------------------ |
| Access Link             | Secure URL for document access |
| Verification Code Field | Confirms identity              |
| Document Viewer         | Read-only document display     |
| "Submit Evidence" Form  | Uploads certifications         |
| "Report Status" Button  | Updates Member status          |

---

## **4\. Additional Features**

- **Manual Override**: Pause DMS with reason/date.
- **Contact Notifications**: Notify contacts on add/remove via **SES**.
- **Admin Dashboard**: Manage DMS at /admin/emergency.

### **Implementation Notes**

- Uses **Amplify** secure cookies for overrides.
- Logs actions in **CloudWatch**.

### **UI Components**

| Feature                   | Description                   |
| ------------------------- | ----------------------------- |
| "Pause Switch" Button     | Disables DMS temporarily      |
| "Notify Contact" Checkbox | Enables contact notifications |
| Admin Status Table        | Displays DMS statuses         |
| Evidence Review Interface | Approves certifications       |

---

## **5\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /Member/emergency-contacts
  - /Member/deadmanswitch
  - /emergency/documents
  - /admin/emergency
  - /emergency/submit-evidence

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - addEmergencyContact
  - configureDMS
  - checkIn
  - pauseDMS
  - testDMS
  - submitEvidence
  - approveEvidence
- **Scheduling**: **Lambda** \+ **EventBridge**.
- **Storage**: **DynamoDB** for data; **S3** for documents.
- **Encryption**: **KMS** for data; TLS/SSL for transit.
- **Logging**: **CloudWatch**.

---

## **6\. Testing and Validation**

- **Unit Tests**: Verify contact logic, DMS triggers, and access flows.
- **Integration Tests**: Simulate escalations and access workflows.
- **Security Tests**: Validate token security and revocation.
- **UAT**: Ensure usability for 65+ users.

### **Test Cases**

| Scenario              | Expected Outcome           |
| --------------------- | -------------------------- |
| Contact Added         | Listed and verified        |
| Check-In              | Timer resets               |
| Missed Check-In       | Escalation triggers        |
| Pause                 | Notifications pause/resume |
| Test Mode             | Simulated alerts           |
| Incapacitation Access | Granted post-certification |
| Death Access          | Permanent post-certificate |
| Recovery              | Access revoked             |

---

## **7\. Compliance and Security**

- **HIPAA**: Encrypts data with **KMS**; restricts via **Cognito**.
- **SOC2**: Logs all actions in **CloudWatch**.
- **State Laws**: Aligns with estate/privacy regulations.

---

## **Summary**

The Emergency Features and Dead Man’s Switch, powered by AWS Amplify, provide robust safety mechanisms for Members. Emergency contacts, automated check-ins via DMS, and secure document access workflows ensure scalability, security, and compliance with HIPAA and SOC2, fostering trust and well-being in the Childfree Legacy platform.
