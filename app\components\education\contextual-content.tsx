/**
 * ContextualContent Component
 *
 * A component for displaying educational content in context.
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  Content,
  ContentIntegrationPoint,
  ContentType,
} from '@/types/education';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { VideoPlayer } from './video-player';
import { ArticleViewer } from './article-viewer';
import { InfographicViewer } from './infographic-viewer';
import { AvatarAssistant } from './avatar-assistant';
import { TooltipContentComponent } from './tooltip-content';

interface ContextualContentProps {
  integrationPoint: ContentIntegrationPoint;
  triggerText: string;
  displayType?: 'tooltip' | 'popup' | 'inline';
  children?: React.ReactNode;
}

export function ContextualContent({
  integrationPoint,
  triggerText,
  displayType = 'popup',
  children,
}: ContextualContentProps) {
  const { contentForIntegrationPoint, trackContentView } =
    useEducationalContent();
  const [isOpen, setIsOpen] = useState(false);

  // Get relevant content for this integration point
  const relevantContent = contentForIntegrationPoint(integrationPoint);

  // If no content is available, don't render anything
  if (relevantContent.length === 0) {
    return <>{children}</>;
  }

  // Use the first piece of content (in a real app, you might want to be more selective)
  const content = relevantContent[0];

  // Track content view when opened
  const handleOpen = () => {
    trackContentView(content.id);
    setIsOpen(true);
  };

  // Render content based on type
  const renderContent = () => {
    switch (content.type) {
      case 'video':
        return <VideoPlayer video={content} />;
      case 'article':
        return <ArticleViewer article={content} />;
      case 'infographic':
        return <InfographicViewer infographic={content} />;
      case 'avatar':
        return <AvatarAssistant />;
      case 'tooltip':
        return (
          <div className='p-4'>
            <h3 className='font-semibold text-lg mb-2'>{content.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: content.content }} />
          </div>
        );
      default:
        return <p>Content not available</p>;
    }
  };

  // Render as tooltip
  if (displayType === 'tooltip') {
    return (
      <TooltipContentComponent
        tooltip={{
          ...content,
          type: ContentType.TOOLTIP,
          triggerText,
          content: `<p>${content.description}</p><a href="/dashboard/education/${content.id}" class="text-primary underline">Learn more</a>`,
        }}
        onView={() => trackContentView(content.id)}
        triggerElement={
          children || (
            <span className='underline cursor-help'>{triggerText}</span>
          )
        }
      />
    );
  }

  // Render as inline
  if (displayType === 'inline') {
    return (
      <Card className='my-4'>
        <CardHeader>
          <h3 className='font-semibold text-lg'>{content.title}</h3>
        </CardHeader>
        <CardContent>{renderContent()}</CardContent>
        <CardFooter>
          <Link href={`/dashboard/education/${content.id}`} passHref>
            <Button variant='outline'>View in Library</Button>
          </Link>
        </CardFooter>
      </Card>
    );
  }

  // Render as popup (default)
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant='link' className='p-0 h-auto' onClick={handleOpen}>
          {children || triggerText}
        </Button>
      </DialogTrigger>
      <DialogContent className='max-w-4xl'>
        <DialogHeader>
          <DialogTitle>{content.title}</DialogTitle>
          <DialogDescription>{content.description}</DialogDescription>
        </DialogHeader>
        <div className='py-4'>{renderContent()}</div>
        <div className='flex justify-between items-center'>
          <span className='text-sm text-muted-foreground'>
            Tags: {content.tags.join(', ')}
          </span>
          <Link href={`/dashboard/education/${content.id}`} passHref>
            <Button variant='outline'>View in Library</Button>
          </Link>
        </div>
      </DialogContent>
    </Dialog>
  );
}
