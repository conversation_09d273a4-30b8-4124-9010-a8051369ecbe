'use client';

import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { TipTapEditorWithVariables } from '@/components/ui/tiptap-editor';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ToastProvider, useToast } from '@/components/ui/toast';
import {
  Al<PERSON><PERSON>riangle,
  ArrowLeft,
  CheckCircle,
  Eye,
  FileText,
  Save,
  Send,
  Upload,
  XCircle,
} from 'lucide-react';
import { getCurrentUser } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getTemplate } from '@/app/utils/templates';
import { toast } from 'sonner';
import {
  getInterviewsList,
  getInterviewWithVersion,
} from '@/lib/api/interview-builder-new';
import { useAuth } from '@/context/AuthContext';
import { adminTemplatesAPI } from '@/lib/api/admin-templates';

// Mock data for states and document types
const states = [
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
];

const documentTypes = [
  'Will',
  'Trust',
  'Healthcare POA',
  'Financial POA',
  'Advance Directive',
];

// FUNCTION FOR LOAD QUESTIONS FROM INTERVIEW BUILDER AND GET REQUIRED DATA FOR TEMPLATE BUILD
const getInterviewWithQuestion = async () => {
  const interviews = await getInterviewsList();
  if (!interviews || interviews.length === 0) return [];

  const interviewData = await getInterviewWithVersion(interviews[0].id);
  const questionData = interviewData?.version?.questions || [];

  return questionData
    .filter(q => q.questionMapping)
    .map(q => ({
      id: q.questionId,
      label: q.text,
      value: q.questionMapping ?? '',
    }));
};

// Mock placeholders for template
const placeholders = [
  { id: 'member_name', label: 'Member Name', value: '[Member_NAME]' },
  { id: 'member_phone', label: 'Member Phone', value: '[Member_PHONE]' },
  { id: 'member_address', label: 'Member Address', value: '[Member_ADDRESS]' },
  { id: 'member_dob', label: 'Member Date of Birth', value: '[Member_DOB]' },
  { id: 'beneficiary', label: 'Beneficiary', value: '[BENEFICIARY]' },
  { id: 'executor', label: 'Executor', value: '[EXECUTOR]' },
  { id: 'witness', label: 'Witness', value: '[WITNESS]' },
  { id: 'notary', label: 'Notary', value: '[NOTARY]' },
  { id: 'today_date', label: "Today's Date", value: '[TODAY_DATE]' },
];

// Define the type for template data
type Template = {
  id: string;
  state: string;
  type: string;
  version: string;
  startDate: string | null;
  status: string;
  content: string;
  legalReferences: string;
  isAdditional: boolean;
  startPageId: string;
  endPageId: string;
};

function TemplateEditPageContent() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const { addToast } = useToast();
  const queryClient = useQueryClient();
  const id = params?.id as string;
  const isNewTemplate = id === 'new';

  const { data: questionsMapping = [], isLoading: isLoadingQuestionsMapping } =
    useQuery({
      queryKey: ['questions-mapping'],
      queryFn: getInterviewWithQuestion,
    });

  const [template, setTemplate] = useState<Template>({
    id: '',
    state: '',
    type: '',
    version: '1.0',
    startDate: new Date().toISOString().split('T')[0] || null,
    status: 'Draft',
    content: '',
    legalReferences: '',
    isAdditional: false,
    startPageId: '',
    endPageId: '',
  });

  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('editor');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [validationResults, setValidationResults] = useState<{
    hasMainDocx: boolean;
    hasManifest: boolean;
    manifestData: any;
    attachments: string[];
    isValid: boolean;
  } | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Field validation state
  const [fieldErrors, setFieldErrors] = useState<{
    state?: string;
    type?: string;
    startDate?: string;
    content?: string;
    legalReferences?: string;
  }>({});
  const [touched, setTouched] = useState<{
    state?: boolean;
    type?: boolean;
    startDate?: boolean;
    content?: boolean;
    legalReferences?: boolean;
  }>({});

  // Preview state
  const [previewData, setPreviewData] = useState<{ [key: string]: string }>({
    '[Member_NAME]': 'John Doe',
    '[Member_ADDRESS]': '123 Main Street, Anytown, CA 90210',
    '[Member_DOB]': '01/15/1980',
    '[BENEFICIARY]': 'Jane Doe',
    '[EXECUTOR]': 'Robert Smith',
    '[WITNESS]': 'Alice Johnson',
    '[NOTARY]': 'Mary Brown',
    '[TODAY_DATE]': new Date().toLocaleDateString(),
  });

  const [previewMode, setPreviewMode] = useState<'formatted' | 'raw'>(
    'formatted'
  );

  // Start and end page content state
  const [startPageContent, setStartPageContent] = useState<string>('');
  const [endPageContent, setEndPageContent] = useState<string>('');
  const [isLoadingStartPage, setIsLoadingStartPage] = useState(false);
  const [isLoadingEndPage, setIsLoadingEndPage] = useState(false);

  // Fetch template data for existing templates
  const { data: templateData, isLoading: isLoadingTemplate } = useQuery({
    queryKey: ['template', id],
    queryFn: async () => {
      const { template, latestVersion } =
        await adminTemplatesAPI.getTemplate(id);

      // Update template state with fetched data
      setTemplate({
        id: template.id,
        state: template.templateState,
        type: template.type,
        version: latestVersion?.versionNumber?.toString() || '1.0',
        startDate:
          template.createdAt?.split('T')[0] ||
          new Date().toISOString().split('T')[0],
        status: template.isActive ? 'Active' : 'Draft',
        content: latestVersion?.content || '',
        legalReferences: '', // This field might need to be added to your schema
        isAdditional: template.isAdditional || false,
        startPageId: template.startPageTemplateId || '',
        endPageId: template.endPageTemplateId || '',
      });

      return { template, latestVersion };
    },
    enabled: !isNewTemplate,
  });

  const {
    data: additionalTemplates = [],
    isLoading: isLoadingAdditionalTemplates,
  } = useQuery({
    queryKey: ['admin-additional-templates'],
    queryFn: adminTemplatesAPI.listAdditionalTemplates,
  });

  console.log('===> ADDITIONAL TEMPLATES', additionalTemplates);

  // Function to fetch template content by ID
  const fetchTemplateContent = async (templateId: string): Promise<string> => {
    try {
      const { template, latestVersion } =
        await adminTemplatesAPI.getTemplate(templateId);
      return latestVersion?.content || '';
    } catch (error) {
      console.error('Error fetching template content:', error);
      return '';
    }
  };

  // Fetch start page content when startPageId changes
  useEffect(() => {
    if (template.startPageId) {
      setIsLoadingStartPage(true);
      fetchTemplateContent(template.startPageId)
        .then(content => {
          setStartPageContent(content);
        })
        .finally(() => {
          setIsLoadingStartPage(false);
        });
    } else {
      setStartPageContent('');
    }
  }, [template.startPageId]);

  // Fetch end page content when endPageId changes
  useEffect(() => {
    if (template.endPageId) {
      setIsLoadingEndPage(true);
      fetchTemplateContent(template.endPageId)
        .then(content => {
          setEndPageContent(content);
        })
        .finally(() => {
          setIsLoadingEndPage(false);
        });
    } else {
      setEndPageContent('');
    }
  }, [template.endPageId]);

  useEffect(() => {
    // Handle error state for non-existent templates
    if (!isNewTemplate && !isLoadingTemplate && !templateData) {
      setError('Template not found');
    }
  }, [isNewTemplate, isLoadingTemplate, templateData]);

  // Validation functions
  const validateField = (fieldName: string, value: string) => {
    const errors: { [key: string]: string } = {};

    switch (fieldName) {
      case 'state':
        if (!value.trim()) {
          errors.state = 'State is required';
        }
        break;
      case 'type':
        if (!value.trim()) {
          errors.type = 'Document type is required';
        }
        break;
      case 'startDate':
        if (!value.trim()) {
          errors.startDate = 'Effective date is required';
        } else {
          const selectedDate = new Date(value);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          if (selectedDate < today) {
            errors.startDate = 'Effective date cannot be in the past';
          }
        }
        break;
      case 'content':
        if (!value.trim()) {
          errors.content = 'Template content is required';
        } else {
          // Strip HTML tags for length validation
          const stripHtml = (html: string) => {
            if (typeof document === 'undefined') return html; // SSR safety
            const tmp = document.createElement('div');
            tmp.innerHTML = html;
            return tmp.textContent || tmp.innerText || '';
          };
          const textContent = stripHtml(value.trim());
          if (textContent.length < 50) {
            errors.content = 'Template content must be at least 50 characters';
          }
        }
        break;
      case 'legalReferences':
        if (!value.trim()) {
          errors.legalReferences = 'Legal references are required';
        }
        break;
    }

    setFieldErrors(prev => ({ ...prev, [fieldName]: errors[fieldName] }));
    return !errors[fieldName];
  };

  const validateAllFields = () => {
    const fields = ['state', 'type', 'startDate', 'content', 'legalReferences'];
    let isValid = true;

    fields.forEach(field => {
      const value = template[field as keyof Template] as string;
      if (!validateField(field, value || '')) {
        isValid = false;
      }
    });

    return isValid;
  };

  const isFormValid = () => {
    const baseRequirements =
      template.state &&
      template.type &&
      template.startDate &&
      template.content &&
      Object.keys(fieldErrors).length === 0;

    // For new templates, require ZIP file upload and validation
    if (isNewTemplate) {
      return (
        baseRequirements &&
        template.legalReferences &&
        uploadedFile &&
        validationResults?.isValid
      );
    }

    // For existing templates, only require basic fields
    return baseRequirements;
  };

  const handleStateChange = (value: string) => {
    setTemplate({ ...template, state: value });
    setTouched(prev => ({ ...prev, state: true }));
    validateField('state', value);
  };

  const handleTypeChange = (value: string) => {
    setTemplate({ ...template, type: value });
    setTouched(prev => ({ ...prev, type: true }));
    validateField('type', value);
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setTemplate({ ...template, startDate: value });
    setTouched(prev => ({ ...prev, startDate: true }));
    validateField('startDate', value);
  };

  const handleLegalReferencesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const value = e.target.value;
    setTemplate({ ...template, legalReferences: value });
    setTouched(prev => ({ ...prev, legalReferences: true }));
    validateField('legalReferences', value);
  };

  const handleIsAdditionalChange = (checked: boolean) => {
    setTemplate({ ...template, isAdditional: checked });
  };

  const handleStartPageChange = (value: string) => {
    setTemplate({ ...template, startPageId: value === 'none' ? '' : value });
  };

  const handleEndPageChange = (value: string) => {
    setTemplate({ ...template, endPageId: value === 'none' ? '' : value });
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.zip')) {
      addToast({
        title: 'Invalid File Type',
        description: 'Please upload a ZIP file containing the template package',
        variant: 'destructive',
      });
      return;
    }

    setUploadedFile(file);
    setError(null);

    addToast({
      title: 'File Uploaded',
      description: `${file.name} uploaded successfully. Validating...`,
      variant: 'success',
    });

    await validateZipFile(file);
  };

  const validateZipFile = async (file: File) => {
    setIsValidating(true);

    try {
      // In a real implementation, you would use a library like JSZip to extract and validate
      // For now, we'll simulate the validation
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock validation results
      const mockValidation = {
        hasMainDocx: true,
        hasManifest: true,
        manifestData: {
          jurisdiction: 'California',
          version: '1.0',
          effectiveDate: '2024-01-01',
          documentType: 'Will',
          requiredFields: ['Member_NAME', 'Member_ADDRESS', 'BENEFICIARY'],
        },
        attachments: ['attachment1.docx', 'attachment2.docx'],
        isValid: true,
      };

      setValidationResults(mockValidation);

      // Update template with manifest data but preserve user's selections
      if (mockValidation.manifestData) {
        setTemplate(prev => ({
          ...prev,
          state: prev.state || mockValidation.manifestData.jurisdiction,
          type: prev.type || mockValidation.manifestData.documentType,
          version: mockValidation.manifestData.version,
          startDate: prev.startDate || new Date().toISOString().split('T')[0],
        }));
      }

      addToast({
        title: 'Validation Complete',
        description:
          'ZIP file validated successfully. Template data has been populated.',
        variant: 'success',
      });
    } catch (err) {
      addToast({
        title: 'Validation Failed',
        description:
          'Failed to validate ZIP file. Please check the file structure and try again.',
        variant: 'destructive',
      });
      setValidationResults(null);
    } finally {
      setIsValidating(false);
    }
  };

  const client = generateClient<Schema>();

  // Create template mutation for new templates
  const createTemplateMutation = useMutation({
    mutationFn: async (isDraft: boolean = true) => {
      if (!template.state || !template.type || !template.content) {
        throw new Error('Please fill in all required fields');
      }

      // Get current user for createdBy field
      const { userId } = await getCurrentUser();
      const now = new Date().toISOString();

      // Create the template
      const { data: createdTemplate, errors: templateErrors } =
        await client.models.Template.create({
          id: crypto.randomUUID(),
          createdBy: userId,
          createdByEmail: user?.signInDetails?.loginId,
          createdAt: now,
          updatedAt: now,
          type: template.type,
          templateState: template.state,
          templateName: `${template.state} ${template.type}`,
          isActive: true,
          isDraft: isDraft,
          isAdditional: template.isAdditional,
          startPageTemplateId: template.startPageId || null,
          endPageTemplateId: template.endPageId || null,
        });

      if (templateErrors || !createdTemplate) {
        throw new Error('Failed to create template');
      }

      // Create the initial template version
      const { errors: versionErrors } =
        await client.models.TemplateVersion.create({
          templateId: createdTemplate.id,
          versionNumber: 1,
          content: template.content,
          createdAt: now,
        });

      if (versionErrors) {
        throw new Error('Failed to create template version');
      }
    },
    onSuccess: () => {
      toast.success('Template created successfully');
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      router.push('/admin/templates');
    },
    onError: error => {
      console.error('Error creating template:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to create template'
      );
    },
  });

  // Update template mutation for existing templates
  const updateTemplateMutation = useMutation({
    mutationFn: async (isDraft: boolean = true) => {
      if (!template.state || !template.type || !template.content) {
        throw new Error('Please fill in all required fields');
      }

      const now = new Date().toISOString();

      // Update the template
      const { errors: templateErrors } = await client.models.Template.update({
        id: id,
        templateName: `${template.state} ${template.type}`,
        type: template.type,
        templateState: template.state,
        updatedAt: now,
        isDraft: isDraft,
        isAdditional: template.isAdditional,
        startPageTemplateId: template.startPageId || null,
        endPageTemplateId: template.endPageId || null,
      });

      if (templateErrors) {
        throw new Error('Failed to update template');
      }

      // Get current version number
      const { data: versions } = await client.models.TemplateVersion.list({
        filter: {
          templateId: { eq: id },
        },
      });
      const latestVersion = versions.sort(
        (a, b) => b.versionNumber - a.versionNumber
      )[0];
      const newVersionNumber = (latestVersion?.versionNumber || 0) + 1;

      // Create a new version
      const { errors: versionErrors } =
        await client.models.TemplateVersion.create({
          templateId: id,
          versionNumber: newVersionNumber,
          content: template.content,
          createdAt: now,
        });

      if (versionErrors) {
        throw new Error('Failed to create template version');
      }
    },
    onSuccess: () => {
      toast.success('Template updated successfully');
      queryClient.invalidateQueries({ queryKey: ['templates'] });
      queryClient.invalidateQueries({ queryKey: ['template', id] });
      router.push('/admin/templates');
    },
    onError: error => {
      console.error('Error updating template:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update template'
      );
    },
  });

  const handleSaveDraft = async () => {
    // Mark all fields as touched to show validation errors
    setTouched({
      state: true,
      type: true,
      startDate: true,
      content: true,
      legalReferences: true,
    });

    if (!validateAllFields()) {
      addToast({
        title: 'Validation Error',
        description: 'Please fix all validation errors before saving',
        variant: 'destructive',
      });
      return;
    }

    if (isNewTemplate) {
      createTemplateMutation.mutate(true); // Save as draft
    } else {
      updateTemplateMutation.mutate(true); // Save as draft
    }
  };

  const handleSubmitForReview = () => {
    // Mark all fields as touched to show validation errors
    setTouched({
      state: true,
      type: true,
      startDate: true,
      content: true,
      legalReferences: true,
    });

    if (!isFormValid()) {
      addToast({
        title: 'Incomplete Form',
        description:
          'Please complete all required fields and upload a valid ZIP file before submitting for review',
        variant: 'destructive',
      });
      return;
    }

    if (isNewTemplate) {
      createTemplateMutation.mutate(true); // Submit for review as draft
    } else {
      updateTemplateMutation.mutate(true); // Submit for review as draft
    }
  };

  const handlePublish = () => {
    // Mark all fields as touched to show validation errors
    setTouched({
      state: true,
      type: true,
      startDate: true,
      content: true,
      legalReferences: true,
    });

    if (!isFormValid()) {
      addToast({
        title: 'Incomplete Form',
        description:
          'Please complete all required fields and upload a valid ZIP file before publishing',
        variant: 'destructive',
      });
      return;
    }

    if (isNewTemplate) {
      createTemplateMutation.mutate(false); // Publish (not draft)
    } else {
      updateTemplateMutation.mutate(false); // Publish (not draft)
    }
  };

  // Calculate completion percentage
  const getCompletionPercentage = () => {
    // Check content length by stripping HTML
    const hasValidContent = () => {
      if (!template.content) return false;
      if (typeof document === 'undefined') return template.content.length >= 50; // SSR safety
      const tmp = document.createElement('div');
      tmp.innerHTML = template.content;
      const textContent = tmp.textContent || tmp.innerText || '';
      return textContent.length >= 50;
    };

    // Different requirements for new vs existing templates
    if (isNewTemplate) {
      const requirements = [
        template.state,
        template.type,
        template.startDate,
        uploadedFile,
        validationResults?.isValid,
        hasValidContent(),
        template.legalReferences,
      ];
      const completed = requirements.filter(Boolean).length;
      return Math.round((completed / requirements.length) * 100);
    } else {
      // For existing templates, don't require ZIP file
      const requirements = [
        template.state,
        template.type,
        template.startDate,
        hasValidContent(),
      ];
      const completed = requirements.filter(Boolean).length;
      return Math.round((completed / requirements.length) * 100);
    }
  };

  // Generate preview content with placeholder substitution
  const getPreviewContent = () => {
    const parts: string[] = [];

    // Add start page content if available
    if (startPageContent) {
      let processedStartPage = startPageContent;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedStartPage = processedStartPage.replace(regex, value);
      });
      parts.push(processedStartPage);
    }

    // Add main template content
    if (template.content) {
      let processedMainContent = template.content;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedMainContent = processedMainContent.replace(regex, value);
      });
      parts.push(processedMainContent);
    }

    // Add end page content if available
    if (endPageContent) {
      let processedEndPage = endPageContent;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedEndPage = processedEndPage.replace(regex, value);
      });
      parts.push(processedEndPage);
    }

    if (parts.length === 0) {
      return 'No content to preview';
    }

    // Join parts with page breaks
    return parts.join(
      '<div style="page-break-before: always; margin-top: 2rem; padding-top: 2rem; border-top: 2px dashed #ccc;"></div>'
    );
  };

  // Generate plain text preview content
  const getPlainTextPreview = () => {
    const parts: string[] = [];

    // Strip HTML tags helper function
    const stripHtml = (html: string) => {
      if (typeof document === 'undefined') return html; // SSR safety
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    };

    // Process start page content
    if (startPageContent) {
      let processedStartPage = startPageContent;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedStartPage = processedStartPage.replace(regex, value);
      });
      parts.push(stripHtml(processedStartPage));
    }

    // Process main template content
    if (template.content) {
      let processedMainContent = template.content;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedMainContent = processedMainContent.replace(regex, value);
      });
      parts.push(stripHtml(processedMainContent));
    }

    // Process end page content
    if (endPageContent) {
      let processedEndPage = endPageContent;
      Object.entries(previewData).forEach(([placeholder, value]) => {
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        );
        processedEndPage = processedEndPage.replace(regex, value);
      });
      parts.push(stripHtml(processedEndPage));
    }

    if (parts.length === 0) {
      return 'No content to preview';
    }

    // Join parts with page separators
    return parts.join('\n\n--- PAGE BREAK ---\n\n');
  };

  // Component for field labels with required indicator
  const FieldLabel = ({
    children,
    required = false,
  }: {
    children: React.ReactNode;
    required?: boolean;
  }) => (
    <label className='text-sm font-medium mb-2 block'>
      {children}
      {required && <span className='text-red-500 ml-1'>*</span>}
    </label>
  );

  // Component for field error display
  const FieldError = ({ error }: { error?: string }) => {
    if (!error) return null;
    return (
      <p className='text-red-500 text-xs mt-1 flex items-center'>
        <AlertTriangle className='h-3 w-3 mr-1' />
        {error}
      </p>
    );
  };

  // Show loading state for existing templates
  if (
    !isNewTemplate &&
    (isLoadingTemplate ||
      isLoadingQuestionsMapping ||
      isLoadingAdditionalTemplates)
  ) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-[var(--custom-gray-medium)]'>
              Loading template...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center mb-6'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => router.push('/admin/templates')}
          className='mr-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Templates
        </Button>
        <div className='flex-1'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
                {isNewTemplate
                  ? 'Create New Template'
                  : `Edit Template: ${template.state} ${template.type}`}
              </h1>
              <p className='text-[var(--custom-gray-medium)] mt-1'>
                {isNewTemplate
                  ? 'Upload and configure a new legal document template'
                  : 'Update existing template configuration'}
              </p>
            </div>
            {isNewTemplate && (
              <div className='text-right'>
                <div className='text-sm font-medium text-[var(--custom-gray-dark)] mb-1'>
                  Completion: {getCompletionPercentage()}%
                </div>
                <div className='w-32 bg-gray-200 rounded-full h-2'>
                  <div
                    className='bg-green-600 h-2 rounded-full transition-all duration-300'
                    style={{ width: `${getCompletionPercentage()}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className='grid grid-cols-1 md:grid-cols-1 gap-6'>
        <div className='md:col-span-1'>
          <Card>
            <CardHeader>
              <CardTitle>Template Details</CardTitle>
              <CardDescription>
                Basic information about the template
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <FieldLabel required>State</FieldLabel>
                <Select
                  value={template.state}
                  onValueChange={handleStateChange}
                >
                  <SelectTrigger
                    className={`${touched.state && fieldErrors.state ? 'border-red-500 ring-red-500' : ''}`}
                  >
                    <SelectValue placeholder='Select state' />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map(state => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FieldError
                  error={touched.state ? fieldErrors.state : undefined}
                />
              </div>

              <div>
                <FieldLabel required>Document Type</FieldLabel>
                <Select value={template.type} onValueChange={handleTypeChange}>
                  <SelectTrigger
                    className={`${touched.type && fieldErrors.type ? 'border-red-500 ring-red-500' : ''}`}
                  >
                    <SelectValue placeholder='Select document type' />
                  </SelectTrigger>
                  <SelectContent>
                    {documentTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FieldError
                  error={touched.type ? fieldErrors.type : undefined}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Version
                </label>
                <Input value={template.version} disabled />
              </div>

              <div>
                <FieldLabel required>Effective Date</FieldLabel>
                <Input
                  type='date'
                  value={template.startDate || ''}
                  onChange={handleStartDateChange}
                  className={`${touched.startDate && fieldErrors.startDate ? 'border-red-500 ring-red-500' : ''}`}
                />
                <FieldError
                  error={touched.startDate ? fieldErrors.startDate : undefined}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>Status</label>
                <Input value={template.status} disabled />
              </div>

              <div className='space-y-2'>
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='isAdditional'
                    checked={template.isAdditional}
                    onCheckedChange={handleIsAdditionalChange}
                  />
                  <label
                    htmlFor='isAdditional'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    Mark as Additional Template
                  </label>
                </div>
                <p className='text-xs text-[var(--custom-gray-medium)] ml-6'>
                  Additional templates are supplementary documents that can be
                  used alongside primary templates
                </p>
              </div>

              {/* Start Page and End Page selects - only show if current template is NOT additional */}
              {!template.isAdditional && (
                <>
                  <div>
                    <label className='text-sm font-medium mb-2 block'>
                      Start Page Template
                    </label>
                    <Select
                      value={template.startPageId || 'none'}
                      onValueChange={handleStartPageChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select start page template (optional)' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='none'>None</SelectItem>
                        {additionalTemplates.map(additionalTemplate => (
                          <SelectItem
                            key={additionalTemplate.id}
                            value={additionalTemplate.id}
                          >
                            {additionalTemplate.templateName ||
                              `${additionalTemplate.templateState} ${additionalTemplate.type}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                      Optional template to be used as the first page of this
                      document
                    </p>
                  </div>

                  <div>
                    <label className='text-sm font-medium mb-2 block'>
                      End Page Template
                    </label>
                    <Select
                      value={template.endPageId || 'none'}
                      onValueChange={handleEndPageChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select end page template (optional)' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='none'>None</SelectItem>
                        {additionalTemplates.map(additionalTemplate => (
                          <SelectItem
                            key={additionalTemplate.id}
                            value={additionalTemplate.id}
                          >
                            {additionalTemplate.templateName ||
                              `${additionalTemplate.templateState} ${additionalTemplate.type}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                      Optional template to be used as the last page of this
                      document
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className='md:col-span-2'>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className='w-full'
          >
            <TabsList className='grid w-full grid-cols-3 mb-6'>
              <TabsTrigger
                value='upload'
                disabled={true}
                className='w-full opacity-60 cursor-not-allowed'
                title='This feature is currently in development and will be available soon'
              >
                <span className='flex items-center'>
                  Upload & Validate
                  <span className='ml-2 text-xs bg-amber-100 text-amber-800 px-1.5 py-0.5 rounded-full'>
                    In Development
                  </span>
                </span>
              </TabsTrigger>
              <TabsTrigger value='editor'>Template Editor</TabsTrigger>
              <TabsTrigger value='preview'>Preview</TabsTrigger>
            </TabsList>

            <TabsContent value='upload'>
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    {isNewTemplate
                      ? 'Upload Template Package'
                      : 'Template Package'}
                    {uploadedFile && validationResults?.isValid && (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    )}
                  </CardTitle>
                  <CardDescription>
                    {isNewTemplate
                      ? 'Upload a ZIP file containing main.docx, manifest.json, and optional attachments'
                      : 'Template package information and optional updates'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-6'>
                    {/* Requirements checklist */}
                    <div className='bg-gray-50 p-4 rounded-lg'>
                      <h4 className='font-medium mb-3 text-[var(--custom-gray-dark)]'>
                        Requirements Checklist
                      </h4>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-2 text-sm'>
                        <div className='flex items-center space-x-2'>
                          {template.state ? (
                            <CheckCircle className='h-4 w-4 text-green-600' />
                          ) : (
                            <XCircle className='h-4 w-4 text-red-600' />
                          )}
                          <span
                            className={
                              template.state ? 'text-green-700' : 'text-red-700'
                            }
                          >
                            State selected
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {template.type ? (
                            <CheckCircle className='h-4 w-4 text-green-600' />
                          ) : (
                            <XCircle className='h-4 w-4 text-red-600' />
                          )}
                          <span
                            className={
                              template.type ? 'text-green-700' : 'text-red-700'
                            }
                          >
                            Document type selected
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {template.startDate ? (
                            <CheckCircle className='h-4 w-4 text-green-600' />
                          ) : (
                            <XCircle className='h-4 w-4 text-red-600' />
                          )}
                          <span
                            className={
                              template.startDate
                                ? 'text-green-700'
                                : 'text-red-700'
                            }
                          >
                            Effective date set
                          </span>
                        </div>
                        {isNewTemplate && (
                          <>
                            <div className='flex items-center space-x-2'>
                              {uploadedFile ? (
                                <CheckCircle className='h-4 w-4 text-green-600' />
                              ) : (
                                <XCircle className='h-4 w-4 text-red-600' />
                              )}
                              <span
                                className={
                                  uploadedFile
                                    ? 'text-green-700'
                                    : 'text-red-700'
                                }
                              >
                                ZIP file uploaded
                              </span>
                            </div>
                            <div className='flex items-center space-x-2'>
                              {validationResults?.isValid ? (
                                <CheckCircle className='h-4 w-4 text-green-600' />
                              ) : (
                                <XCircle className='h-4 w-4 text-red-600' />
                              )}
                              <span
                                className={
                                  validationResults?.isValid
                                    ? 'text-green-700'
                                    : 'text-red-700'
                                }
                              >
                                ZIP validation passed
                              </span>
                            </div>
                          </>
                        )}
                        <div className='flex items-center space-x-2'>
                          {(() => {
                            if (!template.content) return false;
                            if (typeof document === 'undefined')
                              return template.content.length >= 50;
                            const tmp = document.createElement('div');
                            tmp.innerHTML = template.content;
                            const textContent =
                              tmp.textContent || tmp.innerText || '';
                            return textContent.length >= 50;
                          })() ? (
                            <CheckCircle className='h-4 w-4 text-green-600' />
                          ) : (
                            <XCircle className='h-4 w-4 text-red-600' />
                          )}
                          <span
                            className={
                              (() => {
                                if (!template.content) return false;
                                if (typeof document === 'undefined')
                                  return template.content.length >= 50;
                                const tmp = document.createElement('div');
                                tmp.innerHTML = template.content;
                                const textContent =
                                  tmp.textContent || tmp.innerText || '';
                                return textContent.length >= 50;
                              })()
                                ? 'text-green-700'
                                : 'text-red-700'
                            }
                          >
                            Template content added
                          </span>
                        </div>
                      </div>
                    </div>

                    {isNewTemplate ? (
                      <div className='border-2 border-dashed border-gray-300 rounded-lg p-8 text-center'>
                        <Upload className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
                        <div className='space-y-2'>
                          <p className='text-lg font-medium'>
                            Upload ZIP Package
                          </p>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            Select a ZIP file containing your template package
                          </p>
                        </div>
                        <input
                          type='file'
                          accept='.zip'
                          onChange={handleFileUpload}
                          className='hidden'
                          id='file-upload'
                        />
                        <label
                          htmlFor='file-upload'
                          className='mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer'
                        >
                          Choose File
                        </label>
                      </div>
                    ) : (
                      <div className='bg-blue-50 border border-blue-200 rounded-lg p-6 text-center'>
                        <FileText className='mx-auto h-12 w-12 text-blue-600 mb-4' />
                        <div className='space-y-2'>
                          <p className='text-lg font-medium text-blue-900'>
                            Existing Template
                          </p>
                          <p className='text-sm text-blue-700'>
                            You are editing an existing template. The template
                            content can be modified in the Template Editor tab.
                          </p>
                          <p className='text-xs text-blue-600 mt-2'>
                            To upload a new template package, create a new
                            template instead.
                          </p>
                        </div>
                      </div>
                    )}

                    {uploadedFile && (
                      <div className='bg-blue-50 p-4 rounded-lg'>
                        <div className='flex items-center'>
                          <FileText className='h-5 w-5 text-blue-600 mr-2' />
                          <span className='font-medium'>
                            {uploadedFile.name}
                          </span>
                          <span className='ml-2 text-sm text-[var(--custom-gray-medium)]'>
                            ({(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        </div>
                      </div>
                    )}

                    {isValidating && (
                      <div className='text-center py-4'>
                        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2'></div>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          Validating package structure...
                        </p>
                      </div>
                    )}

                    {validationResults && (
                      <div className='space-y-4'>
                        <h3 className='font-medium text-lg'>
                          Validation Results
                        </h3>

                        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                          <div className='flex items-center space-x-2'>
                            {validationResults.hasMainDocx ? (
                              <CheckCircle className='h-5 w-5 text-green-600' />
                            ) : (
                              <XCircle className='h-5 w-5 text-red-600' />
                            )}
                            <span className='text-sm'>main.docx found</span>
                          </div>

                          <div className='flex items-center space-x-2'>
                            {validationResults.hasManifest ? (
                              <CheckCircle className='h-5 w-5 text-green-600' />
                            ) : (
                              <XCircle className='h-5 w-5 text-red-600' />
                            )}
                            <span className='text-sm'>manifest.json found</span>
                          </div>
                        </div>

                        {validationResults.manifestData && (
                          <div className='bg-gray-50 p-4 rounded-lg'>
                            <h4 className='font-medium mb-2'>
                              Parsed Metadata
                            </h4>
                            <div className='grid grid-cols-2 gap-2 text-sm'>
                              <div>
                                <strong>Jurisdiction:</strong>{' '}
                                {validationResults.manifestData.jurisdiction}
                              </div>
                              <div>
                                <strong>Version:</strong>{' '}
                                {validationResults.manifestData.version}
                              </div>
                              <div>
                                <strong>Document Type:</strong>{' '}
                                {validationResults.manifestData.documentType}
                              </div>
                              <div>
                                <strong>Effective Date:</strong>{' '}
                                {validationResults.manifestData.effectiveDate}
                              </div>
                            </div>
                          </div>
                        )}

                        {validationResults.attachments.length > 0 && (
                          <div>
                            <h4 className='font-medium mb-2'>
                              Attachments Found
                            </h4>
                            <ul className='text-sm space-y-1'>
                              {validationResults.attachments.map(
                                (attachment, index) => (
                                  <li
                                    key={index}
                                    className='flex items-center space-x-2'
                                  >
                                    <FileText className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                                    <span>{attachment}</span>
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='editor'>
              <Card>
                <CardHeader>
                  <CardTitle>Template Content</CardTitle>
                  <CardDescription>
                    Edit the template content with placeholders
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FieldLabel required>Template Content</FieldLabel>
                  <TipTapEditorWithVariables
                    content={template.content}
                    onChange={content => {
                      setTemplate({ ...template, content });
                      setTouched(prev => ({ ...prev, content: true }));
                      validateField('content', content);
                    }}
                    placeholder='Enter template content here...'
                    className={`${touched.content && fieldErrors.content ? 'border-red-500 ring-red-500' : ''}`}
                    variables={[...questionsMapping, ...placeholders]}
                    showVariables={true}
                  />
                  <FieldError
                    error={touched.content ? fieldErrors.content : undefined}
                  />
                </CardContent>
              </Card>

              <Card className='mt-6'>
                <CardHeader>
                  <CardTitle>Legal References</CardTitle>
                  <CardDescription>
                    Add relevant legal citations and notes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FieldLabel required>Legal References</FieldLabel>
                  <Textarea
                    value={template.legalReferences}
                    onChange={handleLegalReferencesChange}
                    className={`min-h-[100px] ${touched.legalReferences && fieldErrors.legalReferences ? 'border-red-500 ring-red-500' : ''}`}
                    placeholder='Enter legal references here...'
                  />
                  <FieldError
                    error={
                      touched.legalReferences
                        ? fieldErrors.legalReferences
                        : undefined
                    }
                  />
                </CardContent>
                <CardFooter className='flex justify-end space-x-3'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleSaveDraft}
                    disabled={
                      createTemplateMutation.isPending ||
                      updateTemplateMutation.isPending ||
                      !template.state ||
                      !template.type ||
                      !template.content
                    }
                  >
                    <Save className='mr-2 h-4 w-4' />
                    {createTemplateMutation.isPending ||
                    updateTemplateMutation.isPending
                      ? 'Saving...'
                      : 'Save as Draft'}
                  </Button>

                  <Button
                    variant='secondary'
                    size='sm'
                    onClick={handleSubmitForReview}
                    disabled={
                      createTemplateMutation.isPending ||
                      updateTemplateMutation.isPending ||
                      !isFormValid() ||
                      true
                    }
                    className={
                      !isFormValid() ? 'opacity-50 cursor-not-allowed' : ''
                    }
                  >
                    <Send className='mr-2 h-4 w-4' />
                    {createTemplateMutation.isPending ||
                    updateTemplateMutation.isPending
                      ? 'Submitting...'
                      : 'Submit for Review'}
                  </Button>

                  <Button
                    variant='default'
                    size='sm'
                    onClick={handlePublish}
                    disabled={
                      createTemplateMutation.isPending ||
                      updateTemplateMutation.isPending ||
                      !isFormValid()
                    }
                    className={`${!isFormValid() ? 'opacity-50 cursor-not-allowed' : ''} bg-green-600 hover:bg-green-700`}
                  >
                    <Upload className='mr-2 h-4 w-4' />
                    {createTemplateMutation.isPending ||
                    updateTemplateMutation.isPending
                      ? isNewTemplate
                        ? 'Publishing...'
                        : 'Updating...'
                      : 'Save and Publish'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value='preview'>
              <div className='grid  lg:grid-cols-3 gap-6'>
                {/* Live Preview */}
                <Card className='lg:col-span-2'>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Eye className='h-5 w-5' />
                      Live Preview
                    </CardTitle>
                    <CardDescription>
                      Real-time preview with sample data substitution
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-4'>
                      {/* Preview Controls */}
                      <div className='space-y-3'>
                        <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                          <div className='flex items-center gap-4'>
                            <span className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                              Preview Mode:
                            </span>
                            <Select
                              value={previewMode}
                              onValueChange={value =>
                                setPreviewMode(value as 'formatted' | 'raw')
                              }
                            >
                              <SelectTrigger className='w-32'>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='formatted'>
                                  Formatted
                                </SelectItem>
                                <SelectItem value='raw'>Raw Text</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className='text-xs text-[var(--custom-gray-medium)]'>
                            {template.content
                              ? `${template.content.length} characters`
                              : 'No content'}
                          </div>
                        </div>

                        {/* Document Structure Info */}
                        <div className='p-3 bg-blue-50 rounded-lg'>
                          <h4 className='text-sm font-medium text-blue-900 mb-2'>
                            Document Structure
                          </h4>
                          <div className='space-y-1 text-xs'>
                            {template.startPageId ? (
                              <div className='flex items-center gap-2'>
                                {isLoadingStartPage ? (
                                  <div className='animate-spin rounded-full h-3 w-3 border-b border-blue-600'></div>
                                ) : (
                                  <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                                )}
                                <span className='text-blue-700'>
                                  Start Page:{' '}
                                  {additionalTemplates.find(
                                    t => t.id === template.startPageId
                                  )?.templateName || 'Loading...'}
                                </span>
                              </div>
                            ) : (
                              <div className='flex items-center gap-2'>
                                <div className='w-3 h-3 bg-gray-300 rounded-full'></div>
                                <span className='text-gray-600'>
                                  No start page selected
                                </span>
                              </div>
                            )}

                            <div className='flex items-center gap-2'>
                              <div className='w-3 h-3 bg-blue-500 rounded-full'></div>
                              <span className='text-blue-700 font-medium'>
                                Main Template Content
                              </span>
                            </div>

                            {template.endPageId ? (
                              <div className='flex items-center gap-2'>
                                {isLoadingEndPage ? (
                                  <div className='animate-spin rounded-full h-3 w-3 border-b border-blue-600'></div>
                                ) : (
                                  <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                                )}
                                <span className='text-blue-700'>
                                  End Page:{' '}
                                  {additionalTemplates.find(
                                    t => t.id === template.endPageId
                                  )?.templateName || 'Loading...'}
                                </span>
                              </div>
                            ) : (
                              <div className='flex items-center gap-2'>
                                <div className='w-3 h-3 bg-gray-300 rounded-full'></div>
                                <span className='text-gray-600'>
                                  No end page selected
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Preview Content */}
                      <div className='border rounded-lg bg-background'>
                        <div className='border-b p-3 bg-gray-50'>
                          <div className='flex items-center justify-between'>
                            <span className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                              Document Preview
                            </span>
                            <div className='flex items-center gap-2 text-xs text-[var(--custom-gray-medium)]'>
                              <span>
                                Last updated: {new Date().toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className='p-6 min-h-[500px] max-h-[600px] overflow-y-auto'>
                          {previewMode === 'formatted' ? (
                            <div
                              className='prose prose-sm max-w-none font-serif text-sm leading-relaxed'
                              dangerouslySetInnerHTML={{
                                __html: getPreviewContent(),
                              }}
                            />
                          ) : (
                            <div className='whitespace-pre-wrap font-mono text-sm leading-relaxed'>
                              {getPlainTextPreview()}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Preview Statistics */}
                      <div className='grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg text-center'>
                        <div>
                          <div className='text-lg font-semibold text-[var(--custom-gray-dark)]'>
                            {Object.keys(previewData).length}
                          </div>
                          <div className='text-xs text-[var(--custom-gray-medium)]'>
                            Placeholders
                          </div>
                        </div>
                        <div>
                          <div className='text-lg font-semibold text-[var(--custom-gray-dark)]'>
                            {(() => {
                              const totalContent = [
                                startPageContent,
                                template.content,
                                endPageContent,
                              ]
                                .filter(Boolean)
                                .join('\n');
                              return totalContent
                                ? totalContent.split('\n').length
                                : 0;
                            })()}
                          </div>
                          <div className='text-xs text-[var(--custom-gray-medium)]'>
                            Total Lines
                          </div>
                        </div>
                        <div>
                          <div className='text-lg font-semibold text-[var(--custom-gray-dark)]'>
                            {(() => {
                              const totalContent = [
                                startPageContent,
                                template.content,
                                endPageContent,
                              ]
                                .filter(Boolean)
                                .join('');
                              const basePages = totalContent
                                ? Math.ceil(totalContent.length / 500)
                                : 0;
                              // Add extra pages for start/end pages if they exist
                              const additionalPages =
                                (startPageContent ? 1 : 0) +
                                (endPageContent ? 1 : 0);
                              return Math.max(basePages, additionalPages);
                            })()}
                          </div>
                          <div className='text-xs text-[var(--custom-gray-medium)]'>
                            Est. Pages
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className='lg:col-span-1'>
                  {/* Preview Data Editor */}
                  <CardHeader>
                    <CardTitle className='text-lg'>Preview Data</CardTitle>
                    <CardDescription>
                      Edit sample data for preview
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-3 max-h-[600px] overflow-y-auto'>
                      {Object.entries(previewData).map(
                        ([placeholder, value]) => {
                          const placeholderInfo = placeholders.find(
                            p => p.value === placeholder
                          );
                          return (
                            <div key={placeholder}>
                              <label className='text-xs font-medium text-[var(--custom-gray-dark)] block mb-1'>
                                {placeholderInfo?.label || placeholder}
                              </label>
                              <Input
                                value={value}
                                onChange={e =>
                                  setPreviewData(prev => ({
                                    ...prev,
                                    [placeholder]: e.target.value,
                                  }))
                                }
                                className='text-sm'
                                placeholder='Enter value'
                              />
                              <span className='text-xs text-[var(--custom-gray-medium)] font-mono'>
                                {placeholder}
                              </span>
                            </div>
                          );
                        }
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

export default function TemplateEditPage() {
  return (
    <ToastProvider>
      <TemplateEditPageContent />
    </ToastProvider>
  );
}
