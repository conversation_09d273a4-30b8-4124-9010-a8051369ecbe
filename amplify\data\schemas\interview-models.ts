import { a } from '@aws-amplify/backend';

export const InterviewSet = a
  .model({
    name: a.string().required(),
    description: a.string(),
    isActive: a.boolean().default(true),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    versions: a.hasMany('InterviewSetVersion', 'interviewSetId'),
    progresses: a.hasMany('UserInterviewProgress', 'interviewSetId'),
  })
  .authorization(allow => [
    allow.guest(),
    allow.authenticated(),
    allow.owner(),
  ]);

export const InterviewSetVersion = a
  .model({
    versionNumber: a.integer().required(),
    interviewSetId: a.id().required(),
    interviewSet: a.belongsTo('InterviewSet', 'interviewSetId'),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    isActive: a.boolean().default(true),
    questions: a.hasMany('InterviewQuestion', 'versionId'),
  })
  .authorization(allow => [
    allow.guest(),
    allow.authenticated(),
    allow.owner(),
  ]);

export const InterviewQuestion = a
  .model({
    questionTitle: a.string().required(),
    questionDescription: a.string(),
    type: a.string().required(),
    options: a.json(),
    order: a.integer().required(),
    isActive: a.boolean().default(true),
    versionId: a.id().required(),
    version: a.belongsTo('InterviewSetVersion', 'versionId'),
  })
  .authorization(allow => [
    allow.guest(),
    allow.authenticated(),
    allow.owner(),
  ]);

export const UserInterviewProgress = a
  .model({
    userId: a.string().required(),
    interviewSetId: a.id().required(),
    versionId: a.id().required(),
    currentQuestionId: a.string(),
    completedQuestionIds: a.json(),
    answers: a.json(),
    isComplete: a.boolean().default(false),
    lastUpdated: a.datetime(),
    interviewSet: a.belongsTo('InterviewSet', 'interviewSetId'),
  })
  .identifier(['userId', 'interviewSetId', 'versionId'])
  .authorization(allow => [allow.owner()]);
