'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DocumentActivity, DocumentActivityType, DocumentType } from './types';

interface DocumentActivityChartProps {
  activities: DocumentActivity[];
  period?: 'week' | 'month' | 'year';
}

export function DocumentActivityChart({
  activities,
  period = 'month',
}: DocumentActivityChartProps) {
  // Calculate summary statistics
  const calculateSummary = () => {
    const summary = {
      totalActivities: activities.length,
      createdCount: 0,
      updatedCount: 0,
      viewedCount: 0,
      downloadedCount: 0,
      sharedCount: 0,
      deletedCount: 0,
      byDocumentType: {} as Record<DocumentType, number>,
      byUser: {} as Record<string, number>,
      byDay: {} as Record<string, number>,
    };

    // Initialize document type counts
    const documentTypes: DocumentType[] = [
      'will',
      'trust',
      'poa-healthcare',
      'poa-financial',
      'advance-directive',
      'other',
    ];
    documentTypes.forEach(type => {
      summary.byDocumentType[type] = 0;
    });

    // Process each activity
    activities.forEach(activity => {
      // Count by activity type
      switch (activity.activityType) {
        case 'created':
          summary.createdCount++;
          break;
        case 'updated':
          summary.updatedCount++;
          break;
        case 'viewed':
          summary.viewedCount++;
          break;
        case 'downloaded':
          summary.downloadedCount++;
          break;
        case 'shared':
          summary.sharedCount++;
          break;
        case 'deleted':
          summary.deletedCount++;
          break;
      }

      // Count by document type
      summary.byDocumentType[activity.documentType]++;

      // Count by user
      if (!summary.byUser[activity.userId]) {
        summary.byUser[activity.userId] = 0;
      }
      summary.byUser[activity.userId]++;

      // Count by day
      const date = new Date(activity.timestamp);
      const dayKey = date.toISOString().split('T')[0];
      if (!summary.byDay[dayKey]) {
        summary.byDay[dayKey] = 0;
      }
      summary.byDay[dayKey]++;
    });

    return summary;
  };

  const summary = calculateSummary();

  // Format document type for display
  const formatDocumentType = (type: string) => {
    return type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Get percentage for a value
  const getPercentage = (value: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  };

  // Get color for activity type
  const getActivityTypeColor = (type: DocumentActivityType) => {
    switch (type) {
      case 'created':
        return 'bg-green-500';
      case 'updated':
        return 'bg-blue-500';
      case 'viewed':
        return 'bg-purple-500';
      case 'downloaded':
        return 'bg-amber-500';
      case 'shared':
        return 'bg-indigo-500';
      case 'deleted':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get color for document type
  const getDocumentTypeColor = (type: DocumentType) => {
    switch (type) {
      case 'will':
        return 'bg-blue-2157c';
      case 'trust':
        return 'bg-green-2010c';
      case 'poa-healthcare':
        return 'bg-purple-500';
      case 'poa-financial':
        return 'bg-amber-500';
      case 'advance-directive':
        return 'bg-indigo-500';
      case 'other':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
      {/* Activity Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Type Distribution</CardTitle>
          <CardDescription>
            Breakdown of document activities by type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {/* Created */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-green-500 mr-2'></div>
                  <span>Created</span>
                </div>
                <span className='font-medium'>{summary.createdCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-green-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.createdCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>

            {/* Updated */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-blue-500 mr-2'></div>
                  <span>Updated</span>
                </div>
                <span className='font-medium'>{summary.updatedCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-blue-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.updatedCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>

            {/* Viewed */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-purple-500 mr-2'></div>
                  <span>Viewed</span>
                </div>
                <span className='font-medium'>{summary.viewedCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-purple-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.viewedCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>

            {/* Downloaded */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-amber-500 mr-2'></div>
                  <span>Downloaded</span>
                </div>
                <span className='font-medium'>{summary.downloadedCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-amber-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.downloadedCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>

            {/* Shared */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-indigo-500 mr-2'></div>
                  <span>Shared</span>
                </div>
                <span className='font-medium'>{summary.sharedCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-indigo-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.sharedCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>

            {/* Deleted */}
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-4 h-4 rounded-full bg-red-500 mr-2'></div>
                  <span>Deleted</span>
                </div>
                <span className='font-medium'>{summary.deletedCount}</span>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div
                  className='bg-red-500 h-2 rounded-full'
                  style={{
                    width: `${getPercentage(summary.deletedCount, summary.totalActivities)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Document Type Distribution</CardTitle>
          <CardDescription>
            Breakdown of activities by document type
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {Object.entries(summary.byDocumentType)
              .filter(([_, count]) => count > 0)
              .sort(([_, countA], [__, countB]) => countB - countA)
              .map(([type, count]) => (
                <div key={type} className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center'>
                      <div
                        className={`w-4 h-4 rounded-full ${getDocumentTypeColor(type as DocumentType)} mr-2`}
                      ></div>
                      <span>{formatDocumentType(type)}</span>
                    </div>
                    <span className='font-medium'>{count}</span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-2'>
                    <div
                      className={`${getDocumentTypeColor(type as DocumentType)} h-2 rounded-full`}
                      style={{
                        width: `${getPercentage(count, summary.totalActivities)}%`,
                      }}
                    ></div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
