// Subscription Tier Types
export type SubscriptionTier = 'Basic' | 'Enterprise';

// Billing Cycle Types
export type BillingCycle = 'Monthly' | 'Annual';

// Payment Method Types
export type PaymentMethodType = 'Credit Card' | 'Debit Card' | 'ACH';

// Subscription Status Types
export type SubscriptionStatus =
  | 'Active'
  | 'Canceled'
  | 'Delinquent'
  | 'Suspended'
  | 'In Trust';

// Transaction Status Types
export type TransactionStatus =
  | 'Successful'
  | 'Failed'
  | 'Pending'
  | 'Refunded';

// Discount Types
export type DiscountType = 'Percentage' | 'Fixed';

// Payment Method Interface
export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  lastFour: string;
  expiryDate?: string; // Format: MM/YY
  isDefault: boolean;
  cardBrand?: string; // Visa, MasterCard, etc.
  bankName?: string; // For ACH
}

// Subscription Interface
export interface Subscription {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  billingCycle: BillingCycle;
  status: SubscriptionStatus;
  startDate: string;
  endDate?: string;
  nextBillingDate: string;
  amount: number;
  paymentMethodId: string;
  discount?: {
    type: DiscountType;
    value: number;
    code: string;
    expiryDate?: string;
  };
  cancelReason?: string;
  pauseReason?: string;
  pauseUntil?: string;
}

// Transaction Interface
export interface Transaction {
  id: string;
  userId: string;
  subscriptionId: string;
  date: string;
  amount: number;
  status: TransactionStatus;
  paymentMethodId: string;
  description: string;
  receiptUrl?: string;
  failureReason?: string;
  retryCount?: number;
}

// User Billing Information Interface
export interface UserBillingInfo {
  userId: string;
  subscription?: Subscription;
  paymentMethods: PaymentMethod[];
  transactions: Transaction[];
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

// Admin Billing Statistics Interface
export interface AdminBillingStats {
  totalActiveSubscriptions: number;
  totalRevenue: number;
  revenueThisMonth: number;
  delinquentAccounts: number;
  newSubscriptionsThisMonth: number;
  canceledSubscriptionsThisMonth: number;
}

// Pricing Information Interface
export interface PricingInfo {
  tier: SubscriptionTier;
  monthlyPrice: number;
  annualPrice: number;
  features: string[];
  isPopular?: boolean;
}
