'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import {
  BookOpen,
  Video,
  FileText,
  Search,
  Clock,
  Star,
  Play,
  Download,
  Eye,
  Filter,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  Bookmark,
  Volume2,
  VolumeX,
  Settings,
  Maximize,
  SkipBack,
  SkipForward,
  Pause,
} from 'lucide-react';

// Mock educational content data
const mockContent = {
  videos: [
    {
      id: '1',
      title: 'Estate Planning Basics for Childfree Individuals',
      description:
        "Learn the fundamentals of estate planning when you don't have children",
      duration: '15 min',
      category: 'Wills',
      tags: ['onboarding', 'basics'],
      rating: 4.8,
      thumbnail: '/api/placeholder/300/200',
      views: 1250,
      completed: false,
      hasSubtitles: true,
      hasAudioDescription: true,
      videoUrl: 'https://example.com/video1',
    },
    {
      id: '2',
      title: 'Creating Your Will: Step by Step Guide',
      description: 'A comprehensive guide to creating your first will',
      duration: '22 min',
      category: 'Wills',
      tags: ['documents', 'step-by-step'],
      rating: 4.9,
      thumbnail: '/api/placeholder/300/200',
      views: 980,
      completed: true,
      hasSubtitles: true,
      hasAudioDescription: false,
      videoUrl: 'https://example.com/video2',
    },
    {
      id: '3',
      title: 'Understanding Trusts and Their Benefits',
      description: 'When and why you might need a trust in your estate plan',
      duration: '18 min',
      category: 'Trusts',
      tags: ['advanced', 'trust'],
      rating: 4.7,
      thumbnail: '/api/placeholder/300/200',
      views: 756,
      completed: false,
      hasSubtitles: true,
      hasAudioDescription: true,
      videoUrl: 'https://example.com/video3',
    },
    {
      id: '4',
      title: 'Power of Attorney Explained',
      description:
        'Understanding different types of power of attorney and when to use them',
      duration: '12 min',
      category: 'Power of Attorney',
      tags: ['legal', 'power-of-attorney'],
      rating: 4.6,
      thumbnail: '/api/placeholder/300/200',
      views: 892,
      completed: false,
      hasSubtitles: true,
      hasAudioDescription: true,
      videoUrl: 'https://example.com/video4',
    },
  ],
  articles: [
    {
      id: '1',
      title: '10 Common Estate Planning Mistakes to Avoid',
      description:
        'Learn about the most frequent errors people make in estate planning',
      readTime: '8 min read',
      category: 'Wills',
      tags: ['mistakes', 'tips'],
      author: 'Sarah Johnson, Estate Attorney',
      publishDate: '2025-01-15',
      content: 'Full article content here...',
      tableOfContents: [
        'Introduction',
        'Mistake 1: Not Having a Will',
        'Mistake 2: Outdated Beneficiaries',
        'Conclusion',
      ],
      bookmarked: false,
      rating: 4.7,
    },
    {
      id: '2',
      title: 'Digital Assets: What Happens to Your Online Life?',
      description:
        'How to handle social media, crypto, and digital accounts in your estate plan',
      readTime: '12 min read',
      category: 'Trusts',
      tags: ['digital', 'modern'],
      author: 'Michael Chen, Digital Estate Specialist',
      publishDate: '2025-01-10',
      content: 'Full article content here...',
      tableOfContents: [
        'Digital Asset Overview',
        'Social Media Accounts',
        'Cryptocurrency',
        'Best Practices',
      ],
      bookmarked: true,
      rating: 4.9,
    },
    {
      id: '3',
      title: 'Power of Attorney: Your Safety Net',
      description: 'Understanding when and how to grant power of attorney',
      readTime: '10 min read',
      category: 'Power of Attorney',
      tags: ['legal', 'protection'],
      author: 'Emma Rodriguez, Financial Planner',
      publishDate: '2025-01-05',
      content: 'Full article content here...',
      tableOfContents: [
        'What is Power of Attorney',
        'Types of POA',
        'Choosing an Agent',
        'Legal Requirements',
      ],
      bookmarked: false,
      rating: 4.8,
    },
  ],
  infographics: [
    {
      id: '1',
      title: 'Estate Planning Timeline',
      description:
        'Visual guide to when you should complete different estate planning tasks',
      category: 'Wills',
      tags: ['timeline', 'visual'],
      imageUrl: '/api/placeholder/800/600',
      downloadUrl: '/api/placeholder/download/timeline.pdf',
      views: 2340,
    },
    {
      id: '2',
      title: 'Trust Types Comparison',
      description: 'Compare different types of trusts and their benefits',
      category: 'Trusts',
      tags: ['comparison', 'visual'],
      imageUrl: '/api/placeholder/800/600',
      downloadUrl: '/api/placeholder/download/trusts.pdf',
      views: 1890,
    },
    {
      id: '3',
      title: 'Power of Attorney Decision Tree',
      description: 'Flow chart to help you decide what type of POA you need',
      category: 'Power of Attorney',
      tags: ['decision', 'flowchart'],
      imageUrl: '/api/placeholder/800/600',
      downloadUrl: '/api/placeholder/download/poa.pdf',
      views: 1560,
    },
  ],
  guides: [
    {
      id: '1',
      title: 'Complete Estate Planning Checklist',
      description:
        "A comprehensive checklist to ensure you haven't missed anything",
      pages: 12,
      category: 'Checklist',
      downloadCount: 2340,
    },
    {
      id: '2',
      title: 'Emergency Contact Information Template',
      description: 'Organize all your important contacts in one place',
      pages: 4,
      category: 'Templates',
      downloadCount: 1890,
    },
    {
      id: '3',
      title: 'Asset Inventory Worksheet',
      description: 'Track all your assets for estate planning purposes',
      pages: 8,
      category: 'Worksheets',
      downloadCount: 1560,
    },
  ],
};

export default function EducationalContentPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('videos');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredContent, setFilteredContent] = useState(mockContent);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [selectedArticle, setSelectedArticle] = useState<any>(null);
  const [selectedInfographic, setSelectedInfographic] = useState<any>(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [showArticleReader, setShowArticleReader] = useState(false);
  const [showInfographicModal, setShowInfographicModal] = useState(false);

  const categories = ['All', 'Wills', 'Trusts', 'Power of Attorney'];

  useEffect(() => {
    filterContent();
  }, [searchTerm, selectedCategory]);

  const filterContent = () => {
    let filtered = { ...mockContent };

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered.videos = mockContent.videos.filter(
        item => item.category === selectedCategory
      );
      filtered.articles = mockContent.articles.filter(
        item => item.category === selectedCategory
      );
      filtered.infographics = mockContent.infographics.filter(
        item => item.category === selectedCategory
      );
    }

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered.videos = filtered.videos.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower) ||
          item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
      filtered.articles = filtered.articles.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower) ||
          item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
      filtered.infographics = filtered.infographics.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower) ||
          item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    setFilteredContent(filtered);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    filterContent();
  };

  const handleRating = (
    contentId: string,
    type: string,
    isPositive: boolean
  ) => {
    console.log(
      `Rating ${contentId} (${type}): ${isPositive ? 'positive' : 'negative'}`
    );
    // In a real app, this would send to analytics
  };

  const toggleBookmark = (contentId: string, type: string) => {
    console.log(`Toggling bookmark for ${contentId} (${type})`);
    // In a real app, this would update user preferences
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold mb-4 text-[var(--custom-gray-dark)]'>
          Educational Content Library
        </h1>
        <p className='text-lg text-[var(--custom-gray-medium)] mb-6'>
          Expand your knowledge with our comprehensive library of estate
          planning resources designed for accessibility and ease of
          understanding
        </p>

        {/* Search and Filter Bar */}
        <div className='flex flex-col sm:flex-row gap-4 mb-6'>
          <form onSubmit={handleSearch} className='flex gap-2 flex-1 max-w-md'>
            <div className='relative flex-1'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--custom-gray-medium)] h-4 w-4' />
              <Input
                type='text'
                placeholder='Search videos, articles, guides...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <Button type='submit' variant='default' size='default'>
              Search
            </Button>
          </form>

          <div className='flex gap-2 items-center'>
            <Filter className='h-4 w-4 text-[var(--custom-gray-medium)]' />
            <select
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-[var(--eggplant)] focus:border-transparent'
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Content Stats */}
        <div className='flex gap-6 text-sm text-[var(--custom-gray-medium)] mb-6'>
          <span>{filteredContent.videos.length} Videos</span>
          <span>{filteredContent.articles.length} Articles</span>
          <span>{filteredContent.infographics.length} Infographics</span>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-4 mb-8'>
          <TabsTrigger value='videos' className='flex items-center gap-2'>
            <Video className='h-4 w-4' />
            Videos
          </TabsTrigger>
          <TabsTrigger value='articles' className='flex items-center gap-2'>
            <FileText className='h-4 w-4' />
            Articles
          </TabsTrigger>
          <TabsTrigger value='infographics' className='flex items-center gap-2'>
            <Eye className='h-4 w-4' />
            Infographics
          </TabsTrigger>
          <TabsTrigger value='guides' className='flex items-center gap-2'>
            <BookOpen className='h-4 w-4' />
            Guides & Templates
          </TabsTrigger>
        </TabsList>

        {/* Videos Tab */}
        <TabsContent value='videos'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent.videos.map(video => (
              <Card
                key={video.id}
                className='overflow-hidden hover:shadow-lg transition-shadow'
              >
                <div className='relative'>
                  <div className='w-full h-48 bg-gray-200 flex items-center justify-center'>
                    <Play className='h-12 w-12 text-[var(--custom-gray-medium)]' />
                  </div>
                  <Badge className='absolute top-2 right-2 bg-black/70 text-white'>
                    {video.duration}
                  </Badge>
                  {video.completed && (
                    <Badge className='absolute top-2 left-2 bg-green-600 text-white'>
                      ✓ Completed
                    </Badge>
                  )}
                  <div className='absolute bottom-2 left-2 flex gap-1'>
                    {video.hasSubtitles && (
                      <Badge variant='secondary' className='text-xs'>
                        CC
                      </Badge>
                    )}
                    {video.hasAudioDescription && (
                      <Badge variant='secondary' className='text-xs'>
                        AD
                      </Badge>
                    )}
                  </div>
                </div>
                <CardHeader className='pb-2'>
                  <div className='flex justify-between items-start mb-2'>
                    <Badge variant='outline'>{video.category}</Badge>
                    <div className='flex items-center gap-1'>
                      <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
                      <span className='text-sm'>{video.rating}</span>
                    </div>
                  </div>
                  <CardTitle className='text-lg'>{video.title}</CardTitle>
                  <CardDescription>{video.description}</CardDescription>
                  <div className='flex gap-1 mt-2'>
                    {video.tags.map(tag => (
                      <Badge key={tag} variant='outline' className='text-xs'>
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='flex justify-between items-center mb-3'>
                    <div className='flex items-center gap-1 text-sm text-[var(--custom-gray-medium)]'>
                      <Eye className='h-4 w-4' />
                      {video.views} views
                    </div>
                    <Button
                      variant='default'
                      size='sm'
                      onClick={() => {
                        setSelectedVideo(video);
                        setShowVideoPlayer(true);
                      }}
                    >
                      <Play className='h-4 w-4 mr-1' />
                      Watch
                    </Button>
                  </div>
                  <div className='flex justify-between items-center'>
                    <div className='flex gap-2'>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleRating(video.id, 'video', true)}
                        className='p-1 h-8 w-8'
                      >
                        <ThumbsUp className='h-4 w-4' />
                      </Button>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => handleRating(video.id, 'video', false)}
                        className='p-1 h-8 w-8'
                      >
                        <ThumbsDown className='h-4 w-4' />
                      </Button>
                    </div>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => toggleBookmark(video.id, 'video')}
                      className='p-1 h-8 w-8'
                    >
                      <Bookmark className='h-4 w-4' />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Articles Tab */}
        <TabsContent value='articles'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {mockContent.articles.map(article => (
              <Card
                key={article.id}
                className='hover:shadow-lg transition-shadow'
              >
                <CardHeader>
                  <div className='flex justify-between items-start mb-2'>
                    <Badge variant='outline'>{article.category}</Badge>
                    <div className='flex items-center gap-1 text-sm text-[var(--custom-gray-medium)]'>
                      <Clock className='h-4 w-4' />
                      {article.readTime}
                    </div>
                  </div>
                  <CardTitle className='text-xl'>{article.title}</CardTitle>
                  <CardDescription className='text-base'>
                    {article.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='flex justify-between items-center'>
                    <div className='text-sm text-[var(--custom-gray-medium)]'>
                      <p>By {article.author}</p>
                      <p>
                        {new Date(article.publishDate).toLocaleDateString()}
                      </p>
                    </div>
                    <Button variant='default' size='sm'>
                      Read Article
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Guides Tab */}
        <TabsContent value='guides'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {mockContent.guides.map(guide => (
              <Card
                key={guide.id}
                className='hover:shadow-lg transition-shadow'
              >
                <CardHeader>
                  <div className='flex justify-between items-start mb-2'>
                    <Badge variant='outline'>{guide.category}</Badge>
                    <div className='text-sm text-[var(--custom-gray-medium)]'>
                      {guide.pages} pages
                    </div>
                  </div>
                  <CardTitle className='text-lg'>{guide.title}</CardTitle>
                  <CardDescription>{guide.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-1 text-sm text-[var(--custom-gray-medium)]'>
                      <Download className='h-4 w-4' />
                      {guide.downloadCount} downloads
                    </div>
                    <Button variant='default' size='sm'>
                      <Download className='h-4 w-4 mr-1' />
                      Download
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
