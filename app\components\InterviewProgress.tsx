'use client';

import { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { CheckCircle2, ArrowR<PERSON> } from 'lucide-react';
import Link from 'next/link';
import routes from '@/utils/routes';
import {
  getUserInterviewProgress,
  getInterviewQuestions,
} from '@/app/utils/userInterviewProgress';
import type { Schema } from '@/amplify/data/resource';

interface InterviewProgressProps {
  onContinue?: () => void;
}

export default function InterviewProgress({
  onContinue,
}: InterviewProgressProps) {
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState<
    Schema['UserInterviewProgress']['type'] | null
  >(null);
  const [questions, setQuestions] = useState<
    Schema['InterviewQuestion']['type'][]
  >([]);
  const [error, setError] = useState('');

  useEffect(() => {
    async function fetchData() {
      try {
        const [interviewProgress, interviewQuestions] = await Promise.all([
          getUserInterviewProgress(),
          getInterviewQuestions(),
        ]);

        setProgress(interviewProgress);
        setQuestions(interviewQuestions);
      } catch (err) {
        console.error('Error fetching interview progress:', err);
        setError('Failed to load interview progress');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading interview progress...</CardTitle>
          <CardDescription>
            Please wait while we load your information
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Something went wrong</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // If there are no questions, don't show the component
  if (questions.length === 0) {
    return null;
  }

  // Calculate progress percentage
  const completedCount = Array.isArray(progress?.completedQuestionIds)
    ? progress.completedQuestionIds.length
    : 0;
  const totalQuestions = questions.length;
  const progressPercentage =
    totalQuestions > 0 ? (completedCount / totalQuestions) * 100 : 0;

  // If interview is complete, show completion message
  if (progress?.isComplete) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Interview Complete</CardTitle>
          <CardDescription>
            You have completed all interview questions
          </CardDescription>
        </CardHeader>
        <CardContent className='flex items-center justify-center py-6'>
          <CheckCircle2 className='h-12 w-12 text-primary' />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Interview Progress</CardTitle>
        <CardDescription>
          {completedCount === 0
            ? 'Start your interview to help us understand your needs'
            : `You've completed ${completedCount} of ${totalQuestions} questions`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>Progress</span>
              <span>{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className='h-2' />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild className='w-full'>
          <Link href={routes.interview}>
            {completedCount === 0 ? 'Start Interview' : 'Continue Interview'}
            <ArrowRight className='ml-2 h-4 w-4' />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
