# 10 \- Notifications System

# **Document 10 \- Functional Specification: Notifications System**

## **Overview**

The Notifications System ensures that users of the Childfree Legacy Web Application receive timely updates about critical estate planning events—such as document updates, emergencies, missed payments, and policy changes—while also supporting administrators with notifications for quarterly review periods and sign-off deadlines. Built on AWS Amplify, this system leverages serverless architecture to trigger, deliver, and track notifications across email, SMS, and in-app channels. It prioritizes security (HIPAA and SOC2 compliance), user customization, and accessibility, fostering trust and enabling users (especially those aged 65+) and administrators to manage their responsibilities with clear, actionable alerts.

---

## **1\. Notification Types**

Notifications are categorized by purpose and urgency to ensure appropriate delivery and response.

### **Key Requirements**

#### **Notification Categories**

- **Informational**: Routine updates like document status changes, policy updates, or quarterly newsletters.
- **Action Required**: Prompts for user or admin actions, e.g., reviewing updated documents, confirming check-ins, or completing quarterly template reviews.
- **Urgent**: Critical alerts requiring immediate attention, e.g., emergency access granted or missed payments.

#### **Priority Levels**

- **Low**: Routine updates (e.g., newsletters).
- **Medium**: Time-sensitive tasks (e.g., document review reminders, quarterly review tasks).
- **High**: Urgent alerts (e.g., payment failures, emergencies).

#### **Customization**

- Users can opt in/out of non-critical notifications via settings.
- Urgent notifications are mandatory and non-configurable.
- Administrators can manage notification lists using **AWS AppSync** GraphQL mutations.

### **Compliance Considerations**

- **User Consent**: Track consent for non-urgent notifications in **Amazon DynamoDB**.
- **Accessibility**: Ensure notifications are concise, use simple language, and support large text for accessibility.

---

## **2\. Delivery Methods**

The system supports multiple channels to deliver notifications effectively, with flexibility based on user preferences and urgency.

### **Key Requirements**

#### **Delivery Channels**

- **Email**: Detailed notifications sent via **Amazon Simple Email Service (SES)**.
- **SMS**: Urgent or time-sensitive alerts delivered via **Amazon Simple Notification Service (SNS)**.
- **In-App**: Real-time alerts in the dashboard using **AWS AppSync subscriptions**.

#### **User Preferences**

- Users and administrators configure preferences in /settings/notifications, stored in **DynamoDB**.
- Default to email if no preference is specified.

#### **Fallback Mechanisms**

- For high-priority notifications, retry via SMS if email fails, managed by **AWS Lambda**.
- Log failures in **Amazon CloudWatch** for admin review.

### **User Flow for Managing Preferences**

1. Navigate to /settings/notifications (or /admin/settings/notifications for admins).
2. View a table listing notification categories with checkboxes for email, SMS, and in-app options.
3. Select preferences and click "Save Preferences" to update **DynamoDB** via **AWS AppSync**.

### **Edge Cases**

- **Undeliverable Notifications**: Flag accounts for manual follow-up if all channels fail.
- **Duplicate Prevention**: Coordinate multi-channel sends with **AWS Step Functions**.

### **Compliance Considerations**

- **Data Privacy**: Encrypt content in transit with TLS; limit sensitive data in SMS.
- **Opt-Out**: Include opt-out links/options for non-essential notifications.

### **UI Components**

| Element                        | Description                                 |
| ------------------------------ | ------------------------------------------- |
| Notification Preferences Table | Displays categories and delivery options    |
| Channel Checkboxes             | Toggle email, SMS, in-app for each category |
| "Save Preferences" Button      | Commits changes to user settings            |

---

## **3\. Triggers and Events**

Notifications are triggered by specific events, ensuring timely communication of critical updates or actions.

### **Key Requirements**

#### **Event Triggers**

- **Document Updates**: New or revised documents available for review.
- **Document Status Changes**: Updates to estate document statuses.
- **Emergencies**: Emergency access granted or Dead Man’s Switch activated.
- **Missed Payments**: Payment failures or subscription risks.
- **Policy Changes**: Updates to platform policies or legal requirements.
- **Routine Check-Ins**: Periodic user status confirmations (e.g., Dead Man’s Switch).
- **Quarterly Review Period Start**: Alerts admins at quarter start with a list of templates to review.
- **Sign-Off Deadline Reminder**: Reminds admins at 7, 3, and 1 day(s) before template sign-off deadlines.

#### **Notification Content**

- **Subject Lines**: Clear and specific, e.g., "Action Required: Review Your Updated Will," "Quarterly Review Started."
- **Messages**: Concise, with links like /member/documents/review or /admin/quarterly-review.
- **Examples**:
  - Quarterly Review: "The quarterly review period has started. Review: \[template list\]."
  - Sign-Off Reminder: "Reminder: Sign-off for \[template list\] due by \[date\]."

#### **Timing**

- **Urgent**: Immediate delivery via **Amazon SNS**.
- **Low-Priority**: Batched delivery via **AWS Lambda** and **Amazon EventBridge**.
- **Scheduled**: Quarterly review and sign-off reminders via **EventBridge**.

### **User Flow for Triggered Notifications**

1. An event occurs (e.g., document update, quarterly review start).
2. **AWS Lambda** retrieves preferences from **DynamoDB**.
3. Notification is sent via selected channels using **SES**, **SNS**, or **AppSync**.
4. Recipient acts if required.

### **Edge Cases**

- **High Volume**: Batch similar notifications with **AWS Step Functions**.
- **Event Cancellation**: Cancel pending notifications (e.g., signed-off templates) via **Lambda**.

### **Compliance Considerations**

- **Accuracy**: Validate event and recipient data before sending.
- **Audit Trails**: Log triggers in **CloudWatch** for compliance.

---

## **4\. Tracking and Logging**

The system tracks delivery and interactions to ensure reliability and compliance.

### **Key Requirements**

#### **Delivery Tracking**

- Store status (sent, delivered, opened) in **DynamoDB**.
- Monitor email metrics (open/click rates) with **Amazon SES**.

#### **User Interactions**

- Log actions (e.g., link clicks) via **AWS AppSync**.

#### **Error Handling**

- Record failures in **CloudWatch** and retry with **AWS Lambda**.
- Notify admins of critical failures via **Amazon SNS**.

### **User Flow for Viewing Notification History**

1. Navigate to /member/notifications (or /admin/notifications for admins).
2. View a table of recent notifications with statuses from **DynamoDB**.
3. Click "View Details" for full content or "Mark as Read" to update via **AppSync**.

### **Edge Cases**

- **Spam Filters**: Use SES whitelisted domains to minimize filtering.
- **Inactivity**: Flag inactive accounts for follow-up via **Lambda**.

### **Compliance Considerations**

- **HIPAA**: Encrypt logs with **AWS Key Management Service (KMS)**.
- **SOC2**: Maintain detailed logs in **CloudWatch** for audits.

### **UI Components**

| Element                    | Description                                    |
| -------------------------- | ---------------------------------------------- |
| Notification History Table | Lists notifications with timestamps and status |
| "View Details" Button      | Displays full notification details             |
| "Mark as Read" Button      | Marks notification as acknowledged             |

---

## **5\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /settings/notifications: User preference settings.
  - /member/notifications: User notification history.
  - /admin/settings/notifications: Admin preference settings.
  - /admin/notifications: Admin notification history.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - sendNotification:
    - Input: { userId, eventType, message, priority }
    - Output: { success, notificationId }
  - getNotificationHistory:
    - Input: { userId }
    - Output: { notifications: \[{ id, eventType, timestamp, status }\] }
  - updateNotificationPreferences:
    - Input: { userId, preferences }
    - Output: { success }
  - sendQuarterlyReviewStart: Triggers quarterly review notifications.
  - sendSignOffReminder: Triggers sign-off reminders.
- **Notification Queue**: **Amazon SQS** for scheduling and managing sends.
- **Third-Party Integrations**:
  - Email: **Amazon SES**.
  - SMS: **Amazon SNS**.

### **Database (Amazon DynamoDB)**

- **notifications**:
  - Columns: id, user_id, event_type, message, priority, channel, status, timestamp.
- **notification_preferences**:
  - Columns: user_id, category, channels.

### **Encryption**

- Use TLS/SSL for delivery; encrypt logs with **AWS KMS**.

### **Logging**

- Track sends, deliveries, and interactions in **Amazon CloudWatch**.

---

## **6\. Testing and Validation**

- **Unit Tests**: Validate triggers and preferences with **AWS Amplify CLI**.
- **Integration Tests**: Confirm channel delivery via **AppSync** and **Lambda**.
- **Security Testing**: Protect data with **AWS Web Application Firewall (WAF)**.
- **User Acceptance Testing (UAT)**: Ensure clarity for users 65+ and admins.

### **Test Cases**

| Scenario               | Expected Outcome                          |
| ---------------------- | ----------------------------------------- |
| Document Update        | Email sent with review link               |
| Emergency Alert        | SMS delivered to user and contacts        |
| Missed Payment         | Urgent in-app notification displayed      |
| Preference Update      | Preferences updated successfully          |
| Notification History   | History accurately displayed              |
| Quarterly Review Start | Admins receive template list notification |
| Sign-Off Reminder      | Admins receive reminders at set intervals |

---

## **7\. Compliance and Security**

- **HIPAA**: Avoid unprotected health data in notifications.
- **SOC2**: Secure logs with **AWS IAM** controls.
- **Data Privacy**: Comply with CCPA via preference options.
- **Audit Trails**: Log events with timestamps in **CloudWatch**.

---

## **Summary**

The Notifications System, powered by AWS Amplify, delivers timely, customizable alerts for estate planning events and administrative tasks like quarterly reviews. With secure, multi-channel delivery (email, SMS, in-app) and robust tracking, it enhances user trust and meets HIPAA, SOC2, and accessibility standards, supporting both users and administrators effectively.
