'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  PlusCircle,
  Search,
  Filter,
  MessageSquare,
  Edit,
  Play,
  Clock,
  FileText,
  AlertTriangle,
} from 'lucide-react';
import {
  Interview,
  InterviewVersion,
  InterviewSetWithVersion,
} from '@/types/interview-builder';
import {
  getInterview,
  getVersions,
  deleteVersion,
  duplicateVersion,
  getInterviewsList,
} from '@/lib/api/interview-builder';
import { VersionListTable } from '@/components/dashboard/admin/interview-builder/version-list-table';
import { useQuery } from '@tanstack/react-query';
import { fetchDevices } from 'aws-amplify/auth';

export default function InterviewBuilderPage() {
  const router = useRouter();
  // const [interview, setInterview] = useState<Interview | null>(null);
  // const [versions, setVersions] = useState<InterviewVersion[]>([]);
  // const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const { data: interviewSets = [], isLoading: isLoadingInterviewSets } =
    useQuery({
      queryKey: ['question-sets'],
      queryFn: getInterviewsList,
    });

  console.log('===> INTERVIEW SETS', interviewSets);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalVersions, setTotalVersions] = useState(0);
  const itemsPerPage = 10;

  // useEffect(() => {
  //   loadData();
  // }, [currentPage, statusFilter]);
  //
  // const loadData = async () => {
  //   try {
  //     setLoading(true);
  //     setError(null);
  //
  //     // Load the single interview
  //     const interviewResponse = await getInterview();
  //
  //     setInterview(interviewResponse.interview);
  //
  //     // Load versions with pagination and filtering
  //     const versionsResponse = await getVersions(
  //       currentPage,
  //       itemsPerPage,
  //       statusFilter
  //     );
  //     setVersions(versionsResponse.versions);
  //     setTotalVersions(versionsResponse.total);
  //   } catch (err) {
  //     setError('Failed to load interview data. Please try again.');
  //     console.error('Error loading data:', err);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleCreateVersion = () => {
    router.push('/admin/interview-builder/create');
  };

  const handleBuildVersion = (version: InterviewSetWithVersion) => {
    router.push(`/admin/interview-builder/build/${version.id}`);
  };

  const handlePreviewVersion = (version: InterviewSetWithVersion) => {
    router.push(`/admin/interview-builder/preview/${version.id}`);
  };

  const handleDuplicateVersion = async (version: InterviewSetWithVersion) => {
    // try {
    //   setActionLoading(`duplicate-${version.id}`);
    //   const duplicated = await duplicateVersion(version.id);
    //   if (duplicated) {
    //     await loadData();
    //   }
    // } catch (err) {
    //   setError('Failed to duplicate version. Please try again.');
    //   console.error('Error duplicating version:', err);
    // } finally {
    //   setActionLoading(null);
    // }
  };

  const handleDeleteVersion = async (version: InterviewSetWithVersion) => {
    // if (
    //   !confirm(
    //     `Are you sure you want to delete "${version.name}" (Version ${version.version})? This action cannot be undone.`
    //   )
    // ) {
    //   return;
    // }
    //
    // try {
    //   setActionLoading(`delete-${version.id}`);
    //   const success = await deleteVersion(version.id);
    //   if (success) {
    //     await loadData();
    //   }
    // } catch (err) {
    //   setError('Failed to delete version. Please try again.');
    //   console.error('Error deleting version:', err);
    // } finally {
    //   setActionLoading(null);
    // }
  };

  const filteredInterviewSets = interviewSets.filter(
    version => version.name.toLowerCase().includes(searchTerm.toLowerCase())
    // version?.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: InterviewVersion['status']) => {
    switch (status) {
      case 'published':
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            Published
          </Badge>
        );
      case 'draft':
        return <Badge variant='secondary'>Draft</Badge>;
      case 'archived':
        return (
          <Badge variant='outline' className='text-[var(--custom-gray-medium)]'>
            Archived
          </Badge>
        );
      default:
        return <Badge variant='secondary'>{status}</Badge>;
    }
  };

  const totalPages = Math.ceil(totalVersions / itemsPerPage);

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
            <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
            Interview Builder - Version History
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            Manage interview versions with complete history tracking
          </p>
        </div>
        <Button onClick={handleCreateVersion} variant='default' size='sm'>
          <PlusCircle className='mr-2 h-4 w-4' />
          Create New Version
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className='grid md:grid-cols-4 gap-4 mb-6'>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center'>
              <FileText className='h-8 w-8 text-blue-500' />
              <div className='ml-3'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Total Versions
                </p>
                <p className='text-2xl font-bold'>{interviewSets.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center'>
              <Play className='h-8 w-8 text-green-500' />
              <div className='ml-3'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Published
                </p>
                <p className='text-2xl font-bold'>
                  {/*{versions.filter(v => v.status === 'published').length}*/}0
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center'>
              <Edit className='h-8 w-8 text-yellow-500' />
              <div className='ml-3'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Drafts
                </p>
                <p className='text-2xl font-bold'>
                  {/*{versions.filter(v => v.status === 'draft').length}*/}0
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center'>
              <Clock className='h-8 w-8 text-purple-500' />
              <div className='ml-3'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Current Version
                </p>
                <p className='text-2xl font-bold'>
                  {/*{interview?.currentVersion || 0}*/}0
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className='mb-6'>
        <CardContent className='p-4'>
          <div className='flex flex-col sm:flex-row gap-4'>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--custom-gray-medium)] h-4 w-4' />
                <Input
                  placeholder='Search interviews...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-40'>
                  <Filter className='mr-2 h-4 w-4' />
                  <SelectValue placeholder='Filter by status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='published'>Published</SelectItem>
                  <SelectItem value='draft'>Draft</SelectItem>
                  <SelectItem value='archived'>Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Version List */}
      <Card>
        <CardHeader>
          <CardTitle>Interview Versions</CardTitle>
          <CardDescription>
            Manage interview versions with complete history tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VersionListTable
            versions={filteredInterviewSets}
            loading={isLoadingInterviewSets}
            actionLoading={actionLoading}
            onEdit={handleBuildVersion}
            onBuild={handleBuildVersion}
            onPreview={handlePreviewVersion}
            onDuplicate={handleDuplicateVersion}
            onDelete={handleDeleteVersion}
            getStatusBadge={getStatusBadge}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </CardContent>
      </Card>

      {/* Removed dialog - using pages now */}
    </div>
  );
}
