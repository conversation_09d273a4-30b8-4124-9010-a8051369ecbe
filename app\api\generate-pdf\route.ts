import { NextResponse } from 'next/server';
import puppeteer, { PDFOptions, Browser } from 'puppeteer';

interface PDFGenerationRequest {
  html: string;
  options?: {
    filename?: string;
    format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
    orientation?: 'portrait' | 'landscape';
    margin?: {
      top?: string;
      right?: string;
      bottom?: string;
      left?: string;
    };
    printBackground?: boolean;
    scale?: number;
    displayHeaderFooter?: boolean;
    headerTemplate?: string;
    footerTemplate?: string;
  };
  css?: string; // Optional CSS to inject
}

export async function POST(request: Request) {
  let browser: Browser | null = null;

  try {
    const body: PDFGenerationRequest = await request.json();
    const { html, options = {}, css } = body;

    // Validate required fields
    if (!html || typeof html !== 'string') {
      return NextResponse.json(
        { error: 'HTML content is required and must be a string' },
        { status: 400 }
      );
    }

    if (html.trim().length === 0) {
      return NextResponse.json(
        { error: 'HTML content cannot be empty' },
        { status: 400 }
      );
    }

    // Launch browser with optimized settings
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
      ],
    });

    const page = await browser.newPage();

    // Set viewport for consistent rendering
    await page.setViewport({
      width: 1200,
      height: 800,
      deviceScaleFactor: 1,
    });

    // Inject custom CSS if provided
    let finalHtml = html;
    if (css) {
      finalHtml = `
        <html>
          <head>
            <style>${css}</style>
          </head>
          <body>
            ${html}
          </body>
        </html>
      `;
    }

    // Set content and wait for it to load
    await page.setContent(finalHtml, {
      waitUntil: 'networkidle0',
      timeout: 30000,
    });

    // Configure PDF options
    const pdfOptions: PDFOptions = {
      format: options.format || 'A4',
      landscape: options.orientation === 'landscape',
      printBackground: options.printBackground !== false, // Default to true
      margin: {
        top: options.margin?.top || '1cm',
        right: options.margin?.right || '1cm',
        bottom: options.margin?.bottom || '1cm',
        left: options.margin?.left || '1cm',
      },
      scale: options.scale || 1,
      displayHeaderFooter: options.displayHeaderFooter || false,
      headerTemplate: options.headerTemplate || '',
      footerTemplate: options.footerTemplate || '',
    };

    // Generate PDF
    const pdfBuffer = await page.pdf(pdfOptions);

    await browser.close();
    browser = null;

    // Prepare response headers
    const filename = options.filename || 'document.pdf';
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');

    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${sanitizedFilename}"`,
        'Content-Length': pdfBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    // Ensure browser is closed in case of error
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }

    console.error('PDF generation error:', error);

    // Return appropriate error response
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        return NextResponse.json(
          {
            error: 'PDF generation timed out. Please try with simpler content.',
          },
          { status: 408 }
        );
      }

      if (error.message.includes('memory')) {
        return NextResponse.json(
          {
            error:
              'Insufficient memory to generate PDF. Please try with smaller content.',
          },
          { status: 507 }
        );
      }
    }

    return NextResponse.json(
      {
        error:
          'Failed to generate PDF. Please check your HTML content and try again.',
      },
      { status: 500 }
    );
  }
}
