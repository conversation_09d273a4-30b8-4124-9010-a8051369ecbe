'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, ChevronDown, ChevronUp } from 'lucide-react';

export type DataTableColumn<T> = {
  key: string;
  header: string;
  cell: (item: T) => React.ReactNode;
  sortable?: boolean;
};

export type DataTableProps<T> = {
  data: T[];
  columns: DataTableColumn<T>[];
  keyField: keyof T;
  title?: string;
  searchPlaceholder?: string;
  searchField?: keyof T;
  emptyMessage?: string;
  onRowClick?: (item: T) => void;
  actions?: React.ReactNode;
  className?: string;
  compact?: boolean;
};

export function DataTable<T>({
  data,
  columns,
  keyField,
  title,
  searchPlaceholder = 'Search...',
  searchField,
  emptyMessage = 'No data found',
  onRowClick,
  actions,
  className = '',
  compact = false,
}: DataTableProps<T>) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortKey, setSortKey] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter data based on search query
  const filteredData = searchField
    ? data.filter(item => {
        const fieldValue = String(item[searchField] || '').toLowerCase();
        return fieldValue.includes(searchQuery.toLowerCase());
      })
    : data;

  // Sort data based on sort key and direction
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortKey) return 0;

    const aValue = a[sortKey as keyof T];
    const bValue = b[sortKey as keyof T];

    if (aValue === bValue) return 0;

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Handle number comparison
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    return sortDirection === 'asc' ? 1 : -1;
  });

  const handleSort = (key: string) => {
    if (sortKey === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortKey(key);
      setSortDirection('asc');
    }
  };

  const renderSortIcon = (key: string) => {
    if (sortKey !== key) return null;
    return sortDirection === 'asc' ? (
      <ChevronUp className='h-4 w-4 ml-1' />
    ) : (
      <ChevronDown className='h-4 w-4 ml-1' />
    );
  };

  return (
    <Card className={className}>
      {title && (
        <CardHeader className={compact ? 'p-3' : ''}>
          <div className='flex justify-between items-center'>
            <CardTitle className={compact ? 'text-base' : ''}>
              {title}
            </CardTitle>
            {actions}
          </div>
          {searchField && (
            <div className='relative mt-2'>
              <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className={`pl-8 ${compact ? 'h-8 text-sm' : ''}`}
              />
            </div>
          )}
        </CardHeader>
      )}
      <CardContent className={`p-0 ${!title ? 'pt-0' : ''}`}>
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map(column => (
                  <TableHead
                    key={column.key}
                    className={`${compact ? 'h-9 px-2 py-1 text-xs' : ''} ${
                      column.sortable ? 'cursor-pointer' : ''
                    }`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className='flex items-center'>
                      {column.header}
                      {column.sortable && renderSortIcon(column.key)}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedData.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className='h-24 text-center text-muted-foreground'
                  >
                    {emptyMessage}
                  </TableCell>
                </TableRow>
              ) : (
                sortedData.map(item => (
                  <TableRow
                    key={String(item[keyField])}
                    className={onRowClick ? 'cursor-pointer' : ''}
                    onClick={() => onRowClick && onRowClick(item)}
                  >
                    {columns.map(column => (
                      <TableCell
                        key={column.key}
                        className={compact ? 'p-2 text-sm' : ''}
                      >
                        {column.cell(item)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper components for common cell types
export const TextCell = (text: string | number) => <span>{text}</span>;

export const BadgeCell = (
  text: string,
  variant: 'default' | 'outline' | 'secondary' | 'destructive' = 'default',
  className = ''
) => (
  <Badge variant={variant} className={className}>
    {text}
  </Badge>
);

export const ButtonCell = (
  text: string,
  onClick: (e: React.MouseEvent) => void,
  variant:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'destructive'
    | 'ghost'
    | 'link' = 'outline',
  size: 'default' | 'sm' | 'lg' | 'icon' = 'sm',
  className = ''
) => (
  <Button
    variant={variant}
    size={size}
    onClick={e => {
      e.stopPropagation();
      onClick(e);
    }}
    className={className}
  >
    {text}
  </Button>
);
