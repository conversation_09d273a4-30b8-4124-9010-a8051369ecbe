# 8 \- User Management for Administrators

# **Document 8 \- Functional Specification: User Management for Administrators**

## **Overview**

The User Management for Administrators feature empowers system administrators to securely create, edit, deactivate, and manage user accounts for Members and Welon Trust members within the Childfree Legacy Web Application. Leveraging AWS Amplify, this specification outlines account management and permission assignment processes, ensuring that only authorized users access sensitive estate planning data. The system emphasizes robust security (HIPAA and SOC2 compliance), role-based access control (RBAC), and an intuitive interface to enhance trust and operational efficiency. AWS Amplify’s serverless capabilities, including real-time data management and encryption, provide a scalable and secure foundation.

---

## **1\. User Account Management**

Administrators can create, edit, and deactivate accounts for Members and Welon Trust members, facilitating seamless onboarding and offboarding.

### **Key Requirements**

#### **User Roles Managed**

- **Members**: End users managing their personal estate plans.
- **Welon Trust**: Trust administrators responsible for executing estate plans.
- **Professionals** (Future Phase \- Not MVP): Advisors and authorized individuals accessing Member data.

#### **Account Actions**

- Create new user accounts with role-specific configurations.
- Edit existing user details (e.g., name, email, permissions).
- Deactivate or reactivate user accounts.

#### **Security Measures**

- Only Administrators with "User Management" permissions can perform these actions, enforced via **AWS Cognito User Groups**.
- Sensitive operations (e.g., account deactivation) require multi-factor authentication (MFA) through **AWS Cognito**.
- All requests are secured with HTTPS, leveraging **AWS Amplify’s default configuration**.

#### **Role-Based Access Control (RBAC)**

- Permissions are assigned based on roles (e.g., Members cannot access administrative tools).
- Welon Trust users have fine-grained permissions (e.g., document viewing vs. editing).

### **User Flows**

#### **Creating a New User**

1. Administrator navigates to /admin/users.
2. Clicks "Create New User."
3. Selects role (Member or Welon Trust) from a dropdown.
4. Enters user details (e.g., name, email, phone).
5. Assigns role-specific permissions via checkboxes.
6. Clicks "Save" to create the account and trigger a welcome email via **AWS Simple Email Service (SES)**.

#### **Editing a User**

1. Administrator selects a user from the list and clicks "Edit."
2. Modifies details or permissions as needed.
3. Clicks "Save" to apply changes.
4. System logs edits in **Amazon CloudWatch** and notifies the user via **AWS SES** if required (e.g., permission updates).

#### **Deactivating a User**

1. Administrator selects a user and clicks "Deactivate."
2. Confirms via a dialog: "Are you sure you want to deactivate this user?"
3. Account is marked inactive, and the user is logged out via **AWS Cognito**.
4. Sends deactivation email via **AWS SES**.

### **Edge Cases**

- **Duplicate Email**: Displays "This email is already in use."
- **Pending Actions**: Warns if deactivating users with pending tasks (e.g., unsigned documents).
- **Reactivation**: Restores previous permissions upon reactivation.

### **Compliance Considerations**

- **HIPAA and SOC2**: Encrypts data at rest with **AWS Key Management Service (KMS)** and in transit with TLS.
- **Audit Trails**: Logs all actions in **CloudWatch** with timestamps and admin IDs.
- **RBAC**: Prevents admins from assigning permissions exceeding their own level.

### **UI Components**

| Element                  | Description                        |
| ------------------------ | ---------------------------------- |
| User List Table          | Columns: Name, Email, Role, Status |
| "Create New User" Button | Opens new user form                |
| "Edit" Button            | Opens edit form                    |
| "Deactivate" Button      | Deactivates user                   |
| Role Dropdown            | Selects role (Member, Welon Trust) |
| Permission Checkboxes    | Assigns role-specific permissions  |
| Confirmation Dialog      | Confirms deactivation              |

---

## **2\. Permission Management**

Administrators assign and manage permissions for all roles (Members, Administrators, Welon Trust, Professionals) using subroles for granular control, adhering to the principle of least privilege.

### **Key Requirements**

#### **Permission Types (Updated)**

- **Members**:
  - Permissions: View/edit own estate plans, access emergency features (e.g., Dead Man's Switch).
  - Restrictions: No access to administrative or trust tools.
- **Administrator Subroles**:
  - **Advanced**: Full system access (user management, templates, settings).
  - **Basic**: Read-only user data; password resets.
  - **Reporting**: Generate/export reports.
  - **Finance**: Manage billing and financial reports.
- **Welon Trust Subroles**:
  - **Advanced**: Full trust document access (create, read, update, delete \- CRUD).
  - **Basic**: View documents, update statuses, upload files.
  - **Medical**: Read medical documents, communicate with providers.
  - **Emergency Service**: Manage emergency access and notifications.
- **Professional Subroles** (Future Phase):
  - **Financial Advisor**: Read-only access to assigned Members’ data.

#### **Granular Control**

- Permissions are customizable via checkboxes (e.g., "View Documents," "Edit Documents").
- Default permissions are auto-assigned based on subrole.

#### **Security**

- Permissions cannot exceed the Administrator’s own level.
- Changes require re-authentication via **AWS Cognito MFA**.
- All changes are logged in **CloudWatch**.

### **User Flow for Assigning Permissions**

1. During user creation or editing, select role and subrole.
2. System pre-populates default permissions.
3. Adjust permissions via checkboxes and click "Save."
4. Logs assignment in **CloudWatch**.

### **Edge Cases**

- **Permission Conflicts**: Prevents conflicting assignments (e.g., "Edit" without "View").
- **Bulk Updates**: Supports multi-user permission updates for policy changes.
- **Revoked Permissions**: Notifies users via **AWS SES** if critical access is revoked.

### **Compliance Considerations**

- **Least Privilege**: Assigns only necessary permissions.
- **Audit Trails**: Logs changes with admin ID, timestamp, and details.

### **UI Components**

| Element                   | Description                  |
| ------------------------- | ---------------------------- |
| Permission Checkboxes     | Toggles for granular control |
| "Apply Default" Button    | Resets to subrole defaults   |
| "Save Permissions" Button | Applies permissions          |

---

## **3\. Role-Specific Controls**

The system enforces role-based controls to prevent unauthorized access.

### **Key Requirements**

- **Members**: Limited to personal estate features.
- **Welon Trust**: Access only assigned Members’ documents.
- **Administrators**: Full management, restricted by own permissions.

### **Implementation Notes**

- RBAC enforced via **AWS AppSync resolvers** and **Cognito User Groups**.
- Middleware checks roles before granting route access.

### **Compliance Considerations**

- **Segregation of Duties**: Admins cannot manage their own accounts.
- **Access Reviews**: Periodic audits via **CloudWatch** logs.

---

## **4\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **AWS Amplify Hosting**.
- **Routes**:
  - /admin/users: User management dashboard.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - createUser: Creates user with role and permissions.
  - updateUser: Edits user details/permissions.
  - deactivateUser: Deactivates user.
  - listUsers: Retrieves user list.
- **Database (Amazon DynamoDB)**:
  - users: Columns: id, name, email, role, subrole, permissions, status.
  - audit_logs: Columns: id, admin_id, action, details, timestamp.
- **Encryption**: **AWS KMS** for data at rest; TLS for data in transit.

### **Logging**

- Tracks all actions in **CloudWatch** with structured logs.

---

## **5\. Testing and Validation**

- **Unit Tests**: Validate user creation and permission logic.
- **Integration Tests**: Confirm full management flow.
- **Security Tests**: Ensure RBAC enforcement.
- **User Acceptance Testing (UAT)**: Verify admin usability.

### **Test Cases**

| Scenario                     | Expected Outcome                     |
| ---------------------------- | ------------------------------------ |
| Edit Welon Trust Permissions | Permissions updated successfully     |
| Deactivate User              | Account deactivated, user logged out |
| Bulk Permission Update       | Multiple users updated               |

---

## **6\. Compliance and Security**

- **HIPAA**: Encrypts data with **KMS**; secures transit with TLS.
- **SOC2**: Enforces access controls via **Cognito**; logs in **CloudWatch**.
- **Audit Trails**: Records all actions with timestamps and admin IDs.

---

## **Summary**

The User Management for Administrators feature enables secure account and permission management while ensuring compliance with HIPAA, SOC2, and RBAC principles. By leveraging AWS Amplify’s serverless ecosystem, the system provides intuitive, scalable tools to safeguard sensitive estate planning data, fostering trust in the Childfree Legacy platform.
