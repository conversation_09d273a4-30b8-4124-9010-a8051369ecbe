'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  Database,
  Link,
  AlertTriangle,
  Save,
  FileText,
  Code,
} from 'lucide-react';
import {
  Interview,
  InterviewQuestion,
  TemplateVariable,
} from '@/types/interview-builder';
import { getTemplateVariables } from '@/lib/api/interview-builder';

interface InterviewVariable {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'array';
  description: string;
  defaultValue?: any;
  required: boolean;
  linkedQuestions: string[];
  templateMapping?: string;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: string[];
  };
}

interface VariableManagerProps {
  interview: Interview;
  questions: InterviewQuestion[];
  onDataUpdated: () => void;
}

export function VariableManager({
  interview,
  questions,
  onDataUpdated,
}: VariableManagerProps) {
  const [variables, setVariables] = useState<InterviewVariable[]>([]);
  const [templateVariables, setTemplateVariables] = useState<
    TemplateVariable[]
  >([]);
  const [showAddVariable, setShowAddVariable] = useState(false);
  const [editingVariable, setEditingVariable] =
    useState<InterviewVariable | null>(null);
  const [newVariable, setNewVariable] = useState({
    name: '',
    type: 'text' as InterviewVariable['type'],
    description: '',
    defaultValue: '',
    required: false,
    templateMapping: '',
  });

  useEffect(() => {
    loadTemplateVariables();
    generateVariablesFromQuestions();
  }, [questions]);

  const loadTemplateVariables = async () => {
    try {
      const vars = await getTemplateVariables();
      setTemplateVariables(vars);
    } catch (err) {
      console.error('Error loading template variables:', err);
    }
  };

  const generateVariablesFromQuestions = () => {
    const generatedVars: InterviewVariable[] = questions
      .filter(q => q.templateMapping)
      .map(q => ({
        id: `var_${q.id}`,
        name: q.templateMapping!,
        type: getVariableTypeFromQuestion(q.type),
        description: `Variable for: ${q.text}`,
        required: q.required,
        linkedQuestions: [q.id],
        templateMapping: q.templateMapping,
      }));

    setVariables(generatedVars);
  };

  const getVariableTypeFromQuestion = (
    questionType: string
  ): InterviewVariable['type'] => {
    switch (questionType) {
      case 'number':
        return 'number';
      case 'date':
        return 'date';
      case 'checkbox':
        return 'array';
      default:
        return 'text';
    }
  };

  const handleAddVariable = () => {
    setEditingVariable(null);
    setNewVariable({
      name: '',
      type: 'text',
      description: '',
      defaultValue: '',
      required: false,
      templateMapping: '',
    });
    setShowAddVariable(true);
  };

  const handleEditVariable = (variable: InterviewVariable) => {
    setEditingVariable(variable);
    setNewVariable({
      name: variable.name,
      type: variable.type,
      description: variable.description,
      defaultValue: variable.defaultValue || '',
      required: variable.required,
      templateMapping: variable.templateMapping || '',
    });
    setShowAddVariable(true);
  };

  const handleSaveVariable = () => {
    const variable: InterviewVariable = {
      id: editingVariable?.id || `var_${Date.now()}`,
      name: newVariable.name,
      type: newVariable.type,
      description: newVariable.description,
      defaultValue: newVariable.defaultValue || undefined,
      required: newVariable.required,
      linkedQuestions: editingVariable?.linkedQuestions || [],
      templateMapping: newVariable.templateMapping || undefined,
    };

    if (editingVariable) {
      setVariables(prev =>
        prev.map(v => (v.id === editingVariable.id ? variable : v))
      );
    } else {
      setVariables(prev => [...prev, variable]);
    }

    setShowAddVariable(false);
    onDataUpdated();
  };

  const handleDeleteVariable = (variableId: string) => {
    if (confirm('Are you sure you want to delete this variable?')) {
      setVariables(prev => prev.filter(v => v.id !== variableId));
      onDataUpdated();
    }
  };

  const getLinkedQuestionText = (questionIds: string[]) => {
    return questionIds
      .map(id => questions.find(q => q.id === id)?.text)
      .filter(Boolean)
      .join(', ');
  };

  const getVariableTypeIcon = (type: InterviewVariable['type']) => {
    switch (type) {
      case 'text':
        return '📝';
      case 'number':
        return '🔢';
      case 'date':
        return '📅';
      case 'boolean':
        return '☑️';
      case 'array':
        return '📋';
      default:
        return '❓';
    }
  };

  return (
    <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
      {/* Variables List */}
      <div className='lg:col-span-2'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center justify-between'>
              <span className='flex items-center'>
                <Database className='mr-2 h-5 w-5' />
                Interview Variables
              </span>
              <Button onClick={handleAddVariable} size='sm'>
                <Plus className='mr-2 h-4 w-4' />
                Add Variable
              </Button>
            </CardTitle>
            <CardDescription>
              Manage variables that store interview answers and data
            </CardDescription>
          </CardHeader>
          <CardContent>
            {variables.length === 0 ? (
              <div className='text-center py-12'>
                <Database className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
                <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
                  No variables defined
                </h3>
                <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
                  Variables are automatically created when you map questions to
                  template fields.
                </p>
                <div className='mt-6'>
                  <Button onClick={handleAddVariable}>
                    <Plus className='mr-2 h-4 w-4' />
                    Add Custom Variable
                  </Button>
                </div>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Variable</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Linked Questions</TableHead>
                    <TableHead>Template</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {variables.map(variable => (
                    <TableRow key={variable.id}>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <span className='text-lg'>
                            {getVariableTypeIcon(variable.type)}
                          </span>
                          <div>
                            <div className='font-medium'>{variable.name}</div>
                            {variable.required && (
                              <Badge variant='destructive' className='text-xs'>
                                Required
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant='secondary' className='text-xs'>
                          {variable.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className='max-w-xs truncate text-sm text-[var(--custom-gray-medium)]'>
                          {variable.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='max-w-xs truncate text-sm'>
                          {variable.linkedQuestions.length > 0 ? (
                            <span className='text-blue-600'>
                              {variable.linkedQuestions.length} question(s)
                            </span>
                          ) : (
                            <span className='text-[var(--custom-gray-medium)]'>
                              None
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {variable.templateMapping ? (
                          <div className='flex items-center text-sm text-green-600'>
                            <Link className='h-3 w-3 mr-1' />
                            {variable.templateMapping}
                          </div>
                        ) : (
                          <span className='text-[var(--custom-gray-medium)] text-sm'>
                            Not mapped
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => handleEditVariable(variable)}
                          >
                            <Edit className='h-3 w-3' />
                          </Button>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => handleDeleteVariable(variable.id)}
                            className='text-red-600 hover:text-red-700'
                          >
                            <Trash2 className='h-3 w-3' />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Variable Form / Template Variables */}
      <div className='space-y-6'>
        {/* Add/Edit Variable Form */}
        {showAddVariable && (
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>
                {editingVariable ? 'Edit Variable' : 'Add Variable'}
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='variableName'>Variable Name</Label>
                <Input
                  id='variableName'
                  value={newVariable.name}
                  onChange={e =>
                    setNewVariable(prev => ({ ...prev, name: e.target.value }))
                  }
                  placeholder='client_full_name'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='variableType'>Type</Label>
                <Select
                  value={newVariable.type}
                  onValueChange={(value: InterviewVariable['type']) =>
                    setNewVariable(prev => ({ ...prev, type: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='text'>Text</SelectItem>
                    <SelectItem value='number'>Number</SelectItem>
                    <SelectItem value='date'>Date</SelectItem>
                    <SelectItem value='boolean'>Boolean</SelectItem>
                    <SelectItem value='array'>Array</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='variableDescription'>Description</Label>
                <Textarea
                  id='variableDescription'
                  value={newVariable.description}
                  onChange={e =>
                    setNewVariable(prev => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder='Describe what this variable stores'
                  rows={2}
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='templateMapping'>Template Mapping</Label>
                <Select
                  value={newVariable.templateMapping}
                  onValueChange={value =>
                    setNewVariable(prev => ({
                      ...prev,
                      templateMapping: value,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select template variable' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=''>No mapping</SelectItem>
                    {templateVariables.map(tv => (
                      <SelectItem key={tv.id} value={tv.name}>
                        {tv.name} - {tv.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='flex space-x-2'>
                <Button onClick={handleSaveVariable} size='sm'>
                  <Save className='mr-2 h-4 w-4' />
                  {editingVariable ? 'Update' : 'Add'} Variable
                </Button>
                <Button
                  variant='outline'
                  onClick={() => setShowAddVariable(false)}
                  size='sm'
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Template Variables Reference */}
        <Card>
          <CardHeader>
            <CardTitle className='text-lg flex items-center'>
              <FileText className='mr-2 h-5 w-5' />
              Template Variables
            </CardTitle>
            <CardDescription>
              Available variables in document templates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-2 max-h-60 overflow-y-auto'>
              {templateVariables.map(tv => (
                <div key={tv.id} className='p-2 border rounded text-sm'>
                  <div className='font-medium'>{tv.name}</div>
                  <div className='text-[var(--custom-gray-medium)] text-xs'>
                    {tv.description}
                  </div>
                  <div className='text-blue-600 text-xs'>{tv.category}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Variable Stats */}
        <Card>
          <CardHeader>
            <CardTitle className='text-lg'>Variable Stats</CardTitle>
          </CardHeader>
          <CardContent className='space-y-3'>
            <div className='flex justify-between'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                Total Variables:
              </span>
              <span className='font-medium'>{variables.length}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                Mapped to Template:
              </span>
              <span className='font-medium'>
                {variables.filter(v => v.templateMapping).length}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                Required:
              </span>
              <span className='font-medium'>
                {variables.filter(v => v.required).length}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                Linked to Questions:
              </span>
              <span className='font-medium'>
                {variables.filter(v => v.linkedQuestions.length > 0).length}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
