'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Save, AlertTriangle, Users, Shield, Plus, Minus } from 'lucide-react';
import {
  SubRole,
  RolePermission,
  mockPermissions,
} from './role-management-table';

interface BulkRoleEditorProps {
  selectedRoles: SubRole[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (updates: BulkRoleUpdate[]) => void;
}

export interface BulkRoleUpdate {
  roleId: string;
  action: 'add_permissions' | 'remove_permissions' | 'replace_permissions';
  permissions: string[];
}

type BulkAction = 'add' | 'remove' | 'replace';

export function BulkRoleEditor({
  selectedRoles,
  isOpen,
  onClose,
  onSave,
}: BulkRoleEditorProps) {
  const [selectedAction, setSelectedAction] = useState<BulkAction>('add');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [previewUpdates, setPreviewUpdates] = useState<BulkRoleUpdate[]>([]);

  useEffect(() => {
    generatePreview();
  }, [selectedAction, selectedPermissions, selectedRoles]);

  const generatePreview = () => {
    const updates: BulkRoleUpdate[] = selectedRoles.map(role => {
      let newPermissions: string[] = [];

      switch (selectedAction) {
        case 'add':
          newPermissions = [
            ...new Set([...role.permissions, ...selectedPermissions]),
          ];
          break;
        case 'remove':
          newPermissions = role.permissions.filter(
            p => !selectedPermissions.includes(p)
          );
          break;
        case 'replace':
          newPermissions = [...selectedPermissions];
          break;
      }

      return {
        roleId: role.id,
        action: `${selectedAction}_permissions` as BulkRoleUpdate['action'],
        permissions: newPermissions,
      };
    });

    setPreviewUpdates(updates);
  };

  const handlePermissionToggle = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter(p => p !== permissionId)
      );
    }
  };

  const handleSave = () => {
    onSave(previewUpdates);
    onClose();
  };

  const getActionIcon = (action: BulkAction) => {
    switch (action) {
      case 'add':
        return <Plus className='h-4 w-4' />;
      case 'remove':
        return <Minus className='h-4 w-4' />;
      case 'replace':
        return <Shield className='h-4 w-4' />;
    }
  };

  const getActionColor = (action: BulkAction) => {
    switch (action) {
      case 'add':
        return 'text-green-600';
      case 'remove':
        return 'text-red-600';
      case 'replace':
        return 'text-blue-600';
    }
  };

  const groupedPermissions = mockPermissions.reduce(
    (groups, permission) => {
      const category = permission.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(permission);
      return groups;
    },
    {} as Record<string, RolePermission[]>
  );

  const getTotalUsersAffected = () => {
    return selectedRoles.reduce((total, role) => total + role.userCount, 0);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-5xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <Users className='h-5 w-5' />
            <span>Bulk Edit Roles</span>
          </DialogTitle>
          <DialogDescription>
            Apply permission changes to {selectedRoles.length} selected role(s)
            affecting {getTotalUsersAffected()} user(s)
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue='action' className='w-full'>
          <TabsList className='grid w-full grid-cols-3'>
            <TabsTrigger value='action'>Select Action</TabsTrigger>
            <TabsTrigger value='permissions'>Choose Permissions</TabsTrigger>
            <TabsTrigger value='preview'>Preview Changes</TabsTrigger>
          </TabsList>

          <TabsContent value='action' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle>Bulk Action Type</CardTitle>
                <CardDescription>
                  Choose how you want to modify permissions for the selected
                  roles
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-3'>
                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAction === 'add'
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedAction('add')}
                  >
                    <div className='flex items-center space-x-3'>
                      <div
                        className={`p-2 rounded-full ${selectedAction === 'add' ? 'bg-green-100' : 'bg-gray-100'}`}
                      >
                        <Plus
                          className={`h-4 w-4 ${selectedAction === 'add' ? 'text-green-600' : 'text-[var(--custom-gray-medium)]'}`}
                        />
                      </div>
                      <div>
                        <h3 className='font-medium'>Add Permissions</h3>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          Add selected permissions to existing role permissions
                        </p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAction === 'remove'
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedAction('remove')}
                  >
                    <div className='flex items-center space-x-3'>
                      <div
                        className={`p-2 rounded-full ${selectedAction === 'remove' ? 'bg-red-100' : 'bg-gray-100'}`}
                      >
                        <Minus
                          className={`h-4 w-4 ${selectedAction === 'remove' ? 'text-red-600' : 'text-[var(--custom-gray-medium)]'}`}
                        />
                      </div>
                      <div>
                        <h3 className='font-medium'>Remove Permissions</h3>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          Remove selected permissions from role permissions
                        </p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedAction === 'replace'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedAction('replace')}
                  >
                    <div className='flex items-center space-x-3'>
                      <div
                        className={`p-2 rounded-full ${selectedAction === 'replace' ? 'bg-blue-100' : 'bg-gray-100'}`}
                      >
                        <Shield
                          className={`h-4 w-4 ${selectedAction === 'replace' ? 'text-blue-600' : 'text-[var(--custom-gray-medium)]'}`}
                        />
                      </div>
                      <div>
                        <h3 className='font-medium'>Replace Permissions</h3>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          Replace all role permissions with selected permissions
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <Alert>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>
                    <strong>Warning:</strong> Bulk changes will affect all users
                    assigned to the selected roles. This action cannot be
                    undone. Please review your changes carefully in the preview
                    tab.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Selected Roles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2'>
                  {selectedRoles.map(role => (
                    <div
                      key={role.id}
                      className='flex items-center justify-between p-3 border rounded-lg'
                    >
                      <div>
                        <p className='font-medium'>{role.name}</p>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          {role.description}
                        </p>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <Badge variant='outline'>{role.userCount} users</Badge>
                        <Badge className='bg-gray-100 text-[var(--custom-gray-dark)]'>
                          {role.masterRole}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='permissions' className='space-y-4'>
            <div className='space-y-6'>
              {Object.entries(groupedPermissions).map(
                ([category, permissions]) => (
                  <Card key={category}>
                    <CardHeader>
                      <CardTitle className='text-lg'>{category}</CardTitle>
                      <CardDescription>
                        Select permissions to {selectedAction} for the selected
                        roles
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className='grid grid-cols-1 gap-3'>
                        {permissions.map(permission => (
                          <div
                            key={permission.id}
                            className='flex items-start space-x-3'
                          >
                            <Checkbox
                              id={`bulk-${permission.id}`}
                              checked={selectedPermissions.includes(
                                permission.id
                              )}
                              onCheckedChange={checked =>
                                handlePermissionToggle(
                                  permission.id,
                                  checked as boolean
                                )
                              }
                            />
                            <div className='flex-1 min-w-0'>
                              <Label
                                htmlFor={`bulk-${permission.id}`}
                                className='text-sm font-medium cursor-pointer'
                              >
                                {permission.name}
                              </Label>
                              <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              )}
            </div>
          </TabsContent>

          <TabsContent value='preview' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  {getActionIcon(selectedAction)}
                  <span className={getActionColor(selectedAction)}>
                    {selectedAction.charAt(0).toUpperCase() +
                      selectedAction.slice(1)}{' '}
                    Permissions Preview
                  </span>
                </CardTitle>
                <CardDescription>
                  Review the changes that will be applied to each role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  {previewUpdates.map((update, index) => {
                    const role = selectedRoles.find(
                      r => r.id === update.roleId
                    );
                    if (!role) return null;

                    const currentPermissions = role.permissions;
                    const newPermissions = update.permissions;
                    const addedPermissions = newPermissions.filter(
                      p => !currentPermissions.includes(p)
                    );
                    const removedPermissions = currentPermissions.filter(
                      p => !newPermissions.includes(p)
                    );

                    return (
                      <div
                        key={update.roleId}
                        className='border rounded-lg p-4'
                      >
                        <div className='flex items-center justify-between mb-3'>
                          <h3 className='font-medium'>{role.name}</h3>
                          <Badge variant='outline'>
                            {role.userCount} users affected
                          </Badge>
                        </div>

                        <div className='space-y-2 text-sm'>
                          <div>
                            <span className='font-medium'>
                              Current permissions:{' '}
                            </span>
                            <span className='text-[var(--custom-gray-medium)]'>
                              {currentPermissions.length}
                            </span>
                          </div>
                          <div>
                            <span className='font-medium'>
                              New permissions:{' '}
                            </span>
                            <span className='text-[var(--custom-gray-medium)]'>
                              {newPermissions.length}
                            </span>
                          </div>

                          {addedPermissions.length > 0 && (
                            <div>
                              <span className='font-medium text-green-600'>
                                Added:{' '}
                              </span>
                              <div className='flex flex-wrap gap-1 mt-1'>
                                {addedPermissions.map(permId => (
                                  <Badge
                                    key={permId}
                                    className='bg-green-100 text-green-800 text-xs'
                                  >
                                    {mockPermissions.find(p => p.id === permId)
                                      ?.name || permId}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {removedPermissions.length > 0 && (
                            <div>
                              <span className='font-medium text-red-600'>
                                Removed:{' '}
                              </span>
                              <div className='flex flex-wrap gap-1 mt-1'>
                                {removedPermissions.map(permId => (
                                  <Badge
                                    key={permId}
                                    className='bg-red-100 text-red-800 text-xs'
                                  >
                                    {mockPermissions.find(p => p.id === permId)
                                      ?.name || permId}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className='flex items-center justify-between'>
          <div className='text-sm text-[var(--custom-gray-medium)]'>
            {selectedPermissions.length} permission(s) selected for{' '}
            {selectedRoles.length} role(s)
          </div>

          <div className='flex space-x-2'>
            <Button variant='outline' onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={selectedPermissions.length === 0}
              className='flex items-center space-x-2'
            >
              <Save className='h-4 w-4' />
              <span>Apply Changes</span>
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
