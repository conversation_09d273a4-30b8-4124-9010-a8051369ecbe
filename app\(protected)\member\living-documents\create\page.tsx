'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText, Plus } from 'lucide-react';
import Link from 'next/link';
import routes from '@/utils/routes';
import { useLivingDocuments } from '@/hooks/useLivingDocuments';
import { LivingDocumentModal } from '@/components/dashboard/LivingDocumentModal';
import {
  documentTemplates,
  documentTypeLabels,
} from '@/app/utils/livingDocumentUtils';

export default function CreateLivingDocumentPage() {
  const router = useRouter();
  const { addDocument } = useLivingDocuments();
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const handleCreateFromTemplate = (template: any) => {
    setSelectedTemplate(template);
    setModalOpen(true);
  };

  const handleCreateBlank = () => {
    setSelectedTemplate(null);
    setModalOpen(true);
  };

  const handleSaveDocument = async (documentData: any) => {
    try {
      await addDocument(documentData);
      setModalOpen(false);
      router.push(routes.livingDocuments);
    } catch (error) {
      console.error('Error creating document:', error);
    }
  };

  const getInitialValues = () => {
    if (selectedTemplate) {
      return {
        title: selectedTemplate.title.replace(' Template', ''),
        documentType: selectedTemplate.documentType,
        content: selectedTemplate.content,
        reminderFrequency: 'SemiAnnually' as const,
        status: 'Active' as const,
        isTemplate: false,
      };
    }
    return undefined;
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        {/* Header */}
        <div className='flex items-center gap-4 mb-8'>
          <Button variant='outline' size='sm' asChild>
            <Link href={routes.livingDocuments}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Documents
            </Link>
          </Button>
          <div>
            <h1 className='text-3xl font-bold'>Create Living Document</h1>
            <p className='text-[var(--custom-gray-medium)] mt-2'>
              Choose a template or start from scratch to create your living
              document.
            </p>
          </div>
        </div>

        {/* Create Options */}
        <div className='grid gap-6 mb-8'>
          {/* Create from Blank */}
          <Card
            className='cursor-pointer hover:shadow-md transition-shadow'
            onClick={handleCreateBlank}
          >
            <CardContent className='p-6'>
              <div className='flex items-center gap-4'>
                <div className='p-3 bg-blue-100 rounded-lg'>
                  <Plus className='h-6 w-6 text-blue-600' />
                </div>
                <div>
                  <h3 className='text-lg font-semibold'>Start from Blank</h3>
                  <p className='text-[var(--custom-gray-medium)]'>
                    Create a custom document from scratch
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Templates Section */}
          <div>
            <h2 className='text-xl font-semibold mb-4'>
              Choose from Templates
            </h2>
            <div className='grid gap-4 md:grid-cols-2'>
              {documentTemplates.map(template => (
                <Card
                  key={template.id}
                  className='cursor-pointer hover:shadow-md transition-shadow'
                  onClick={() => handleCreateFromTemplate(template)}
                >
                  <CardHeader className='pb-3'>
                    <div className='flex items-center gap-3'>
                      <div className='p-2 bg-green-100 rounded-lg'>
                        <FileText className='h-5 w-5 text-green-600' />
                      </div>
                      <div>
                        <CardTitle className='text-lg'>
                          {template.title}
                        </CardTitle>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          {documentTypeLabels[template.documentType]}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                      {template.description}
                    </p>
                    <div className='text-xs text-[var(--custom-gray-medium)]'>
                      Click to use this template
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Info Section */}
        <Card className='bg-blue-50 border-blue-200'>
          <CardContent className='p-6'>
            <h3 className='font-semibold text-blue-900 mb-2'>
              About Living Documents
            </h3>
            <div className='space-y-2 text-sm text-blue-800'>
              <p>
                • Living documents are designed to be updated regularly to keep
                information current
              </p>
              <p>
                • Set reminder frequencies to ensure you review and update your
                documents on schedule
              </p>
              <p>
                • Templates provide a structured starting point with common
                fields and sections
              </p>
              <p>
                • All documents are securely encrypted and stored in your
                personal vault
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Modal */}
        <LivingDocumentModal
          open={modalOpen}
          onOpenChange={setModalOpen}
          onSave={handleSaveDocument}
          initialValues={getInitialValues()}
        />
      </div>
    </div>
  );
}
