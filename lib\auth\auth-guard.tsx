'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DashboardContentLayoutSkeleton } from '@/components/ui/skeletons';
import routes from '@/utils/routes';
import Link from 'next/link';

interface AuthGuardProps {
  children: React.ReactNode;
  fallbackMessage?: string;
  redirectTo?: string;
}

export function AuthGuard({
  children,
  fallbackMessage = 'You need to be logged in to access this page.',
  redirectTo = routes.login,
}: AuthGuardProps) {
  const { user, loading } = useAuth();

  if (loading) {
    return <DashboardContentLayoutSkeleton />;
  }

  if (!user) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Page Not Available</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-muted-foreground'>{fallbackMessage}</p>
              <div className='mt-4 flex justify-end'>
                <Button asChild>
                  <Link href={redirectTo} prefetch={true}>
                    Go to Login
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
