'use client';

import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

// Generate the client outside of hook
const client = generateClient<Schema>();

export interface LivingDocument {
  id: string;
  userId: string;
  documentType:
    | 'EmergencyContacts'
    | 'PetCare'
    | 'DigitalAssets'
    | 'EndOfLifeWishes'
    | 'MedicalDirectives'
    | 'Other';
  title: string;
  content: string; // JSON string containing document data
  version: number;
  status: 'Draft' | 'Active' | 'Archived';
  lastReviewDate?: string;
  nextReviewDate?: string;
  reminderFrequency: 'Monthly' | 'Quarterly' | 'SemiAnnually' | 'Annually';
  isTemplate: boolean;
  templateId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export function useLivingDocuments() {
  const [documents, setDocuments] = useState<LivingDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch documents
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      const user = await getCurrentUser();

      const { data, errors } = await client.models.LivingDocument.list({
        filter: {
          userId: { eq: user.userId },
          status: { ne: 'Archived' },
        },
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        const mappedDocuments: LivingDocument[] = data.map(doc => ({
          id: doc.id!,
          userId: doc.userId,
          documentType: doc.documentType as LivingDocument['documentType'],
          title: doc.title,
          content: doc.content,
          version: doc.version || 1,
          status: doc.status as LivingDocument['status'],
          lastReviewDate: doc.lastReviewDate || undefined,
          nextReviewDate: doc.nextReviewDate || undefined,
          reminderFrequency:
            doc.reminderFrequency as LivingDocument['reminderFrequency'],
          isTemplate: Boolean(doc.isTemplate),
          templateId: doc.templateId || undefined,
          createdAt: doc.createdAt || undefined,
          updatedAt: doc.updatedAt || undefined,
        }));

        setDocuments(mappedDocuments);
      }
    } catch (err) {
      console.error('Error fetching living documents:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch documents'
      );
    } finally {
      setLoading(false);
    }
  };

  // Add document
  const addDocument = async (
    documentData: Omit<
      LivingDocument,
      'id' | 'userId' | 'version' | 'createdAt' | 'updatedAt'
    >
  ) => {
    try {
      const user = await getCurrentUser();

      // Calculate next review date based on frequency
      const nextReviewDate = calculateNextReviewDate(
        documentData.reminderFrequency
      );

      const { data, errors } = await client.models.LivingDocument.create({
        userId: user.userId,
        documentType: documentData.documentType,
        title: documentData.title,
        content: documentData.content,
        version: 1,
        status: documentData.status || 'Active',
        lastReviewDate: new Date().toISOString(),
        nextReviewDate: nextReviewDate.toISOString(),
        reminderFrequency: documentData.reminderFrequency,
        isTemplate: documentData.isTemplate,
        templateId: documentData.templateId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        const newDocument: LivingDocument = {
          id: data.id!,
          userId: data.userId,
          documentType: data.documentType as LivingDocument['documentType'],
          title: data.title,
          content: data.content,
          version: data.version || 1,
          status: data.status as LivingDocument['status'],
          lastReviewDate: data.lastReviewDate || undefined,
          nextReviewDate: data.nextReviewDate || undefined,
          reminderFrequency:
            data.reminderFrequency as LivingDocument['reminderFrequency'],
          isTemplate: Boolean(data.isTemplate),
          templateId: data.templateId || undefined,
          createdAt: data.createdAt || undefined,
          updatedAt: data.updatedAt || undefined,
        };

        setDocuments([...documents, newDocument]);

        // Log the creation
        await logDocumentUpdate(data.id!, 'Created', 'Document created', 0, 1);

        return data;
      }
    } catch (err) {
      console.error('Error adding living document:', err);
      throw err;
    }
  };

  // Update document
  const updateDocument = async (
    id: string,
    updates: Partial<LivingDocument>
  ) => {
    try {
      const existingDoc = documents.find(doc => doc.id === id);
      if (!existingDoc) {
        throw new Error('Document not found');
      }

      const newVersion = existingDoc.version + 1;
      const nextReviewDate = updates.reminderFrequency
        ? calculateNextReviewDate(updates.reminderFrequency)
        : existingDoc.nextReviewDate
          ? new Date(existingDoc.nextReviewDate)
          : calculateNextReviewDate(existingDoc.reminderFrequency);

      const { data, errors } = await client.models.LivingDocument.update({
        id: id,
        ...updates,
        version: newVersion,
        lastReviewDate: new Date().toISOString(),
        nextReviewDate: nextReviewDate.toISOString(),
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        const updatedDocument: LivingDocument = {
          id: data.id!,
          userId: data.userId,
          documentType: data.documentType as LivingDocument['documentType'],
          title: data.title,
          content: data.content,
          version: data.version || 1,
          status: data.status as LivingDocument['status'],
          lastReviewDate: data.lastReviewDate || undefined,
          nextReviewDate: data.nextReviewDate || undefined,
          reminderFrequency:
            data.reminderFrequency as LivingDocument['reminderFrequency'],
          isTemplate: Boolean(data.isTemplate),
          templateId: data.templateId || undefined,
          createdAt: data.createdAt || undefined,
          updatedAt: data.updatedAt || undefined,
        };

        setDocuments(
          documents.map(doc => (doc.id === id ? updatedDocument : doc))
        );

        // Log the update
        await logDocumentUpdate(
          id,
          'Updated',
          'Document updated',
          existingDoc.version,
          newVersion
        );

        return data;
      }
    } catch (err) {
      console.error('Error updating living document:', err);
      throw err;
    }
  };

  // Delete document (archive it)
  const deleteDocument = async (id: string) => {
    try {
      const { data, errors } = await client.models.LivingDocument.update({
        id: id,
        status: 'Archived',
        updatedAt: new Date().toISOString(),
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      setDocuments(documents.filter(doc => doc.id !== id));

      // Log the archival
      await logDocumentUpdate(id, 'Archived', 'Document archived');

      return data;
    } catch (err) {
      console.error('Error deleting living document:', err);
      throw err;
    }
  };

  // Helper function to calculate next review date
  const calculateNextReviewDate = (
    frequency: LivingDocument['reminderFrequency']
  ): Date => {
    const now = new Date();
    switch (frequency) {
      case 'Monthly':
        return new Date(now.setMonth(now.getMonth() + 1));
      case 'Quarterly':
        return new Date(now.setMonth(now.getMonth() + 3));
      case 'SemiAnnually':
        return new Date(now.setMonth(now.getMonth() + 6));
      case 'Annually':
        return new Date(now.setFullYear(now.getFullYear() + 1));
      default:
        return new Date(now.setMonth(now.getMonth() + 6)); // Default to semi-annually
    }
  };

  // Helper function to log document updates
  const logDocumentUpdate = async (
    documentId: string,
    changeType: 'Created' | 'Updated' | 'Reviewed' | 'Archived',
    description?: string,
    previousVersion?: number,
    newVersion?: number
  ) => {
    try {
      const user = await getCurrentUser();

      await client.models.DocumentUpdateLog.create({
        documentId,
        userId: user.userId,
        changeType,
        changeDescription: description,
        previousVersion,
        newVersion,
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      console.error('Error logging document update:', err);
      // Don't throw here as this is just logging
    }
  };

  // Get document status for dashboard
  const getDocumentStatus = (
    document: LivingDocument
  ): 'current' | 'due-soon' | 'overdue' => {
    if (!document.nextReviewDate) return 'current';

    const now = new Date();
    const reviewDate = new Date(document.nextReviewDate);
    const daysUntilReview = Math.ceil(
      (reviewDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysUntilReview < 0) return 'overdue';
    if (daysUntilReview <= 30) return 'due-soon';
    return 'current';
  };

  useEffect(() => {
    fetchDocuments();
  }, []);

  return {
    documents,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument,
    refetch: fetchDocuments,
    getDocumentStatus,
  };
}
