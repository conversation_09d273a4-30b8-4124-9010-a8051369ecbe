#!/usr/bin/env node

/**
 * Test commit message validation
 * Usage: node scripts/test-commit-message.js "Your commit message"
 */

const { execSync } = require('child_process');

function testCommitMessage(message) {
  console.log(`Testing commit message: "${message}"`);
  console.log('─'.repeat(50));

  try {
    // Test the commit message using commitlint
    execSync(`echo "${message}" | npx commitlint`, {
      stdio: 'inherit',
      encoding: 'utf8',
    });
    console.log('✅ Commit message is valid!');
    return true;
  } catch (error) {
    console.log('❌ Commit message is invalid!');
    return false;
  }
}

function runTests() {
  console.log('🧪 Testing Commit Message Validation\n');

  const testCases = [
    // Valid messages
    { message: 'CHIL-69 Add user authentication feature', shouldPass: true },
    { message: 'Fix login bug CHIL-123', shouldPass: true },
    {
      message: 'CHIL-456: Update documentation for API endpoints',
      shouldPass: true,
    },
    { message: 'Refactor database queries (CHIL-789)', shouldPass: true },
    { message: 'CHIL-1 Initial commit', shouldPass: true },
    { message: 'CHIL-9999 Very long ticket number', shouldPass: true },

    // Invalid messages
    { message: 'Add user authentication feature', shouldPass: false },
    { message: 'Fix login bug', shouldPass: false },
    { message: 'Update documentation', shouldPass: false },
    { message: 'CHIL- Missing number', shouldPass: false },
    { message: 'CHIL-ABC Invalid number format', shouldPass: false },
    { message: 'chil-123 Wrong case', shouldPass: false },
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach((testCase, index) => {
    console.log(`\nTest ${index + 1}:`);
    const result = testCommitMessage(testCase.message);

    if (result === testCase.shouldPass) {
      console.log(
        `✅ Test passed (expected: ${testCase.shouldPass ? 'valid' : 'invalid'})`
      );
      passed++;
    } else {
      console.log(
        `❌ Test failed (expected: ${testCase.shouldPass ? 'valid' : 'invalid'}, got: ${result ? 'valid' : 'invalid'})`
      );
      failed++;
    }

    console.log('─'.repeat(50));
  });

  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(
    `📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`
  );

  if (failed > 0) {
    process.exit(1);
  }
}

// Check if a specific message was provided as argument
const args = process.argv.slice(2);
if (args.length > 0) {
  const message = args.join(' ');
  testCommitMessage(message);
} else {
  // Run all tests
  runTests();
}
