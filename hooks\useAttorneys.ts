'use client';

import { useState, useEffect, useCallback } from 'react';
import { Attorney } from '@/types/attorney-reviews';
import {
  fetchAttorneys,
  updateAttorney,
  deleteAttorney,
  permanentlyDeleteAttorney,
  createAttorney,
} from '@/lib/data/attorneys';

interface UseAttorneysState {
  attorneys: Attorney[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateAttorneyStatus: (
    attorneyId: string,
    isActive: boolean
  ) => Promise<void>;
  removeAttorney: (attorneyId: string) => Promise<void>;
  addAttorney: (attorneyData: Omit<Attorney, 'id'>) => Promise<void>;
}

export function useAttorneys(): UseAttorneysState {
  const [attorneys, setAttorneys] = useState<Attorney[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchAttorneys();
      setAttorneys(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching attorneys:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  const updateAttorneyStatus = useCallback(
    async (attorneyId: string, isActive: boolean) => {
      try {
        setError(null);
        const updatedAttorney = await updateAttorney(attorneyId, { isActive });

        setAttorneys(prevAttorneys =>
          prevAttorneys.map(attorney =>
            attorney.id === attorneyId ? { ...attorney, isActive } : attorney
          )
        );
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to update attorney'
        );
        throw err;
      }
    },
    []
  );

  const removeAttorney = useCallback(
    async (attorneyId: string, permanent: boolean = true) => {
      try {
        setError(null);

        if (permanent) {
          // Permanent delete - attorney disappears immediately
          await permanentlyDeleteAttorney(attorneyId);
          // Remove attorney from state completely
          setAttorneys(prevAttorneys =>
            prevAttorneys.filter(attorney => attorney.id !== attorneyId)
          );
        } else {
          // Soft delete - mark as inactive
          await deleteAttorney(attorneyId);
          setAttorneys(prevAttorneys =>
            prevAttorneys.map(attorney =>
              attorney.id === attorneyId
                ? { ...attorney, isActive: false }
                : attorney
            )
          );
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to delete attorney'
        );
        throw err;
      }
    },
    []
  );

  const addAttorney = useCallback(
    async (attorneyData: Omit<Attorney, 'id'>) => {
      try {
        setError(null);
        const newAttorney = await createAttorney(attorneyData);

        setAttorneys(prevAttorneys => [newAttorney, ...prevAttorneys]);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to create attorney'
        );
        throw err;
      }
    },
    []
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    attorneys,
    loading,
    error,
    refetch,
    updateAttorneyStatus,
    removeAttorney,
    addAttorney,
  };
}
