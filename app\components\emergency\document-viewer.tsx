'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Document } from './types';
import {
  FileText,
  Download,
  X,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize2,
  Eye,
  Calendar,
  User,
  FileType,
} from 'lucide-react';

interface DocumentViewerProps {
  document: Document;
  isOpen: boolean;
  onClose: () => void;
  onDownload: (documentId: string) => void;
}

export function DocumentViewer({
  document,
  isOpen,
  onClose,
  onDownload,
}: DocumentViewerProps) {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Mock PDF content - in real implementation, this would be actual PDF rendering
  const renderDocumentContent = () => {
    return (
      <div className='bg-background border rounded-lg shadow-inner p-8 min-h-[800px] relative'>
        <div
          className='transition-transform duration-200 w-full'
          style={{
            transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
            transformOrigin: 'center center',
          }}
        >
          {/* Mock document content */}
          <div className='max-w-2xl mx-auto space-y-6 text-[var(--custom-gray-dark)]'>
            <div className='text-center border-b pb-4'>
              <h1 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {document.title}
              </h1>
              <p className='text-sm text-[var(--custom-gray-medium)] mt-2'>
                Document Type: {document.type}
              </p>
            </div>

            <div className='space-y-4'>
              <div className='bg-gray-50 p-4 rounded'>
                <h2 className='font-semibold text-lg mb-2'>
                  Document Information
                </h2>
                <div className='grid grid-cols-2 gap-4 text-sm'>
                  <div>
                    <span className='font-medium'>Document ID:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {document.id}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>User ID:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {document.userId}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>Created:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {formatDate(document.createdAt)}
                    </p>
                  </div>
                  <div>
                    <span className='font-medium'>Last Updated:</span>
                    <p className='text-[var(--custom-gray-medium)]'>
                      {formatDate(document.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Mock content based on document type */}
              {document.type === 'Legal' && (
                <div className='space-y-6'>
                  <h3 className='font-semibold text-lg'>
                    Legal Document Content
                  </h3>
                  <p className='text-justify leading-relaxed'>
                    This is a mock legal document for demonstration purposes. In
                    a real implementation, this would display the actual content
                    of the {document.title}. The document contains important
                    legal information and instructions that have been securely
                    stored and verified.
                  </p>

                  <div className='space-y-4'>
                    <h4 className='font-medium'>
                      Section 1: General Provisions
                    </h4>
                    <p className='text-justify leading-relaxed'>
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                      Sed do eiusmod tempor incididunt ut labore et dolore magna
                      aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                      ullamco laboris nisi ut aliquip ex ea commodo consequat.
                      Duis aute irure dolor in reprehenderit in voluptate velit
                      esse cillum dolore eu fugiat nulla pariatur.
                    </p>

                    <h4 className='font-medium'>
                      Section 2: Specific Instructions
                    </h4>
                    <p className='text-justify leading-relaxed'>
                      Excepteur sint occaecat cupidatat non proident, sunt in
                      culpa qui officia deserunt mollit anim id est laborum. Sed
                      ut perspiciatis unde omnis iste natus error sit voluptatem
                      accusantium doloremque laudantium, totam rem aperiam,
                      eaque ipsa quae ab illo inventore veritatis et quasi
                      architecto beatae vitae dicta sunt explicabo.
                    </p>

                    <h4 className='font-medium'>
                      Section 3: Legal Obligations
                    </h4>
                    <p className='text-justify leading-relaxed'>
                      Nemo enim ipsam voluptatem quia voluptas sit aspernatur
                      aut odit aut fugit, sed quia consequuntur magni dolores
                      eos qui ratione voluptatem sequi nesciunt. Neque porro
                      quisquam est, qui dolorem ipsum quia dolor sit amet,
                      consectetur, adipisci velit, sed quia non numquam eius
                      modi tempora incidunt ut labore et dolore magnam aliquam
                      quaerat voluptatem.
                    </p>
                  </div>

                  <div className='bg-yellow-50 border border-yellow-200 p-4 rounded'>
                    <p className='text-sm text-yellow-800'>
                      <strong>Important:</strong> This document has legal
                      implications and should be reviewed carefully. Please
                      consult with appropriate legal counsel if needed.
                    </p>
                  </div>
                </div>
              )}

              {document.type === 'Medical' && (
                <div className='space-y-6'>
                  <h3 className='font-semibold text-lg'>
                    Medical Document Content
                  </h3>
                  <p className='text-justify leading-relaxed'>
                    This is a mock medical document for demonstration purposes.
                    In a real implementation, this would display the actual
                    content of the {document.title}. The document contains
                    important medical directives and healthcare instructions.
                  </p>

                  <div className='space-y-4'>
                    <h4 className='font-medium'>Medical History</h4>
                    <p className='text-justify leading-relaxed'>
                      Patient has a comprehensive medical history including
                      regular check-ups and preventive care. All medical records
                      have been maintained according to healthcare standards and
                      regulations. This section contains detailed information
                      about past medical procedures, treatments, and ongoing
                      care requirements.
                    </p>

                    <h4 className='font-medium'>Healthcare Directives</h4>
                    <p className='text-justify leading-relaxed'>
                      Specific instructions regarding healthcare decisions,
                      treatment preferences, and medical interventions. These
                      directives are legally binding and should be followed by
                      healthcare providers in accordance with the patient's
                      wishes and applicable laws.
                    </p>

                    <h4 className='font-medium'>Emergency Contacts</h4>
                    <p className='text-justify leading-relaxed'>
                      Designated healthcare proxies and emergency contacts who
                      are authorized to make medical decisions on behalf of the
                      patient when they are unable to do so themselves. Contact
                      information and authorization levels are clearly
                      specified.
                    </p>

                    <h4 className='font-medium'>Medication Information</h4>
                    <p className='text-justify leading-relaxed'>
                      Current medications, dosages, allergies, and adverse
                      reactions. This information is critical for healthcare
                      providers to ensure safe and effective treatment. Regular
                      updates are maintained to reflect any changes in
                      medication regimen.
                    </p>
                  </div>

                  <div className='bg-blue-50 border border-blue-200 p-4 rounded'>
                    <p className='text-sm text-blue-800'>
                      <strong>Medical Notice:</strong> This document contains
                      sensitive medical information and healthcare directives.
                      Please ensure proper handling and confidentiality.
                    </p>
                  </div>
                </div>
              )}

              {document.type === 'Personal' && (
                <div className='space-y-6'>
                  <h3 className='font-semibold text-lg'>
                    Personal Document Content
                  </h3>
                  <p className='text-justify leading-relaxed'>
                    This is a mock personal document for demonstration purposes.
                    In a real implementation, this would display the actual
                    content of the {document.title}. The document contains
                    personal instructions and important information.
                  </p>

                  <div className='space-y-4'>
                    <h4 className='font-medium'>Personal Wishes</h4>
                    <p className='text-justify leading-relaxed'>
                      Detailed personal preferences and wishes regarding various
                      aspects of life and end-of-life care. These instructions
                      reflect the individual's values, beliefs, and personal
                      choices that should be respected and followed.
                    </p>

                    <h4 className='font-medium'>Care Instructions</h4>
                    <p className='text-justify leading-relaxed'>
                      Specific instructions for the care of dependents, pets,
                      property, and personal belongings. These guidelines ensure
                      that important responsibilities are handled according to
                      the individual's preferences and requirements.
                    </p>

                    <h4 className='font-medium'>Special Requests</h4>
                    <p className='text-justify leading-relaxed'>
                      Any special requests or arrangements that are important to
                      the individual. This may include ceremonial preferences,
                      distribution of personal items, or specific instructions
                      for handling personal affairs.
                    </p>
                  </div>
                </div>
              )}

              {document.type === 'Financial' && (
                <div className='space-y-6'>
                  <h3 className='font-semibold text-lg'>
                    Financial Document Content
                  </h3>
                  <p className='text-justify leading-relaxed'>
                    This is a mock financial document for demonstration
                    purposes. In a real implementation, this would display the
                    actual content of the {document.title}. The document
                    contains important financial information and account
                    details.
                  </p>

                  <div className='space-y-4'>
                    <h4 className='font-medium'>Account Information</h4>
                    <p className='text-justify leading-relaxed'>
                      Comprehensive listing of financial accounts, including
                      bank accounts, investment portfolios, retirement funds,
                      and other financial assets. Account numbers, institutions,
                      and access information are securely documented.
                    </p>

                    <h4 className='font-medium'>Asset Distribution</h4>
                    <p className='text-justify leading-relaxed'>
                      Instructions for the distribution and management of
                      financial assets. This includes beneficiary information,
                      percentage allocations, and specific instructions for
                      handling various types of financial instruments and
                      investments.
                    </p>

                    <h4 className='font-medium'>Financial Obligations</h4>
                    <p className='text-justify leading-relaxed'>
                      Documentation of outstanding debts, loans, mortgages, and
                      other financial obligations. Payment schedules, creditor
                      information, and instructions for handling these
                      obligations are clearly outlined.
                    </p>

                    <h4 className='font-medium'>Insurance Policies</h4>
                    <p className='text-justify leading-relaxed'>
                      Details of life insurance, health insurance, property
                      insurance, and other insurance policies. Policy numbers,
                      coverage amounts, beneficiaries, and claim procedures are
                      documented for easy reference.
                    </p>
                  </div>

                  <div className='bg-green-50 border border-green-200 p-4 rounded'>
                    <p className='text-sm text-green-800'>
                      <strong>Financial Notice:</strong> This document contains
                      sensitive financial information. Please handle with
                      appropriate security measures.
                    </p>
                  </div>
                </div>
              )}

              <div className='border-t pt-4 text-center text-sm text-[var(--custom-gray-medium)]'>
                <p>End of Document</p>
                <p className='mt-2'>
                  This is a mock document for demonstration purposes.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-6xl max-h-[90vh] p-0'>
        <DialogHeader className='p-6 pb-4 border-b'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <FileText className='h-6 w-6 text-blue-600' />
              <div>
                <DialogTitle className='text-xl'>{document.title}</DialogTitle>
                <div className='flex items-center gap-4 mt-1 text-sm text-muted-foreground'>
                  <div className='flex items-center gap-1'>
                    <FileType className='h-4 w-4' />
                    <span>{document.type}</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Calendar className='h-4 w-4' />
                    <span>Updated {formatDate(document.updatedAt)}</span>
                  </div>
                </div>
              </div>
            </div>
            <Badge variant='outline'>{document.type}</Badge>
          </div>
        </DialogHeader>

        {/* Toolbar */}
        <div className='flex items-center justify-between px-6 py-3 bg-muted/50 border-b'>
          <div className='flex items-center gap-2'>
            <Button variant='outline' size='sm' onClick={handleZoomOut}>
              <ZoomOut className='h-4 w-4' />
            </Button>
            <span className='text-sm font-medium min-w-[60px] text-center'>
              {zoom}%
            </span>
            <Button variant='outline' size='sm' onClick={handleZoomIn}>
              <ZoomIn className='h-4 w-4' />
            </Button>
            <Button variant='outline' size='sm' onClick={handleRotate}>
              <RotateCw className='h-4 w-4' />
            </Button>
          </div>

          <div className='flex items-center gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onDownload(document.id)}
            >
              <Download className='h-4 w-4 mr-1' />
              Download
            </Button>
            <Button variant='outline' size='sm' onClick={onClose}>
              <X className='h-4 w-4' />
            </Button>
          </div>
        </div>

        {/* Document Content */}
        <div className='flex-1 overflow-auto p-6 bg-gray-100 max-h-[calc(90vh-200px)]'>
          <div className='h-full overflow-y-auto overflow-x-auto'>
            {renderDocumentContent()}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
