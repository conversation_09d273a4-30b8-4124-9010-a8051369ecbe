'use client';

import '../styles/globals.css';
import { DM_Sans, Inter } from 'next/font/google';
import ConfigureAmplify from '@/utils/configureAmplify';
import Header from '@/components/Header';
import { AuthProvider } from '@/context/AuthContext';
import { Toaster } from '@/components/ui/sonner';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Providers } from '@/components/providers';
import { ReactNode } from 'react';
import Script from 'next/script';

const queryClient = new QueryClient();

const dmSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-dm-sans',
  weight: ['400', '500', '600', '700'],
});

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  weight: ['400', '500', '600', '700'],
});

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang='en' className={`${dmSans.variable} ${inter.variable}`}>
      <body data-accessibility-adjustable='true'>
        <ConfigureAmplify />
        <Providers>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <div className='flex min-h-screen flex-col'>
                <Header />
                <main className='flex-1'>{children}</main>
                <Toaster />
              </div>
            </AuthProvider>
          </QueryClientProvider>
        </Providers>

        <Script
          src='https://acc-landing.vercel.app/accessibilik.min.js'
          strategy='afterInteractive'
        />
      </body>
    </html>
  );
}
