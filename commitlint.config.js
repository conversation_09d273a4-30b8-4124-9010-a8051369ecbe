module.exports = {
  rules: {
    // Custom rule to enforce ticket number in commit message
    'ticket-number-required': [2, 'always'],
    // Allow longer subject lines for ticket numbers
    'subject-max-length': [2, 'always', 100],
    // Disable conventional commit rules to focus on ticket numbers
    'type-enum': [0],
    'type-empty': [0],
    'subject-empty': [0],
    'subject-case': [0],
    'header-max-length': [0],
  },
  plugins: [
    {
      rules: {
        'ticket-number-required': ({ subject, header }) => {
          // Check if the commit message contains a ticket number in format CHIL-XX
          const ticketPattern = /CHIL-\d+/;
          const hasTicketNumber = ticketPattern.test(header || subject || '');

          return [
            hasTicketNumber,
            hasTicketNumber
              ? ''
              : 'Commit message must contain a ticket number in format CHIL-XX (e.g., CHIL-69)',
          ];
        },
      },
    },
  ],
};
