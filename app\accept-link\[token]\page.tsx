'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  CheckCircle,
  AlertTriangle,
  Clock,
  UserPlus,
  Home,
  Heart,
  Baby,
} from 'lucide-react';

// Types for invitation data
interface InvitationData {
  id: string;
  inviterName: string;
  inviterEmail: string;
  inviteeEmail: string;
  status: 'pending' | 'expired' | 'accepted' | 'declined';
  expiresAt: string;
  sharedProfileFields?: {
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    pets?: string[];
    householdDetails?: string;
  };
}

interface ProfileFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phone: string;
  // Shared fields that can be pre-filled
  address: string;
  city: string;
  state: string;
  zipCode: string;
  pets: string;
  householdDetails: string;
}

export default function AcceptLinkPage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;

  const [invitationData, setInvitationData] = useState<InvitationData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isNewUser, setIsNewUser] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);

  const [profileData, setProfileData] = useState<ProfileFormData>({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    pets: '',
    householdDetails: '',
  });

  // Load invitation data
  useEffect(() => {
    const loadInvitationData = async () => {
      try {
        // Simulate API call to validate token and get invitation data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock invitation data
        const mockInvitation: InvitationData = {
          id: 'inv_123',
          inviterName: 'John Johnson',
          inviterEmail: '<EMAIL>',
          inviteeEmail: '<EMAIL>',
          status: 'pending',
          expiresAt: '2024-02-01',
          sharedProfileFields: {
            address: '123 Main Street',
            city: 'Springfield',
            state: 'IL',
            zipCode: '62701',
            pets: ['Max (Dog)', 'Whiskers (Cat)'],
            householdDetails: 'Two-story home with garden, no children',
          },
        };

        setInvitationData(mockInvitation);

        // Pre-fill shared profile fields if this is for a new user
        if (mockInvitation.sharedProfileFields) {
          setProfileData(prev => ({
            ...prev,
            address: mockInvitation.sharedProfileFields?.address || '',
            city: mockInvitation.sharedProfileFields?.city || '',
            state: mockInvitation.sharedProfileFields?.state || '',
            zipCode: mockInvitation.sharedProfileFields?.zipCode || '',
            pets: mockInvitation.sharedProfileFields?.pets?.join(', ') || '',
            householdDetails:
              mockInvitation.sharedProfileFields?.householdDetails || '',
          }));
        }
      } catch (err) {
        setError('Invalid or expired invitation link');
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      loadInvitationData();
    }
  }, [token]);

  const handleAcceptInvitation = async () => {
    setIsAccepting(true);
    setError('');

    try {
      // Simulate API call to accept invitation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess('Account successfully linked!');

      // Redirect to dashboard after success
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      setError('Failed to accept invitation. Please try again.');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDeclineInvitation = async () => {
    try {
      // Simulate API call to decline invitation
      await new Promise(resolve => setTimeout(resolve, 500));

      setSuccess('Invitation declined.');

      // Redirect to home page
      setTimeout(() => {
        router.push('/');
      }, 2000);
    } catch (err) {
      setError('Failed to decline invitation. Please try again.');
    }
  };

  const handleCompleteProfile = async () => {
    // Validate required personal fields
    if (
      !profileData.firstName ||
      !profileData.lastName ||
      !profileData.dateOfBirth
    ) {
      setError('Please fill in all required personal information');
      return;
    }

    setIsAccepting(true);
    setError('');

    try {
      // Simulate API call to create profile and accept invitation
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSuccess('Profile created and account linked successfully!');

      // Redirect to dashboard
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err) {
      setError('Failed to create profile. Please try again.');
    } finally {
      setIsAccepting(false);
    }
  };

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='text-center py-8'>
            <Clock className='h-12 w-12 mx-auto mb-4 animate-spin' />
            <p>Loading invitation...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitationData || invitationData.status === 'expired') {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='text-center py-8'>
            <AlertTriangle className='h-12 w-12 mx-auto mb-4 text-red-500' />
            <h2 className='text-xl font-semibold mb-2'>Invalid Invitation</h2>
            <p className='text-muted-foreground mb-4'>
              This invitation link is invalid or has expired.
            </p>
            <Button onClick={() => router.push('/')}>Go to Home</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='text-center py-8'>
            <CheckCircle className='h-12 w-12 mx-auto mb-4 text-green-500' />
            <h2 className='text-xl font-semibold mb-2'>Success!</h2>
            <p className='text-muted-foreground'>{success}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-2xl mx-auto px-4'>
        <Card>
          <CardHeader className='text-center'>
            <div className='flex justify-center mb-4'>
              <div className='p-3 bg-blue-100 rounded-full'>
                <Users className='h-8 w-8 text-blue-600' />
              </div>
            </div>
            <CardTitle className='text-2xl'>Account Link Invitation</CardTitle>
            <CardDescription>
              {invitationData.inviterName} has invited you to link accounts for
              shared estate planning
            </CardDescription>
          </CardHeader>

          <CardContent className='space-y-6'>
            {/* Invitation Details */}
            <div className='bg-blue-50 p-4 rounded-lg'>
              <h3 className='font-medium mb-2'>Invitation Details</h3>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>From:</span>
                  <span className='font-medium'>
                    {invitationData.inviterName}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Email:</span>
                  <span>{invitationData.inviterEmail}</span>
                </div>
                <div className='flex justify-between'>
                  <span>Expires:</span>
                  <span>{invitationData.expiresAt}</span>
                </div>
              </div>
            </div>

            {/* What this means */}
            <div>
              <h3 className='font-medium mb-3'>
                What does linking accounts mean?
              </h3>
              <ul className='space-y-2 text-sm text-muted-foreground'>
                <li className='flex items-start gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>
                    You can collaborate on shared estate planning documents
                  </span>
                </li>
                <li className='flex items-start gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>Your individual documents remain private</span>
                </li>
                <li className='flex items-start gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>
                    You can create joint trusts and shared property
                    documentation
                  </span>
                </li>
                <li className='flex items-start gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-500 mt-0.5 flex-shrink-0' />
                  <span>You can unlink accounts at any time</span>
                </li>
              </ul>
            </div>

            {/* New User Profile Setup */}
            {!showProfileForm && (
              <div>
                <h3 className='font-medium mb-3'>Do you have an account?</h3>
                <div className='flex gap-3'>
                  <Button
                    variant='outline'
                    className='flex-1'
                    onClick={handleAcceptInvitation}
                    disabled={isAccepting}
                  >
                    {isAccepting ? 'Accepting...' : 'Yes, I have an account'}
                  </Button>
                  <Button
                    className='flex-1'
                    onClick={() => setShowProfileForm(true)}
                  >
                    <UserPlus className='w-4 h-4 mr-2' />
                    No, create my account
                  </Button>
                </div>
              </div>
            )}

            {/* Profile Form for New Users */}
            {showProfileForm && (
              <div className='space-y-6'>
                <Separator />

                <div>
                  <h3 className='font-medium mb-3'>Create Your Profile</h3>
                  <Alert className='mb-4'>
                    <Home className='h-4 w-4' />
                    <AlertDescription>
                      Some fields have been pre-filled based on{' '}
                      {invitationData.inviterName}'s information. Please review
                      and update as needed.
                    </AlertDescription>
                  </Alert>
                </div>

                {/* Personal Information (Required) */}
                <div>
                  <h4 className='font-medium mb-3 text-red-600'>
                    Personal Information (Required)
                  </h4>
                  <div className='grid grid-cols-2 gap-4'>
                    <div>
                      <Label htmlFor='firstName'>First Name *</Label>
                      <Input
                        id='firstName'
                        value={profileData.firstName}
                        onChange={e =>
                          handleInputChange('firstName', e.target.value)
                        }
                        placeholder='Enter your first name'
                      />
                    </div>
                    <div>
                      <Label htmlFor='lastName'>Last Name *</Label>
                      <Input
                        id='lastName'
                        value={profileData.lastName}
                        onChange={e =>
                          handleInputChange('lastName', e.target.value)
                        }
                        placeholder='Enter your last name'
                      />
                    </div>
                  </div>
                  <div className='grid grid-cols-2 gap-4 mt-4'>
                    <div>
                      <Label htmlFor='dateOfBirth'>Date of Birth *</Label>
                      <Input
                        id='dateOfBirth'
                        type='date'
                        value={profileData.dateOfBirth}
                        onChange={e =>
                          handleInputChange('dateOfBirth', e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor='phone'>Phone Number</Label>
                      <Input
                        id='phone'
                        value={profileData.phone}
                        onChange={e =>
                          handleInputChange('phone', e.target.value)
                        }
                        placeholder='(*************'
                      />
                    </div>
                  </div>
                </div>

                {/* Shared Information (Pre-filled) */}
                <div>
                  <h4 className='font-medium mb-3 flex items-center gap-2'>
                    <Heart className='h-4 w-4 text-blue-500' />
                    Shared Household Information (Pre-filled)
                  </h4>
                  <div className='space-y-4'>
                    <div>
                      <Label htmlFor='address'>Address</Label>
                      <Input
                        id='address'
                        value={profileData.address}
                        onChange={e =>
                          handleInputChange('address', e.target.value)
                        }
                        placeholder='123 Main Street'
                      />
                    </div>
                    <div className='grid grid-cols-3 gap-4'>
                      <div>
                        <Label htmlFor='city'>City</Label>
                        <Input
                          id='city'
                          value={profileData.city}
                          onChange={e =>
                            handleInputChange('city', e.target.value)
                          }
                          placeholder='Springfield'
                        />
                      </div>
                      <div>
                        <Label htmlFor='state'>State</Label>
                        <Input
                          id='state'
                          value={profileData.state}
                          onChange={e =>
                            handleInputChange('state', e.target.value)
                          }
                          placeholder='IL'
                        />
                      </div>
                      <div>
                        <Label htmlFor='zipCode'>ZIP Code</Label>
                        <Input
                          id='zipCode'
                          value={profileData.zipCode}
                          onChange={e =>
                            handleInputChange('zipCode', e.target.value)
                          }
                          placeholder='62701'
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor='pets'>Pets</Label>
                      <Input
                        id='pets'
                        value={profileData.pets}
                        onChange={e =>
                          handleInputChange('pets', e.target.value)
                        }
                        placeholder='Max (Dog), Whiskers (Cat)'
                      />
                    </div>
                    <div>
                      <Label htmlFor='householdDetails'>
                        Household Details
                      </Label>
                      <Input
                        id='householdDetails'
                        value={profileData.householdDetails}
                        onChange={e =>
                          handleInputChange('householdDetails', e.target.value)
                        }
                        placeholder='Two-story home with garden'
                      />
                    </div>
                  </div>
                </div>

                <div className='flex gap-3'>
                  <Button
                    variant='outline'
                    onClick={() => setShowProfileForm(false)}
                    className='flex-1'
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleCompleteProfile}
                    disabled={isAccepting}
                    className='flex-1'
                  >
                    {isAccepting
                      ? 'Creating Account...'
                      : 'Create Account & Link'}
                  </Button>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <Alert variant='destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Decline Option */}
            {!showProfileForm && (
              <div className='text-center pt-4 border-t'>
                <Button
                  variant='ghost'
                  onClick={handleDeclineInvitation}
                  className='text-muted-foreground'
                >
                  Decline Invitation
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
