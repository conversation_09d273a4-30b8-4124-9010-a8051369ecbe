'use client';

import { useEffect, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import zxcvbn from 'zxcvbn';

interface PasswordStrengthIndicatorProps {
  password: string;
}

export default function PasswordStrengthIndicator({
  password,
}: PasswordStrengthIndicatorProps) {
  const [passwordScore, setPasswordScore] = useState(0);

  // Password strength labels
  const passwordStrengthLabels = [
    'Very Weak',
    'Weak',
    'Fair',
    'Good',
    'Strong',
  ];

  const getPasswordScorePercentage = (score: number) => {
    return score * 25;
  };

  useEffect(() => {
    if (password) {
      const result = zxcvbn(password);
      setPasswordScore(result.score);
    } else {
      setPasswordScore(0);
    }
  }, [password]);

  return (
    <div className='space-y-1'>
      <div className='flex justify-between text-xs'>
        <span className='text-muted-foreground'>Password strength:</span>
        {password ? (
          <span
            className={
              passwordScore >= 3
                ? 'text-green-500'
                : passwordScore >= 2
                  ? 'text-yellow-500'
                  : 'text-destructive'
            }
          >
            {passwordStrengthLabels[passwordScore]}
          </span>
        ) : (
          <span className='text-muted-foreground'>Enter a password</span>
        )}
      </div>
      <Progress
        value={password ? getPasswordScorePercentage(passwordScore) : 0}
        className='h-1.5'
      />
    </div>
  );
}
