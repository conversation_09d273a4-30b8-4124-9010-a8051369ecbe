# Amplify Data Schemas

This directory contains the refactored Amplify data models, organized by functional domain for better maintainability and modularity.

## File Structure

### Individual Schema Files

- **`demo-models.ts`** - Demo/example models for testing and development

  - `Todo` - Simple todo model for testing

- **`auth-models.ts`** - Authentication and user onboarding related models

  - `UserOnboarding` - User onboarding progress tracking

- **`interview-models.ts`** - Interview and questionnaire related models

  - `InterviewQuestion` - Interview questions configuration
  - `UserInterviewProgress` - User progress through interview questions

- **`template-models.ts`** - Template management related models

  - `Template` - Document templates
  - `TemplateVersion` - Template versioning

- **`contact-models.ts`** - Emergency contact related models

  - `EmergencyContact` - Emergency contact information

- **`document-models.ts`** - Living document related models

  - `LivingDocument` - Living documents (wills, directives, etc.)
  - `DocumentUpdateLog` - Audit log for document changes

- **`vault-models.ts`** - Vault access related models
  - `VaultAccess` - Access control for vault documents

### Index File

- **`index.ts`** - Re-exports all models from individual schema files for easy importing

## Usage

The main `resource.ts` file imports all models from the `schemas` directory:

```typescript
import {
  Todo,
  UserOnboarding,
  InterviewQuestion,
  UserInterviewProgress,
  Template,
  TemplateVersion,
  EmergencyContact,
  LivingDocument,
  DocumentUpdateLog,
  VaultAccess,
} from './schemas';
```

## Adding New Models

1. Create a new schema file in this directory (e.g., `new-feature-models.ts`)
2. Define your models using the Amplify schema syntax
3. Export the models from the file
4. Add the exports to `index.ts`
5. Import and add the models to the schema in `resource.ts`

## Benefits of This Structure

- **Modularity**: Related models are grouped together
- **Maintainability**: Easier to find and modify specific models
- **Scalability**: Easy to add new model groups without cluttering the main file
- **Team Collaboration**: Multiple developers can work on different model groups simultaneously
- **Clear Organization**: Logical separation by business domain
