'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Users,
  User,
  Shield,
  Eye,
  Edit,
  ArrowLeftRight,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { useLinkedAccountContext } from '@/lib/contexts/linked-account-context';
import { LINK_PERMISSIONS } from '@/types/account';

export function AccountSwitcher() {
  const {
    activeAccount,
    availableLinkedAccounts,
    switchToOwnAccount,
    switchToLinkedAccount,
    hasPermission,
    isLoading,
  } = useLinkedAccountContext();

  const [isExpanded, setIsExpanded] = useState(false);

  if (isLoading || !activeAccount) {
    return (
      <Card>
        <CardContent className='p-4'>
          <div className='flex items-center gap-2'>
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
            <span className='text-sm'>Loading account information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleAccountSwitch = (value: string) => {
    if (value === 'own') {
      switchToOwnAccount();
    } else {
      const linkedAccount = availableLinkedAccounts.find(
        link => link.id === value
      );
      if (linkedAccount) {
        switchToLinkedAccount(linkedAccount);
      }
    }
  };

  const getPermissionBadges = () => {
    if (activeAccount.mode === 'own') {
      return (
        <Badge
          variant='default'
          className='bg-green-100 text-green-800 border-green-300'
        >
          <CheckCircle className='h-3 w-3 mr-1' />
          Full Access
        </Badge>
      );
    }

    const permissions = activeAccount.permissions;
    const permissionLabels = {
      [LINK_PERMISSIONS.VIEW_DOCUMENTS]: 'View Docs',
      [LINK_PERMISSIONS.EDIT_DOCUMENTS]: 'Edit Docs',
      [LINK_PERMISSIONS.VIEW_EMERGENCY_CONTACTS]: 'View Emergency',
      [LINK_PERMISSIONS.EDIT_EMERGENCY_CONTACTS]: 'Edit Emergency',
      [LINK_PERMISSIONS.VIEW_DEAD_MAN_SWITCH]: 'View DMS',
      [LINK_PERMISSIONS.EDIT_DEAD_MAN_SWITCH]: 'Edit DMS',
      [LINK_PERMISSIONS.EXECUTE_EMERGENCY_PROTOCOL]: 'Emergency Protocol',
      [LINK_PERMISSIONS.VIEW_BILLING]: 'View Billing',
      [LINK_PERMISSIONS.EDIT_BILLING]: 'Edit Billing',
    };

    return (
      <div className='flex flex-wrap gap-1'>
        {permissions.map(permission => (
          <Badge key={permission} variant='outline' className='text-xs'>
            {permissionLabels[permission] || permission}
          </Badge>
        ))}
      </div>
    );
  };

  const getLinkTypeLabel = () => {
    if (activeAccount.mode === 'own') return null;

    const linkType = activeAccount.linkedAccount?.linkType;
    const labels = {
      primary: 'Primary Partner',
      secondary: 'Secondary Contact',
      delegate: 'Delegate',
      emergency: 'Emergency Contact',
    };

    return labels[linkType || 'secondary'] || 'Linked Account';
  };

  return (
    <Card className='border-2 border-blue-200 bg-blue-50/50'>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <ArrowLeftRight className='h-5 w-5 text-blue-600' />
            <span className='text-lg'>Active Account</span>
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Collapse' : 'Switch Account'}
          </Button>
        </CardTitle>
      </CardHeader>

      <CardContent className='space-y-4'>
        {/* Current Account Display */}
        <div className='flex items-center justify-between p-3 bg-background rounded-lg border'>
          <div className='flex items-center gap-3'>
            <div className='p-2 bg-blue-100 rounded-full'>
              {activeAccount.mode === 'own' ? (
                <User className='h-4 w-4 text-blue-600' />
              ) : (
                <Users className='h-4 w-4 text-blue-600' />
              )}
            </div>
            <div>
              <p className='font-medium'>{activeAccount.user.name}</p>
              <p className='text-sm text-muted-foreground'>
                {activeAccount.mode === 'own'
                  ? 'Your Account'
                  : getLinkTypeLabel()}
              </p>
            </div>
          </div>
          <div className='text-right'>
            <Badge
              variant={activeAccount.mode === 'own' ? 'default' : 'secondary'}
              className='mb-1'
            >
              {activeAccount.mode === 'own' ? 'Own Account' : 'Linked Account'}
            </Badge>
          </div>
        </div>

        {/* Permissions Display */}
        <div className='space-y-2'>
          <p className='text-sm font-medium'>Current Permissions:</p>
          {getPermissionBadges()}
        </div>

        {/* Account Switcher */}
        {isExpanded && (
          <div className='space-y-4 pt-4 border-t'>
            <div>
              <label className='text-sm font-medium mb-2 block'>
                Switch to Account:
              </label>
              <Select
                value={
                  activeAccount.mode === 'own'
                    ? 'own'
                    : activeAccount.linkedAccount?.id
                }
                onValueChange={handleAccountSwitch}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Select account' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='own'>
                    <div className='flex items-center gap-2'>
                      <User className='h-4 w-4' />
                      <span>Your Own Account</span>
                      <Badge variant='outline' className='ml-2'>
                        Full Access
                      </Badge>
                    </div>
                  </SelectItem>
                  {availableLinkedAccounts.map(link => {
                    const user = { name: `Account of ${link.userId}` }; // In real app, fetch user details
                    return (
                      <SelectItem key={link.id} value={link.id}>
                        <div className='flex items-center gap-2'>
                          <Users className='h-4 w-4' />
                          <span>{user.name}</span>
                          <Badge variant='outline' className='ml-2 capitalize'>
                            {link.linkType}
                          </Badge>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Available Linked Accounts Info */}
            {availableLinkedAccounts.length === 0 && (
              <Alert>
                <AlertCircle className='h-4 w-4' />
                <AlertDescription>
                  You don't have access to any linked accounts. Contact the
                  account owner to request access.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Access Level Warning for Linked Accounts */}
        {activeAccount.mode === 'linked' && (
          <Alert className='border-amber-200 bg-amber-50'>
            <Shield className='h-4 w-4 text-amber-600' />
            <AlertDescription className='text-amber-800'>
              You are viewing a linked account with limited permissions. Some
              features may be restricted.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for header/navigation
export function AccountSwitcherCompact() {
  const { activeAccount, isLoading } = useLinkedAccountContext();

  if (isLoading || !activeAccount) {
    return (
      <div className='flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-md'>
        <div className='animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600'></div>
        <span className='text-xs'>Loading...</span>
      </div>
    );
  }

  return (
    <div className='flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-md border border-blue-200'>
      {activeAccount.mode === 'own' ? (
        <User className='h-3 w-3 text-blue-600' />
      ) : (
        <Users className='h-3 w-3 text-blue-600' />
      )}
      <span className='text-xs font-medium'>
        {activeAccount.mode === 'own'
          ? 'Your Account'
          : `Linked: ${activeAccount.user.name}`}
      </span>
      <Badge
        variant={activeAccount.mode === 'own' ? 'default' : 'secondary'}
        className='text-xs px-1 py-0'
      >
        {activeAccount.mode === 'own' ? 'Own' : 'Linked'}
      </Badge>
    </div>
  );
}
