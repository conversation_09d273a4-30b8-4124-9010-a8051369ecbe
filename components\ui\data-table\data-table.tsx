'use client';

import * as React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
  PaginationState,
  getFacetedRowModel,
  getFacetedUniqueValues,
  Table as TanStackTable,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTableToolbar } from './data-table-toolbar';
import { DataTablePagination } from './data-table-pagination';
import { DataTableSkeleton } from './data-table-skeleton';

export interface DataTableFilterConfig {
  id: string;
  title: string;
  options: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
}

export interface DataTableConfig {
  searchColumn?: string;
  searchPlaceholder?: string;
  filters?: DataTableFilterConfig[];
  enableColumnVisibility?: boolean;
  enablePagination?: boolean;
  enableRowSelection?: boolean;
  defaultPageSize?: number;
}

interface DataTableProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
  config?: DataTableConfig;
  toolbar?: React.ComponentType<{
    table: TanStackTable<TData>;
    config?: DataTableConfig;
  }>;
  pagination?: React.ComponentType<{ table: TanStackTable<TData> }>;
  loading?: boolean;
  error?: string | null;
  className?: string;
}

export function DataTable<TData>({
  columns,
  data,
  config = {},
  toolbar: CustomToolbar,
  pagination: CustomPagination,
  loading = false,
  error = null,
  className = '',
}: DataTableProps<TData>) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const {
    searchColumn = 'name',
    searchPlaceholder = 'Search...',
    filters = [],
    enableColumnVisibility = true,
    enablePagination = true,
    enableRowSelection = false,
    defaultPageSize = 10,
  } = config;

  // Initialize state from URL parameters
  const initializeFromURL = React.useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());

    // Initialize sorting
    const sortColumn = params.get('sort');
    const sortOrder = params.get('order') as 'asc' | 'desc' | null;
    const initialSorting: SortingState =
      sortColumn && sortOrder
        ? [{ id: sortColumn, desc: sortOrder === 'desc' }]
        : [];

    // Initialize column filters
    const initialFilters: ColumnFiltersState = [];

    // Add search filter
    const search = params.get('search');
    if (search && searchColumn) {
      initialFilters.push({ id: searchColumn, value: search });
    }

    // Add faceted filters
    filters.forEach(filter => {
      const filterValue = params.get(filter.id);
      if (filterValue) {
        initialFilters.push({ id: filter.id, value: filterValue.split(',') });
      }
    });

    // Initialize column visibility
    const hiddenColumns = params.get('hidden');
    const initialVisibility: VisibilityState = {};
    if (hiddenColumns) {
      hiddenColumns.split(',').forEach(column => {
        initialVisibility[column] = false;
      });
    }

    // Initialize pagination
    const page = parseInt(params.get('page') || '1', 10) - 1; // Convert to 0-based
    const pageSize = parseInt(
      params.get('pageSize') || defaultPageSize.toString(),
      10
    );

    return {
      sorting: initialSorting,
      columnFilters: initialFilters,
      columnVisibility: initialVisibility,
      pagination: { pageIndex: page, pageSize },
    };
  }, [searchParams, searchColumn, filters, defaultPageSize]);

  const initialState = initializeFromURL();

  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>(initialState.columnVisibility);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    initialState.columnFilters
  );
  const [sorting, setSorting] = React.useState<SortingState>(
    initialState.sorting
  );
  const [pagination, setPagination] = React.useState<PaginationState>(
    initialState.pagination
  );

  // Function to update URL with current state
  const updateURL = React.useCallback(
    (updates: {
      sorting?: SortingState;
      columnFilters?: ColumnFiltersState;
      columnVisibility?: VisibilityState;
      pagination?: PaginationState;
    }) => {
      const params = new URLSearchParams();

      // Handle sorting
      const currentSorting = updates.sorting ?? sorting;
      if (currentSorting.length > 0) {
        const sort = currentSorting[0];
        params.set('sort', sort.id);
        params.set('order', sort.desc ? 'desc' : 'asc');
      }

      // Handle column filters
      const currentFilters = updates.columnFilters ?? columnFilters;
      currentFilters.forEach(filter => {
        if (filter.id === searchColumn && filter.value) {
          params.set('search', String(filter.value));
        } else if (Array.isArray(filter.value) && filter.value.length > 0) {
          params.set(filter.id, filter.value.join(','));
        }
      });

      // Handle column visibility
      if (enableColumnVisibility) {
        const currentVisibility = updates.columnVisibility ?? columnVisibility;
        const hiddenColumns = Object.entries(currentVisibility)
          .filter(([, visible]) => !visible)
          .map(([column]) => column);
        if (hiddenColumns.length > 0) {
          params.set('hidden', hiddenColumns.join(','));
        }
      }

      // Handle pagination
      if (enablePagination) {
        const currentPagination = updates.pagination ?? pagination;
        if (currentPagination.pageIndex > 0) {
          params.set('page', (currentPagination.pageIndex + 1).toString());
        }
        if (currentPagination.pageSize !== defaultPageSize) {
          params.set('pageSize', currentPagination.pageSize.toString());
        }
      }

      const newURL = `${window.location.pathname}?${params.toString()}`;
      router.replace(newURL, { scroll: false });
    },
    [
      router,
      sorting,
      columnFilters,
      columnVisibility,
      pagination,
      searchColumn,
      enableColumnVisibility,
      enablePagination,
      defaultPageSize,
    ]
  );

  // Custom handlers that update both state and URL
  const handleSortingChange = React.useCallback(
    (updater: any) => {
      const newSorting =
        typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      updateURL({ sorting: newSorting });
    },
    [sorting, updateURL]
  );

  const handleColumnFiltersChange = React.useCallback(
    (updater: any) => {
      const newFilters =
        typeof updater === 'function' ? updater(columnFilters) : updater;
      setColumnFilters(newFilters);
      updateURL({ columnFilters: newFilters });
    },
    [columnFilters, updateURL]
  );

  const handleColumnVisibilityChange = React.useCallback(
    (updater: any) => {
      const newVisibility =
        typeof updater === 'function' ? updater(columnVisibility) : updater;
      setColumnVisibility(newVisibility);
      updateURL({ columnVisibility: newVisibility });
    },
    [columnVisibility, updateURL]
  );

  const handlePaginationChange = React.useCallback(
    (updater: any) => {
      const newPagination =
        typeof updater === 'function' ? updater(pagination) : updater;
      setPagination(newPagination);
      updateURL({ pagination: newPagination });
    },
    [pagination, updateURL]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      ...(enablePagination && { pagination }),
    },
    enableRowSelection,
    onRowSelectionChange: setRowSelection,
    onSortingChange: handleSortingChange,
    onColumnFiltersChange: handleColumnFiltersChange,
    onColumnVisibilityChange: handleColumnVisibilityChange,
    ...(enablePagination && { onPaginationChange: handlePaginationChange }),
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...(enablePagination && { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const ToolbarComponent = CustomToolbar || DataTableToolbar;
  const PaginationComponent = CustomPagination || DataTablePagination;

  // Show loading skeleton
  if (loading) {
    return (
      <div className={className}>
        <DataTableSkeleton
          columnCount={columns.length}
          rowCount={10}
          showToolbar={true}
          showPagination={enablePagination}
        />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className='rounded-md border border-red-200 bg-red-50 p-4'>
          <div className='flex'>
            <div className='ml-3'>
              <h3 className='text-sm font-medium text-red-800'>
                Error loading data
              </h3>
              <div className='mt-2 text-sm text-red-700'>
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Toolbar with filters */}
      <ToolbarComponent table={table} config={config} />

      {/* Table */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {enablePagination && <PaginationComponent table={table} />}
    </div>
  );
}
