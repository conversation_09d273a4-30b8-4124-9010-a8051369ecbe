'use client';

import React, { useState } from 'react';
import {
  DataTable,
  DataTableColumn,
  BadgeCell,
  ButtonCell,
} from '../../../../components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Edit, Settings, Users, Shield } from 'lucide-react';

// Define role and permission types based on documentation
export type MasterRole =
  | 'Member'
  | 'Administrator'
  | 'Welon Trust'
  | 'Professional';

export type SubRole = {
  id: string;
  name: string;
  description: string;
  masterRole: MasterRole;
  permissions: string[];
  userCount: number;
};

export type RolePermission = {
  id: string;
  name: string;
  description: string;
  category:
    | 'User Management'
    | 'Content'
    | 'System'
    | 'Emergency'
    | 'Financial'
    | 'Medical';
};

// Mock data for roles and subroles based on documentation
export const mockSubRoles: SubRole[] = [
  // Administrator subroles
  {
    id: 'admin-advanced',
    name: 'Administrator - Advanced',
    description:
      'Full system access including user management, templates, and system settings',
    masterRole: 'Administrator',
    permissions: [
      'user_create',
      'user_edit',
      'user_delete',
      'template_manage',
      'system_settings',
      'audit_logs',
    ],
    userCount: 3,
  },
  {
    id: 'admin-basic',
    name: 'Administrator - Basic',
    description:
      'Basic support tasks like password resets and account assistance',
    masterRole: 'Administrator',
    permissions: ['user_view', 'password_reset', 'account_support'],
    userCount: 5,
  },
  {
    id: 'admin-reporting',
    name: 'Administrator - Reporting',
    description: 'Generate and export usage and compliance reports',
    masterRole: 'Administrator',
    permissions: ['reports_generate', 'reports_export', 'audit_view'],
    userCount: 2,
  },
  {
    id: 'admin-finance',
    name: 'Administrator - Finance',
    description: 'Manage billing, subscriptions, and financial reporting',
    masterRole: 'Administrator',
    permissions: [
      'billing_manage',
      'subscriptions_manage',
      'financial_reports',
    ],
    userCount: 1,
  },
  // Welon Trust subroles
  {
    id: 'welon-advanced',
    name: 'Welon - Advanced',
    description: 'Full trust document access with reporting capabilities',
    masterRole: 'Welon Trust',
    permissions: ['trust_documents_full', 'reports_generate', 'member_notes'],
    userCount: 2,
  },
  {
    id: 'welon-basic',
    name: 'Welon - Basic',
    description: 'View trust documents and update statuses',
    masterRole: 'Welon Trust',
    permissions: ['trust_documents_view', 'status_update', 'document_upload'],
    userCount: 4,
  },
  {
    id: 'welon-emergency',
    name: 'Welon - Emergency Service',
    description: 'Manage emergency access and notifications',
    masterRole: 'Welon Trust',
    permissions: [
      'emergency_access',
      'document_send',
      'contact_notify',
      'action_log',
    ],
    userCount: 3,
  },
  {
    id: 'welon-medical',
    name: 'Welon - Medical',
    description: 'Access medical documents and healthcare provider liaison',
    masterRole: 'Welon Trust',
    permissions: [
      'medical_documents',
      'healthcare_liaison',
      'emergency_medical',
    ],
    userCount: 2,
  },
];

export const mockPermissions: RolePermission[] = [
  // User Management
  {
    id: 'user_create',
    name: 'Create Users',
    description: 'Create new user accounts',
    category: 'User Management',
  },
  {
    id: 'user_edit',
    name: 'Edit Users',
    description: 'Modify user account details',
    category: 'User Management',
  },
  {
    id: 'user_delete',
    name: 'Delete Users',
    description: 'Delete user accounts',
    category: 'User Management',
  },
  {
    id: 'user_view',
    name: 'View Users',
    description: 'View user account information',
    category: 'User Management',
  },

  // Content Management
  {
    id: 'template_manage',
    name: 'Manage Templates',
    description: 'Create and edit document templates',
    category: 'Content',
  },
  {
    id: 'trust_documents_full',
    name: 'Full Trust Access',
    description: 'Full access to trust documents',
    category: 'Content',
  },
  {
    id: 'trust_documents_view',
    name: 'View Trust Documents',
    description: 'Read-only access to trust documents',
    category: 'Content',
  },
  {
    id: 'medical_documents',
    name: 'Medical Documents',
    description: 'Access to medical directives and documents',
    category: 'Medical',
  },

  // System
  {
    id: 'system_settings',
    name: 'System Settings',
    description: 'Configure system-wide settings',
    category: 'System',
  },
  {
    id: 'audit_logs',
    name: 'Audit Logs',
    description: 'View and manage audit logs',
    category: 'System',
  },
  {
    id: 'audit_view',
    name: 'View Audit Logs',
    description: 'Read-only access to audit logs',
    category: 'System',
  },

  // Emergency
  {
    id: 'emergency_access',
    name: 'Emergency Access',
    description: 'Manage emergency document access',
    category: 'Emergency',
  },
  {
    id: 'emergency_medical',
    name: 'Emergency Medical',
    description: 'Emergency medical document access',
    category: 'Emergency',
  },

  // Financial
  {
    id: 'billing_manage',
    name: 'Billing Management',
    description: 'Manage billing and payments',
    category: 'Financial',
  },
  {
    id: 'subscriptions_manage',
    name: 'Subscription Management',
    description: 'Manage user subscriptions',
    category: 'Financial',
  },
  {
    id: 'financial_reports',
    name: 'Financial Reports',
    description: 'Generate financial reports',
    category: 'Financial',
  },

  // Support
  {
    id: 'password_reset',
    name: 'Password Reset',
    description: 'Reset user passwords',
    category: 'User Management',
  },
  {
    id: 'account_support',
    name: 'Account Support',
    description: 'Provide account assistance',
    category: 'User Management',
  },
  {
    id: 'reports_generate',
    name: 'Generate Reports',
    description: 'Create system reports',
    category: 'System',
  },
  {
    id: 'reports_export',
    name: 'Export Reports',
    description: 'Export reports to external formats',
    category: 'System',
  },

  // Document Management
  {
    id: 'status_update',
    name: 'Status Updates',
    description: 'Update document and member statuses',
    category: 'Content',
  },
  {
    id: 'document_upload',
    name: 'Document Upload',
    description: 'Upload signed documents',
    category: 'Content',
  },
  {
    id: 'document_send',
    name: 'Send Documents',
    description: 'Send documents to contacts',
    category: 'Content',
  },
  {
    id: 'member_notes',
    name: 'Member Notes',
    description: 'Add notes to member files',
    category: 'Content',
  },
  {
    id: 'contact_notify',
    name: 'Contact Notifications',
    description: 'Send notifications to emergency contacts',
    category: 'Emergency',
  },
  {
    id: 'action_log',
    name: 'Action Logging',
    description: 'Log actions and activities',
    category: 'System',
  },
  {
    id: 'healthcare_liaison',
    name: 'Healthcare Liaison',
    description: 'Communicate with healthcare providers',
    category: 'Medical',
  },
];

interface RoleManagementTableProps {
  onEditRole: (role: SubRole) => void;
  onBulkEdit: (selectedRoles: SubRole[]) => void;
}

export function RoleManagementTable({
  onEditRole,
  onBulkEdit,
}: RoleManagementTableProps) {
  const [selectedRoles, setSelectedRoles] = useState<SubRole[]>([]);

  const getRoleBadgeColor = (masterRole: MasterRole) => {
    switch (masterRole) {
      case 'Administrator':
        return 'bg-blue-100 text-blue-800';
      case 'Welon Trust':
        return 'bg-green-100 text-green-800';
      case 'Professional':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-[var(--custom-gray-dark)]';
    }
  };

  const columns: DataTableColumn<SubRole>[] = [
    {
      key: 'name',
      header: 'Role Name',
      cell: role => (
        <div className='flex items-center space-x-3'>
          <Shield className='h-4 w-4 text-[var(--custom-gray-medium)]' />
          <div>
            <div className='font-medium'>{role.name}</div>
            <div className='text-sm text-[var(--custom-gray-medium)]'>
              {role.description}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'masterRole',
      header: 'Master Role',
      cell: role => (
        <Badge className={getRoleBadgeColor(role.masterRole)}>
          {role.masterRole}
        </Badge>
      ),
    },
    {
      key: 'permissions',
      header: 'Permissions',
      cell: role => (
        <div className='flex flex-wrap gap-1'>
          {role.permissions.slice(0, 3).map(permission => (
            <Badge key={permission} variant='outline' className='text-xs'>
              {mockPermissions.find(p => p.id === permission)?.name ||
                permission}
            </Badge>
          ))}
          {role.permissions.length > 3 && (
            <Badge variant='outline' className='text-xs'>
              +{role.permissions.length - 3} more
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: 'userCount',
      header: 'Users',
      cell: role => (
        <div className='flex items-center space-x-1'>
          <Users className='h-4 w-4 text-[var(--custom-gray-medium)]' />
          <span>{role.userCount}</span>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: role => (
        <Button
          variant='outline'
          size='sm'
          onClick={() => onEditRole(role)}
          className='flex items-center space-x-1'
        >
          <Edit className='h-3 w-3' />
          <span>Edit</span>
        </Button>
      ),
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Role Statistics */}
      <div className='grid md:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Shield className='h-8 w-8 text-blue-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Total Roles
              </p>
              <p className='text-2xl font-bold'>{mockSubRoles.length}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Settings className='h-8 w-8 text-purple-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Admin Roles
              </p>
              <p className='text-2xl font-bold'>
                {
                  mockSubRoles.filter(r => r.masterRole === 'Administrator')
                    .length
                }
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Shield className='h-8 w-8 text-green-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Welon Roles
              </p>
              <p className='text-2xl font-bold'>
                {
                  mockSubRoles.filter(r => r.masterRole === 'Welon Trust')
                    .length
                }
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Users className='h-8 w-8 text-orange-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Total Users
              </p>
              <p className='text-2xl font-bold'>
                {mockSubRoles.reduce((sum, role) => sum + role.userCount, 0)}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {selectedRoles.length > 0 && (
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                {selectedRoles.length} role(s) selected
              </span>
              <Button
                onClick={() => onBulkEdit(selectedRoles)}
                className='flex items-center space-x-1'
              >
                <Edit className='h-4 w-4' />
                <span>Bulk Edit</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Roles & Subroles</CardTitle>
          <CardDescription>
            Manage role permissions and assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={mockSubRoles}
            columns={columns}
            keyField='id'
            searchField='name'
            searchPlaceholder='Search roles...'
          />
        </CardContent>
      </Card>
    </div>
  );
}
