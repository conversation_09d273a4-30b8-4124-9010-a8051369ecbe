'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  <PERSON><PERSON><PERSON>riangle,
  FileText,
  Eye,
  Calendar,
  Download,
} from 'lucide-react';

// Mock data for quarterly reviews
const quarterlyReviews = [
  {
    id: 'QR-2023-Q2',
    quarter: 'Q2 2023',
    startDate: '2023-04-01',
    endDate: '2023-06-30',
    status: 'In Progress',
    progress: 65,
    dueDate: '2023-07-15',
    totalTemplates: 20,
    reviewedTemplates: 13,
    signedOffTemplates: 13,
  },
  {
    id: 'QR-2023-Q1',
    quarter: 'Q1 2023',
    startDate: '2023-01-01',
    endDate: '2023-03-31',
    status: 'Completed',
    progress: 100,
    dueDate: '2023-04-15',
    totalTemplates: 18,
    reviewedTemplates: 18,
    signedOffTemplates: 18,
  },
  {
    id: 'QR-2022-Q4',
    quarter: 'Q4 2022',
    startDate: '2022-10-01',
    endDate: '2022-12-31',
    status: 'Completed',
    progress: 100,
    dueDate: '2023-01-15',
    totalTemplates: 15,
    reviewedTemplates: 15,
    signedOffTemplates: 15,
  },
];

// Define the type for template
type ReviewTemplate = {
  id: string;
  state: string;
  type: string;
  version: string;
  lastReviewDate: string | null;
  reviewer: string | null;
  status: string;
  signedOff: boolean;
};

// Define the type for templates in review
type TemplatesInReviewMap = {
  [key: string]: ReviewTemplate[];
};

// Mock data for templates in review
const templatesInReview: TemplatesInReviewMap = {
  'QR-2023-Q2': [
    {
      id: '1',
      state: 'California',
      type: 'Will',
      version: '1.0',
      lastReviewDate: '2023-01-15',
      reviewer: 'John Smith',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '2',
      state: 'California',
      type: 'Trust',
      version: '2.1',
      lastReviewDate: '2023-02-10',
      reviewer: 'Jane Doe',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '3',
      state: 'New York',
      type: 'Healthcare POA',
      version: '1.2',
      lastReviewDate: '2023-03-05',
      reviewer: 'Robert Johnson',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '4',
      state: 'Texas',
      type: 'Financial POA',
      version: '1.0',
      lastReviewDate: '2023-01-20',
      reviewer: 'Emily Davis',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '5',
      state: 'Florida',
      type: 'Advance Directive',
      version: '1.1',
      lastReviewDate: '2023-02-25',
      reviewer: 'Michael Wilson',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '6',
      state: 'Arizona',
      type: 'Will',
      version: '1.0',
      lastReviewDate: '2023-03-10',
      reviewer: 'Sarah Brown',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '7',
      state: 'Washington',
      type: 'Trust',
      version: '1.2',
      lastReviewDate: '2023-01-30',
      reviewer: 'David Miller',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '8',
      state: 'Oregon',
      type: 'Healthcare POA',
      version: '1.0',
      lastReviewDate: '2023-02-15',
      reviewer: 'Jennifer Taylor',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '9',
      state: 'Colorado',
      type: 'Financial POA',
      version: '1.1',
      lastReviewDate: '2023-03-20',
      reviewer: 'James Anderson',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '10',
      state: 'Nevada',
      type: 'Advance Directive',
      version: '1.0',
      lastReviewDate: '2023-01-25',
      reviewer: 'Lisa Thomas',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '11',
      state: 'Utah',
      type: 'Will',
      version: '1.1',
      lastReviewDate: '2023-02-20',
      reviewer: 'Richard Harris',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '12',
      state: 'Idaho',
      type: 'Trust',
      version: '1.0',
      lastReviewDate: '2023-03-15',
      reviewer: 'Patricia Clark',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '13',
      state: 'Montana',
      type: 'Healthcare POA',
      version: '1.0',
      lastReviewDate: '2023-01-10',
      reviewer: 'William Lee',
      status: 'Reviewed',
      signedOff: true,
    },
    {
      id: '14',
      state: 'Wyoming',
      type: 'Financial POA',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '15',
      state: 'New Mexico',
      type: 'Advance Directive',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '16',
      state: 'Oklahoma',
      type: 'Will',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '17',
      state: 'Kansas',
      type: 'Trust',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '18',
      state: 'Nebraska',
      type: 'Healthcare POA',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '19',
      state: 'South Dakota',
      type: 'Financial POA',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
    {
      id: '20',
      state: 'North Dakota',
      type: 'Advance Directive',
      version: '1.0',
      lastReviewDate: null,
      reviewer: null,
      status: 'Pending Review',
      signedOff: false,
    },
  ],
  'QR-2023-Q1': [
    // Similar structure for Q1 2023 templates
  ],
  'QR-2022-Q4': [
    // Similar structure for Q4 2022 templates
  ],
};

export default function QuarterlyReviewPage() {
  const router = useRouter();
  const [selectedReview, setSelectedReview] = useState<string>('QR-2023-Q2');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [selectedTemplate, setSelectedTemplate] =
    useState<ReviewTemplate | null>(null);
  const [reviewComment, setReviewComment] = useState<string>('');
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState<boolean>(false);

  // Get selected review details
  const selectedReviewDetails = quarterlyReviews.find(
    review => review.id === selectedReview
  );

  // Get templates for selected review
  const reviewTemplates = templatesInReview[selectedReview] || [];

  // Filter templates based on status
  const filteredTemplates =
    statusFilter === 'All'
      ? reviewTemplates
      : reviewTemplates.filter(template => template.status === statusFilter);

  const handleSelectReview = (reviewId: string) => {
    setSelectedReview(reviewId);
    setStatusFilter('All');
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  const handleReviewTemplate = (template: ReviewTemplate) => {
    setSelectedTemplate(template);
    setReviewComment('');
    setIsReviewDialogOpen(true);
  };

  const handleSubmitReview = () => {
    // In a real implementation, this would submit the review
    setIsReviewDialogOpen(false);
  };

  const handleViewTemplate = (templateId: string) => {
    router.push(`/admin/templates/edit/${templateId}`);
  };

  const handleDownloadReport = () => {
    // In a real implementation, this would download a report
    alert('Downloading quarterly review report...');
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not reviewed';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex justify-between items-center mb-6'>
        <div className='flex items-center'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => router.push('/admin/templates')}
            className='mr-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Templates
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
              Quarterly Review Dashboard
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Track quarterly legal review signatures and compliance
            </p>
          </div>
        </div>

        <Button variant='outline' size='sm' onClick={handleDownloadReport}>
          <Download className='mr-2 h-4 w-4' />
          Download Report
        </Button>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <div className='md:col-span-1'>
          <Card>
            <CardHeader>
              <CardTitle>Quarterly Reviews</CardTitle>
              <CardDescription>
                Select a quarter to view templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {quarterlyReviews.map(review => (
                  <div
                    key={review.id}
                    className={`p-4 border rounded-md cursor-pointer transition-colors ${
                      selectedReview === review.id
                        ? 'border-green-2010c bg-green-2010c/10'
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => handleSelectReview(review.id)}
                  >
                    <div className='flex justify-between items-start mb-2'>
                      <h3 className='font-medium'>{review.quarter} Review</h3>
                      <Badge
                        variant={
                          review.status === 'Completed' ? 'default' : 'outline'
                        }
                        className={
                          review.status === 'Completed' ? 'bg-green-2010c' : ''
                        }
                      >
                        {review.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      <Calendar className='inline-block h-3 w-3 mr-1' />
                      {formatDate(review.startDate)} -{' '}
                      {formatDate(review.endDate)}
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                      Due: {formatDate(review.dueDate)}
                    </p>

                    <div className='space-y-1'>
                      <div className='flex justify-between text-sm'>
                        <span>Progress</span>
                        <span>{review.progress}%</span>
                      </div>
                      <Progress value={review.progress} className='h-2' />
                      <div className='flex justify-between text-xs text-[var(--custom-gray-medium)] mt-1'>
                        <span>
                          {review.reviewedTemplates}/{review.totalTemplates}{' '}
                          reviewed
                        </span>
                        <span>
                          {review.signedOffTemplates}/{review.totalTemplates}{' '}
                          signed off
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='md:col-span-2'>
          {selectedReviewDetails && (
            <>
              <Card className='mb-6'>
                <CardHeader>
                  <CardTitle>{selectedReviewDetails.quarter} Review</CardTitle>
                  <CardDescription>
                    {formatDate(selectedReviewDetails.startDate)} -{' '}
                    {formatDate(selectedReviewDetails.endDate)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-4'>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Status
                      </p>
                      <p>{selectedReviewDetails.status}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Due Date
                      </p>
                      <p>{formatDate(selectedReviewDetails.dueDate)}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Total Templates
                      </p>
                      <p>{selectedReviewDetails.totalTemplates}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Reviewed
                      </p>
                      <p>
                        {selectedReviewDetails.reviewedTemplates}/
                        {selectedReviewDetails.totalTemplates}
                      </p>
                    </div>
                  </div>

                  <div className='space-y-1'>
                    <div className='flex justify-between text-sm'>
                      <span>Overall Progress</span>
                      <span>{selectedReviewDetails.progress}%</span>
                    </div>
                    <Progress
                      value={selectedReviewDetails.progress}
                      className='h-2'
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Templates for Review</CardTitle>
                  <CardDescription>
                    Templates that need quarterly review
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='mb-4'>
                    <Select
                      value={statusFilter}
                      onValueChange={handleStatusFilterChange}
                    >
                      <SelectTrigger className='w-[200px]'>
                        <SelectValue placeholder='Filter by status' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='All'>All Statuses</SelectItem>
                        <SelectItem value='Reviewed'>Reviewed</SelectItem>
                        <SelectItem value='Pending Review'>
                          Pending Review
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {filteredTemplates.length === 0 ? (
                    <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                      No templates found matching your filter.
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>State</TableHead>
                          <TableHead>Document Type</TableHead>
                          <TableHead>Version</TableHead>
                          <TableHead>Last Review</TableHead>
                          <TableHead>Reviewer</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTemplates.map(template => (
                          <TableRow key={template.id}>
                            <TableCell>{template.state}</TableCell>
                            <TableCell>{template.type}</TableCell>
                            <TableCell>v{template.version}</TableCell>
                            <TableCell>
                              {formatDate(template.lastReviewDate)}
                            </TableCell>
                            <TableCell>
                              {template.reviewer || 'Not assigned'}
                            </TableCell>
                            <TableCell>
                              <div className='flex items-center'>
                                {template.status === 'Reviewed' ? (
                                  <CheckCircle className='mr-2 h-4 w-4 text-green-500' />
                                ) : (
                                  <Clock className='mr-2 h-4 w-4 text-amber-500' />
                                )}
                                {template.status}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className='flex space-x-1'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    handleViewTemplate(template.id)
                                  }
                                  className='h-8 w-8 p-0'
                                >
                                  <Eye className='h-4 w-4' />
                                  <span className='sr-only'>View</span>
                                </Button>
                                {template.status !== 'Reviewed' && (
                                  <Button
                                    variant='default'
                                    size='sm'
                                    onClick={() =>
                                      handleReviewTemplate(template)
                                    }
                                    className='h-8 w-8 p-0'
                                  >
                                    <FileText className='h-4 w-4' />
                                    <span className='sr-only'>Review</span>
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>

      <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
        <DialogContent className='sm:max-w-[600px]'>
          <DialogHeader>
            <DialogTitle>Review Template</DialogTitle>
            <DialogDescription>
              {selectedTemplate &&
                `${selectedTemplate.state} ${selectedTemplate.type} (v${selectedTemplate.version})`}
            </DialogDescription>
          </DialogHeader>

          <div className='py-4'>
            <label className='text-sm font-medium mb-2 block'>
              Review Comments
            </label>
            <Textarea
              value={reviewComment}
              onChange={e => setReviewComment(e.target.value)}
              placeholder='Enter your review comments here...'
              className='min-h-[150px]'
            />
          </div>

          <DialogFooter>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setIsReviewDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant='default'
              size='sm'
              onClick={handleSubmitReview}
              disabled={!reviewComment.trim()}
            >
              <CheckCircle className='mr-2 h-4 w-4' />
              Sign Off
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
