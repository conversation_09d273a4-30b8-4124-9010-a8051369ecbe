'use client';

import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import type { AuthUser } from 'aws-amplify/auth';
import { getCurrentUser, signOut, fetchAuthSession } from 'aws-amplify/auth';
import { fetchUserByCognitoId } from '@/lib/data/users';
import {
  getUserOnboardingStatus,
  OnboardingStep,
} from '@/utils/userOnboarding';
import InactivityTimer from '@/app/components/InactivityTimer';

// Define the shape of the context
interface AuthContextType {
  user: AuthUser | null;
  userId: string | null;
  onboardingStatus: OnboardingStep;
  loading: boolean;
  refreshUser: () => Promise<boolean>;
  logout: () => Promise<void>;
  userRoles: string[];
  userSubrole: string | null;
  updateOnboardingStatus: () => Promise<void>;
  isRememberMeSession: boolean;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  userId: null,
  loading: true,
  onboardingStatus: OnboardingStep.CHILD_STATUS,
  refreshUser: async () => false,
  logout: async () => {},
  userRoles: [],
  userSubrole: null,
  updateOnboardingStatus: async () => {},
  isRememberMeSession: false,
});

// Custom hook to use the auth context
export function useAuth() {
  return useContext(AuthContext);
}

// Provider component that wraps the app and makes auth object available to any child component that calls useAuth()
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [userRoles, setRoles] = useState<string[]>([]);
  const [userSubrole, setUserSubrole] = useState<string | null>(null);
  const [onboardingStatus, setOnboardingStatus] = useState(
    OnboardingStep.CHILD_STATUS
  );
  const [isRememberMeSession, setIsRememberMeSession] = useState(false);

  // LOADING STATES
  const [loading, setLoading] = useState(true);

  // Function to fetch the current authenticated user
  const fetchUser = async () => {
    try {
      // Check if user selected "Remember Me" to determine refresh strategy
      const rememberMe = localStorage.getItem('rememberMe') === 'true';
      setIsRememberMeSession(rememberMe);

      // Only force refresh tokens if "Remember Me" was selected
      // Otherwise, let tokens expire naturally after 5 minutes
      const [session, userData] = await Promise.all([
        fetchAuthSession({ forceRefresh: rememberMe }),
        getCurrentUser(),
      ]);

      const userFullData = await fetchUserByCognitoId(userData.userId);
      setUserId(userFullData?.id ?? null);
      setUserSubrole(userFullData?.subrole ?? null);

      // Extract user groups from the session
      const groupsPayload =
        session.tokens?.accessToken?.payload['cognito:groups'];
      const groups = Array.isArray(groupsPayload)
        ? (groupsPayload as string[])
        : [];
      setRoles(groups);

      // Fetch onboarding status after we have user data (since it needs the user)
      const onboardingStatus = await getUserOnboardingStatus();

      const isValidStep = Object.values(OnboardingStep).includes(
        onboardingStatus.currentStep as OnboardingStep
      );
      setOnboardingStatus(
        isValidStep
          ? (onboardingStatus.currentStep as OnboardingStep)
          : OnboardingStep.CHILD_STATUS
      );

      setUser(userData);
    } catch (error) {
      console.log('===> Not signed in');
      setUser(null);
      setUserSubrole(null);
    } finally {
      setLoading(false);
    }
  };

  // Function to refresh the user data
  async function refreshUser() {
    setLoading(true);
    try {
      await fetchUser();
      // Return true if user is authenticated, false otherwise
      return !!user;
    } catch (error) {
      console.error('Error refreshing user:', error);
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Function to handle logout
  async function logout() {
    try {
      await signOut();
      setUser(null);
      setUserSubrole(null);
      setIsRememberMeSession(false);
      // Clear remember me preference on logout
      localStorage.removeItem('rememberMe');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  // Function to update only the onboarding status
  async function updateOnboardingStatus() {
    try {
      const onboardingStatus = await getUserOnboardingStatus();

      const isValidStep = Object.values(OnboardingStep).includes(
        onboardingStatus.currentStep as OnboardingStep
      );
      setOnboardingStatus(
        isValidStep
          ? (onboardingStatus.currentStep as OnboardingStep)
          : OnboardingStep.CHILD_STATUS
      );
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  }

  // Fetch user on initial load
  useEffect(() => {
    fetchUser();
  }, []);

  const value = {
    user,
    loading,
    onboardingStatus,
    userId,
    refreshUser,
    logout,
    userRoles,
    userSubrole,
    updateOnboardingStatus,
    isRememberMeSession,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      <InactivityTimer timeoutMinutes={30} warningMinutes={25} />
    </AuthContext.Provider>
  );
}
