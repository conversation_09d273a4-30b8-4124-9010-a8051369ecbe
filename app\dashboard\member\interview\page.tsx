'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  InterviewNewProvider,
  useInterviewNew,
} from '@/components/interview/interview-new-context';
import { QuestionCard } from '@/components/interview/question-new';
import { ProgressBarNew } from '@/components/interview/progress-bar-new';
import { useAuth } from '@/app/context/AuthContext';
import { CheckCircle, RotateCcw, Save, Home, ArrowRight } from 'lucide-react';

const InterviewSelectionCard: React.FC = () => {
  const { interviews, selectInterview, isLoading } = useInterviewNew();
  const router = useRouter();

  if (isLoading) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='animate-pulse space-y-4'>
            <div className='h-4 bg-gray-200 rounded w-1/2'></div>
            <div className='h-20 bg-gray-200 rounded'></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (interviews.length === 0) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardHeader>
          <CardTitle>No Interviews Available</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-gray-600 mb-4'>
            There are currently no active interviews available. Please check
            back later or contact support.
          </p>
          <Button onClick={() => router.push('/dashboard')}>
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Sort interviews by question count (most questions first)
  const sortedInterviews = [...interviews].sort(
    (a, b) =>
      b.latestVersion.questions.length - a.latestVersion.questions.length
  );

  return (
    <div className='space-y-4'>
      <h2 className='text-xl font-semibold text-gray-900 mb-4'>
        Available Interviews
        <span className='text-sm font-normal text-gray-500 ml-2'>
          (Sorted by question count)
        </span>
      </h2>
      {sortedInterviews.map(interview => (
        <Card
          key={interview.id}
          className='cursor-pointer hover:shadow-md transition-shadow'
        >
          <CardHeader>
            <CardTitle className='flex items-center justify-between'>
              {interview.name}
              <ArrowRight className='w-5 h-5 text-gray-400' />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-gray-600 mb-4'>
              {interview.description ||
                'Complete this interview to help us create your personalized documents.'}
            </p>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-4'>
                <span className='text-sm text-gray-500'>
                  {interview.latestVersion.questions.length} questions
                </span>
                <span className='text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full'>
                  {interview.latestVersion.questions.length >= 8
                    ? 'Comprehensive'
                    : interview.latestVersion.questions.length >= 5
                      ? 'Standard'
                      : 'Quick'}
                </span>
              </div>
              <Button
                onClick={() => selectInterview(interview.id)}
                className='flex items-center gap-2'
              >
                Start Interview
                <ArrowRight className='w-4 h-4' />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const InterviewContent: React.FC = () => {
  const {
    currentInterview,
    isComplete,
    isLoading,
    error,
    resetInterview,
    getProgress,
  } = useInterviewNew();
  const router = useRouter();

  const progress = getProgress();

  // Show interview selection if no interview is selected
  if (!currentInterview && !isLoading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-8'>
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>
              Estate Planning Interview
            </h1>
            <p className='text-gray-600'>
              Choose an interview to get started with your estate planning
              process.
            </p>
          </div>
          <InterviewSelectionCard />
        </div>
      </div>
    );
  }

  // Show completion screen
  if (isComplete) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <Card className='text-center'>
            <CardHeader>
              <div className='flex justify-center mb-4'>
                <CheckCircle className='w-16 h-16 text-green-500' />
              </div>
              <CardTitle className='text-2xl font-bold text-green-700'>
                Interview Completed!
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-gray-600'>
                Congratulations! You have successfully completed the estate
                planning interview. Your responses have been saved and will be
                used to create your personalized documents.
              </p>

              <div className='flex justify-center gap-4 pt-4'>
                <Button
                  onClick={() => router.push('/dashboard')}
                  className='flex items-center gap-2'
                >
                  <Home className='w-4 h-4' />
                  Return to Dashboard
                </Button>

                <Button
                  variant='outline'
                  onClick={() => {
                    if (
                      confirm(
                        'Are you sure you want to retake the interview? This will reset all your current answers.'
                      )
                    ) {
                      resetInterview();
                    }
                  }}
                  className='flex items-center gap-2'
                >
                  <RotateCcw className='w-4 h-4' />
                  Retake Interview
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        {/* Header */}
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            {currentInterview?.name || 'Estate Planning Interview'}
          </h1>
          <p className='text-gray-600'>
            {currentInterview?.description ||
              "Let's walk through your estate plan together. We'll ask you a series of questions to help create your personalized estate documents."}
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className='mb-6 border-red-200 bg-red-50'>
            <AlertDescription className='text-red-700'>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Progress Bar */}
        <ProgressBarNew />

        {/* Question Card */}
        <QuestionCard />

        {/* Action Buttons */}
        <div className='mt-8 flex justify-between'>
          <Button
            variant='outline'
            size='default'
            onClick={() => {
              if (
                confirm(
                  'Are you sure you want to save and exit? Your progress will be saved.'
                )
              ) {
                router.push('/dashboard');
              }
            }}
            className='flex items-center gap-2'
          >
            <Save className='w-4 h-4' />
            Save and Exit
          </Button>

          <Button
            variant='destructive'
            size='default'
            onClick={() => {
              if (
                confirm(
                  'Are you sure you want to reset the interview? All your answers will be lost.'
                )
              ) {
                resetInterview();
              }
            }}
            className='flex items-center gap-2'
          >
            <RotateCcw className='w-4 h-4' />
            Reset Interview
          </Button>
        </div>

        {/* Progress Summary */}
        {progress > 0 && (
          <Card className='mt-6 bg-blue-50 border-blue-200'>
            <CardContent className='p-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-blue-900'>
                    Interview Progress
                  </p>
                  <p className='text-xs text-blue-700'>
                    You've completed {progress}% of the questions
                  </p>
                </div>
                <div className='text-2xl font-bold text-blue-600'>
                  {progress}%
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

const InterviewPageContent: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();

  // Show authentication required message
  if (!user) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <Card>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-gray-600 mb-4'>
                You need to be logged in to access the interview.
              </p>
              <Button onClick={() => router.push('/login')}>Go to Login</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <InterviewNewProvider defaultInterviewSelector='mostQuestions'>
      <InterviewContent />
    </InterviewNewProvider>
  );
};

export default function InterviewPage() {
  return <InterviewPageContent />;
}
