import rolesConfig from './roles.json';

export type UserRole =
  | 'member'
  | 'administrator'
  | 'welon_trust'
  | 'linked_account';

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  routes: string[];
}

export interface Subrole {
  id: string;
  name: string;
  description: string;
  additional_permissions: string[];
}

export interface UserContext {
  role: UserRole;
  subrole?: string;
  permissions: string[];
  displayName: string;
  routes: string[];
}

export class RoleManager {
  private static instance: RoleManager;
  private roles: Record<string, Role>;
  private subroles: Record<string, Record<string, Subrole>>;

  private constructor() {
    this.roles = rolesConfig.roles as Record<string, Role>;
    this.subroles = rolesConfig.subroles as Record<
      string,
      Record<string, Subrole>
    >;
  }

  public static getInstance(): RoleManager {
    if (!RoleManager.instance) {
      RoleManager.instance = new RoleManager();
    }
    return RoleManager.instance;
  }

  public getRole(roleId: UserRole): Role | null {
    return this.roles[roleId] || null;
  }

  public getSubrole(roleId: UserRole, subroleKey: string): Subrole | null {
    return this.subroles[roleId]?.[subroleKey] || null;
  }

  public getAllRoles(): Role[] {
    return Object.values(this.roles);
  }

  public getSubrolesForRole(roleId: UserRole): Subrole[] {
    return Object.values(this.subroles[roleId] || {});
  }

  public getUserContext(roleId: UserRole, subroleKey?: string): UserContext {
    const role = this.getRole(roleId);
    if (!role) {
      throw new Error(`Role ${roleId} not found`);
    }

    let permissions = [...role.permissions];
    let displayName = role.name;

    if (subroleKey) {
      const subrole = this.getSubrole(roleId, subroleKey);
      if (subrole) {
        permissions = [...permissions, ...subrole.additional_permissions];
        displayName = subrole.name;
      }
    }

    return {
      role: roleId,
      subrole: subroleKey,
      permissions,
      displayName,
      routes: role.routes,
    };
  }

  public hasPermission(userContext: UserContext, permission: string): boolean {
    return userContext.permissions.includes(permission);
  }

  public canAccessRoute(userContext: UserContext, route: string): boolean {
    return userContext.routes.some(allowedRoute => {
      if (allowedRoute.endsWith('/*')) {
        const basePath = allowedRoute.slice(0, -2);
        return route.startsWith(basePath);
      }
      return route === allowedRoute;
    });
  }

  // Test users for role switching
  public getTestUsers(): Array<{
    role: UserRole;
    subrole?: string;
    name: string;
  }> {
    return [
      { role: 'member', name: 'John Doe (Member)' },
      { role: 'administrator', subrole: 'advanced', name: 'Admin Advanced' },
      { role: 'administrator', subrole: 'basic', name: 'Admin Basic' },
      { role: 'welon_trust', subrole: 'basic', name: 'Welon Staff' },
      { role: 'welon_trust', subrole: 'advanced', name: 'Welon Advanced' },
      { role: 'linked_account', subrole: 'spouse', name: 'Spouse Account' },
      { role: 'linked_account', subrole: 'family', name: 'Family Member' },
    ];
  }
}
