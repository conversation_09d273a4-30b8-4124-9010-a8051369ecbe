'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserPlus, AlertCircle, Users } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useEmergencyContacts } from '@/hooks/useEmergencyContacts';
import useModal from '@/hooks/useModal';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { Headline, Subhead } from '@/components/ui/brand/typography';
import { toast } from 'sonner';
import type { EmergencyContact } from '@/hooks/useEmergencyContacts';
import Link from 'next/link';
import { useRouter } from 'next/navigation'; // Use navigation router
import { ContactListSkeleton } from '@/components/ui/skeletons/contact-list-skeleton';

export default function EmergencyContactsPage() {
  const router = useRouter(); // Initialize router
  const { contacts, loading, error, deleteContact, refreshContacts } =
    useEmergencyContacts();
  const deleteConfirmModal = useModal();
  const [contactToDelete, setContactToDelete] =
    useState<EmergencyContact | null>(null);

  // Ensure contacts are loaded when the page mounts
  useEffect(() => {
    refreshContacts();
  }, [refreshContacts]);

  const handleDeleteClick = (contact: EmergencyContact) => {
    setContactToDelete(contact);
    deleteConfirmModal.showModal();
  };

  const handleConfirmDelete = async () => {
    if (!contactToDelete) return;

    try {
      await deleteContact(contactToDelete.id);
      toast.success(
        `${contactToDelete.fullName} has been removed from your emergency contacts.`
      );
      deleteConfirmModal.hideModal();
      setContactToDelete(null);
    } catch (error) {
      toast.error('Failed to delete contact. Please try again.');
      console.error('Error deleting contact:', error);
    }
  };

  const handleEditContact = (contact: EmergencyContact) => {
    router.push(`/dashboard/member/emergency-contacts/edit?id=${contact.id}`);
  };

  // Render function for contact item to avoid hydration issues
  const renderContact = (contact: EmergencyContact) => (
    <Card key={contact.id} className='overflow-hidden'>
      <CardContent className='p-6'>
        <div className='flex justify-between items-start'>
          <div className='space-y-4'>
            <div className='flex items-center'>
              <h3 className='text-lg font-semibold'>{contact.fullName}</h3>
              {contact.isPrimaryForType && (
                <span className='ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full'>
                  Primary {contact.contactType}
                </span>
              )}
            </div>

            <p>{contact.relationship}</p>

            <div className='space-y-1'>
              <div className='flex items-center'>
                <span className='text-muted-foreground mr-2'>Email:</span>
                <span>{contact.emailAddress}</span>
              </div>
              <div className='flex items-center'>
                <span className='text-muted-foreground mr-2'>Phone:</span>
                <span>{contact.phoneNumber}</span>
              </div>
              <div className='flex items-center'>
                <span className='text-muted-foreground mr-2'>Type:</span>
                <span>{contact.contactType}</span>
              </div>
            </div>
          </div>
          <div className='flex gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => handleEditContact(contact)}
            >
              Edit
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => handleDeleteClick(contact)}
              className='text-destructive hover:text-destructive'
            >
              Delete
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className='max-w-7xl mx-auto'>
      <Card className='mb-8 p-6'>
        <Headline className='mb-2'>Emergency Contacts</Headline>
        <Subhead className='text-muted-foreground'>
          Designate trusted individuals to receive specific information in
          emergencies.
        </Subhead>
      </Card>

      {/* Add Contact Button */}
      <div className='mb-6 flex justify-end'>
        <Button
          className='bg-[var(--off-black)] text-white hover:text-white'
          asChild
        >
          <Link href='/dashboard/member/emergency-contacts/add'>
            <UserPlus className='mr-2 h-4 w-4' />
            Add Emergency Contact
          </Link>
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>
            Error loading contacts: {error}
            <div className='mt-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => refreshContacts()}
              >
                Try again
              </Button>{' '}
              or contact support.
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Contact List */}
      {loading ? (
        <ContactListSkeleton count={3} />
      ) : !error && contacts.length === 0 ? (
        <Card className='bg-muted/50'>
          <CardContent className='py-8 text-center'>
            <Users className='h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50' />
            <p className='text-muted-foreground mb-4'>
              No emergency contacts found
            </p>
            <Button asChild>
              <Link href='/dashboard/member/emergency-contacts/add'>
                <UserPlus className='h-4 w-4 mr-2' />
                Add First Contact
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className='space-y-4'>
          {contacts.map(contact => renderContact(contact))}
        </div>
      )}

      {/* About Emergency Contacts Card */}
      <Card className='mt-8 bg-[#0f172a] text-white'>
        <CardContent className='p-6 space-y-6'>
          <div>
            <h2 className='text-xl font-bold mb-2'>About Emergency Contacts</h2>
            <p>
              Emergency contacts can receive specific information if you're
              unable to communicate.
            </p>
          </div>

          <div>
            <h3 className='text-lg font-semibold mb-1'>Medical Contacts</h3>
            <p className='text-[var(--off-white)]'>
              Receive medical details such as allergies and medical power of
              attorney.
            </p>
          </div>

          <div>
            <h3 className='text-lg font-semibold mb-1'>Other Contacts</h3>
            <p className='text-[var(--off-white)]'>
              Receive non-medical details such as pet information and access
              instructions.
            </p>
          </div>

          <div className='pt-2'>
            <p className='text-sm'>
              <strong>Note:</strong> All emergency information requests are
              routed through a call center for verification.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteConfirmModal.isVisible}
        onOpenChange={deleteConfirmModal.setIsVisible}
        title='Delete Emergency Contact'
        description='Are you sure you want to delete this emergency contact? This action cannot be undone.'
        confirmLabel='Delete Contact'
        confirmVariant='destructive'
        onConfirm={handleConfirmDelete}
        className='sm:max-w-md'
        icon={<AlertCircle className='h-6 w-6 text-red-500' />}
      >
        {contactToDelete ? (
          <p className='text-sm'>
            This will permanently remove {contactToDelete.fullName} from your
            emergency contacts.
          </p>
        ) : (
          <p className='text-sm'>
            This will permanently remove this contact from your emergency
            contacts.
          </p>
        )}
      </ConfirmationDialog>
    </div>
  );
}
