import {
  Interview,
  InterviewVersion,
  Interview<PERSON><PERSON><PERSON>,
  CreateVersionRequest,
  UpdateVersionRequest,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  CreateInterviewRequest,
  UpdateInterviewRequest,
  VersionListResponse,
  QuestionListResponse,
  TemplateVariable,
  QuestionType,
  QuestionCategory,
  QuestionOption,
  InterviewSetWithVersion,
} from '@/types/interview-builder';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

// API response type for getting interview
interface GetInterviewResponse {
  interview: Interview | any; // Mock type to handle interview sets array
}

import { SIMPLE_BASIC_YAML } from '@/lib/sample-yaml-interviews';

// Generate the Amplify data client
const client = generateClient<Schema>();

// Mock data for development - Single interview with one version
const mockVersions: InterviewVersion[] = [
  {
    id: 'v1',
    version: 1,
    name: 'Advanced Estate Planning Interview',
    description:
      'Comprehensive estate planning with complex conditional logic and branching',
    status: 'draft',
    targetTemplate: 'advanced-estate-plan',
    questions: [],
    totalQuestions: 3,
    estimatedDuration: 15,
    yamlContent: SIMPLE_BASIC_YAML,
    createdBy: '<EMAIL>',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
];

const mockInterview: Interview = {
  id: '1',
  currentVersion: 1,
  versions: mockVersions,
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z',
};

const mockQuestions: InterviewQuestion[] = [
  {
    id: 'q1',
    text: 'What is your full legal name?',
    type: 'text',
    category: 'personal',
    required: true,
    helpText: 'Enter your name exactly as it appears on legal documents',
    placeholder: 'First Middle Last',
    validation: [
      { type: 'required', message: 'Full name is required' },
      {
        type: 'minLength',
        value: 2,
        message: 'Name must be at least 2 characters',
      },
    ],
    defaultNextQuestionId: 'q2',
    templateMapping: 'client_full_name',
    order: 1,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'q2',
    text: 'What is your date of birth?',
    type: 'date',
    category: 'personal',
    required: true,
    helpText: 'This information is required for legal document preparation',
    validation: [{ type: 'required', message: 'Date of birth is required' }],
    defaultNextQuestionId: 'q3',
    templateMapping: 'client_date_of_birth',
    order: 2,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 'q3',
    text: 'Do you own a home?',
    type: 'radio',
    category: 'financial',
    required: true,
    helpText:
      'This helps us determine how to handle real estate in your estate plan',
    options: [
      {
        id: 'opt1',
        label: 'Yes, I own my primary residence',
        value: 'yes_primary',
      },
      {
        id: 'opt2',
        label: 'Yes, I own multiple properties',
        value: 'yes_multiple',
      },
      { id: 'opt3', label: 'No, I rent or live with family', value: 'no' },
    ],
    conditionalLogic: [
      { condition: 'equals', value: 'yes_primary', nextQuestionId: 'q4' },
      { condition: 'equals', value: 'yes_multiple', nextQuestionId: 'q5' },
      { condition: 'equals', value: 'no', nextQuestionId: 'q6' },
    ],
    templateMapping: 'home_ownership_status',
    order: 3,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
];

const mockTemplateVariables: TemplateVariable[] = [
  {
    id: '1',
    name: 'client_full_name',
    description: "Client's full legal name",
    type: 'text',
    category: 'Personal Information',
  },
  {
    id: '2',
    name: 'client_date_of_birth',
    description: "Client's date of birth",
    type: 'date',
    category: 'Personal Information',
  },
  {
    id: '3',
    name: 'client_address',
    description: "Client's primary address",
    type: 'text',
    category: 'Personal Information',
  },
  {
    id: '4',
    name: 'spouse_name',
    description: "Spouse's full name",
    type: 'text',
    category: 'Family Information',
  },
  {
    id: '5',
    name: 'primary_beneficiary',
    description: 'Primary beneficiary name',
    type: 'text',
    category: 'Estate Planning',
  },
  {
    id: '6',
    name: 'executor_name',
    description: "Executor's full name",
    type: 'text',
    category: 'Estate Planning',
  },
  {
    id: '7',
    name: 'home_ownership_status',
    description: 'Home ownership status',
    type: 'text',
    category: 'Financial Assets',
  },
  {
    id: '8',
    name: 'total_estate_value',
    description: 'Estimated total estate value',
    type: 'number',
    category: 'Financial Assets',
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// API Functions
export const getInterview = async (): Promise<GetInterviewResponse> => {
  await delay(300);
  return {
    interview: mockInterview,
  };
};

export const getInterviewsList = async (): Promise<
  InterviewSetWithVersion[]
> => {
  try {
    const { data: interviewSets, errors } =
      await client.models.InterviewSet.list({
        selectionSet: [
          'id',
          'name',
          'description',
          'isActive',
          'createdAt',
          'updatedAt',
          'versions.versionNumber',
          'versions.questions.id',
        ],
      });

    if (errors) {
      console.error('Failed to fetch interview sets:', errors);
      return [];
    }
    const setsWithLatestVersion = interviewSets.map(set => {
      const versions = set.versions ?? [];

      // Type the reduce function properly
      const latestVersion = versions.reduce<(typeof versions)[0] | null>(
        (latest, version) => {
          return !latest ||
            (version.versionNumber &&
              (!latest.versionNumber ||
                version.versionNumber > latest.versionNumber))
            ? version
            : latest;
        },
        null
      );

      return {
        id: set.id,
        name: set.name,
        description: set.description,
        isActive: set.isActive,
        createdAt: set.createdAt,
        updatedAt: set.updatedAt,
        latestVersionNumber: latestVersion?.versionNumber ?? null,
        questionsCount: latestVersion?.questions?.length ?? 0,
      };
    });

    return setsWithLatestVersion;
  } catch (error) {
    console.error('Error fetching interview sets:', error);
    return [];
  }
};

export const createInterview = async (
  data: CreateInterviewRequest
): Promise<Interview> => {
  try {
    // Get current user for tracking who created the interview
    const user = await getCurrentUser();
    const now = new Date().toISOString();

    // Create the InterviewSet in the database
    const { data: newInterviewSet, errors: setErrors } =
      await client.models.InterviewSet.create({
        name: data.name,
        description: data.description,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

    if (setErrors) {
      console.error('Errors creating interview set:', setErrors);
      throw new Error('Failed to create interview set');
    }

    if (!newInterviewSet) {
      throw new Error('No data returned from create interview set');
    }

    // Create the first version of the InterviewSet in the database
    const { data: newInterviewSetVersion, errors: versionErrors } =
      await client.models.InterviewSetVersion.create({
        versionNumber: 1,
        interviewSetId: newInterviewSet.id,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

    if (versionErrors) {
      console.error('Errors creating interview set version:', versionErrors);
      throw new Error('Failed to create interview set version');
    }

    if (!newInterviewSetVersion) {
      throw new Error('No data returned from create interview set version');
    }

    // Create version structure for compatibility with existing UI
    const newVersion: InterviewVersion = {
      id: newInterviewSetVersion.id,
      version: newInterviewSetVersion.versionNumber,
      name: data.name,
      description: data.description || '',
      status: 'draft',
      estimatedDuration: 10,
      targetTemplate: data.targetTemplate || '',
      questions: [],
      totalQuestions: 0,
      yamlContent: '',
      createdBy: user.username || '<EMAIL>',
      createdAt: newInterviewSetVersion.createdAt || now,
      updatedAt: newInterviewSetVersion.updatedAt || now,
    };

    // Return Interview structure for compatibility with existing UI
    const newInterview: Interview = {
      id: newInterviewSet.id,
      currentVersion: 1,
      versions: [newVersion],
      createdAt: newInterviewSet.createdAt || now,
      updatedAt: newInterviewSet.updatedAt || now,
    };

    return newInterview;
  } catch (error) {
    console.error('Error creating interview:', error);
    throw error;
  }
};

export const updateInterview = async (
  data: UpdateInterviewRequest
): Promise<Interview> => {
  await delay(600);

  // In a real app, this would update the current version of the interview
  const currentVersion = mockInterview.versions.find(
    v => v.version === mockInterview.currentVersion
  );
  if (currentVersion) {
    if (data.name) currentVersion.name = data.name;
    if (data.description) currentVersion.description = data.description;
    if (data.targetTemplate)
      currentVersion.targetTemplate = data.targetTemplate;
    currentVersion.updatedAt = new Date().toISOString();
  }

  mockInterview.updatedAt = new Date().toISOString();
  return mockInterview;
};

export const getVersions = async (
  page = 1,
  limit = 10,
  status?: string
): Promise<VersionListResponse> => {
  await delay(500);

  let filteredVersions = mockVersions;
  if (status && status !== 'all') {
    filteredVersions = mockVersions.filter(
      version => version.status === status
    );
  }

  const start = (page - 1) * limit;
  const end = start + limit;
  const paginatedVersions = filteredVersions.slice(start, end);

  return {
    versions: paginatedVersions,
    total: filteredVersions.length,
    page,
    limit,
  };
};

export const getVersion = async (
  versionId: string
): Promise<InterviewVersion | null> => {
  await delay(300);
  const version = mockVersions[0];
  if (version) {
    // Add questions to the version
    return {
      ...version,
      questions: mockQuestions.filter(q => true), // In real app, filter by version ID
    };
  }
  return null;
};

export const getInterviewSetWithLastVersion = async (
  interviewSetId: string
) => {
  try {
    console.log('interviewSetId', interviewSetId);
    const { data: interviewSet, errors } = await client.models.InterviewSet.get(
      { id: interviewSetId },
      {
        selectionSet: [
          'id',
          'name',
          'description',
          'isActive',
          'createdAt',
          'updatedAt',
          'versions.id',
          'versions.versionNumber',
          'versions.isActive',
          'versions.createdAt',
          'versions.updatedAt',
          'versions.questions.id',
          'versions.questions.questionTitle',
          'versions.questions.questionDescription',
          'versions.questions.type',
          'versions.questions.options',
          'versions.questions.order',
          'versions.questions.isActive',
        ],
      }
    );

    if (errors) {
      console.error('Failed to fetch interview set:', errors);
      return;
    }

    if (!interviewSet) {
      console.log('Interview set not found');
      return;
    }

    const versions = interviewSet.versions ?? [];

    // Find the latest version (highest versionNumber)
    const latestVersion = versions.reduce<(typeof versions)[0] | null>(
      (latest, version) => {
        return !latest ||
          (version.versionNumber &&
            (!latest.versionNumber ||
              version.versionNumber > latest.versionNumber))
          ? version
          : latest;
      },
      null
    );

    if (!latestVersion) {
      console.log('No versions found for interview set');
      return;
    }

    // Prepare the result with interview set data and latest version with questions
    const result = {
      id: interviewSet.id,
      name: interviewSet.name,
      description: interviewSet.description,
      isActive: interviewSet.isActive,
      createdAt: interviewSet.createdAt,
      updatedAt: interviewSet.updatedAt,
      lastestVersionId: latestVersion.id,
      versionNumber: latestVersion.versionNumber,
      questions: latestVersion.questions ?? [],
    };

    console.log('===> result', result);

    return result;
  } catch (error) {
    console.error('Error fetching interview set with latest version:', error);
  }
};

export const createVersion = async (
  data: CreateVersionRequest
): Promise<InterviewVersion> => {
  await delay(800);

  const newVersion: InterviewVersion = {
    id: `v${mockVersions.length + 1}`,
    version: mockVersions.length + 1,
    name: data.name,
    description: data.description,
    status: 'draft',
    targetTemplate: data.targetTemplate,
    questions: [],
    totalQuestions: 0,
    estimatedDuration: 0,
    yamlContent:
      data.yamlContent ||
      `metadata:
  title: "${data.name}"
  description: "${data.description}"
questions:
  - id: sample_question
    question: "Sample question"
    type: text`,
    createdBy: '<EMAIL>',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    parentVersionId: data.parentVersionId,
  };

  mockVersions.push(newVersion);
  mockInterview.currentVersion = newVersion.version;
  mockInterview.updatedAt = new Date().toISOString();
  return newVersion;
};

export const updateVersion = async (
  versionId: string,
  data: UpdateVersionRequest
): Promise<InterviewVersion | null> => {
  await delay(600);

  const index = mockVersions.findIndex(v => v.id === versionId);
  if (index === -1) return null;

  const currentVersion = mockVersions[index];
  if (!currentVersion) return null;

  const updatedVersion: InterviewVersion = {
    ...currentVersion,
    ...data,
    updatedAt: new Date().toISOString(),
    ...(data.status === 'published' && !currentVersion.publishedAt
      ? { publishedAt: new Date().toISOString() }
      : {}),
  };

  mockVersions[index] = updatedVersion;
  return updatedVersion;
};

export const deleteVersion = async (versionId: string): Promise<boolean> => {
  await delay(400);

  const index = mockVersions.findIndex(v => v.id === versionId);
  if (index === -1) return false;

  mockVersions.splice(index, 1);
  return true;
};

export const getQuestions = async (
  interviewSetId: string
): Promise<QuestionListResponse> => {
  try {
    // Get the latest version for this interview set
    const latestVersionId =
      await getLatestVersionForInterviewSet(interviewSetId);

    if (!latestVersionId) {
      // No versions exist yet, return empty questions
      return {
        questions: [],
        total: 0,
      };
    }

    // Fetch questions from the database for the latest version
    const { data: questionsData, errors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: latestVersionId },
          isActive: { eq: true },
        },
      });

    if (errors) {
      console.error('Errors fetching interview questions:', errors);
      throw new Error('Failed to fetch interview questions');
    }

    // Transform the database response to match the expected InterviewQuestion type
    const questions: InterviewQuestion[] = (questionsData || [])
      .map(q => ({
        id: q.id,
        text: q.questionTitle,
        type: q.type as QuestionType,
        category: 'personal' as QuestionCategory, // Default category since it's not stored in DB
        required: true, // Default value since it's not stored in DB
        helpText: q.questionDescription || '',
        placeholder: '', // Default value since it's not stored in DB
        options: q.options ? JSON.parse(q.options as string) : [],
        validation: [], // Default value since it's not stored in DB
        conditionalLogic: [], // Default value since it's not stored in DB
        defaultNextQuestionId: null, // Default value since it's not stored in DB
        templateMapping: '', // Default value since it's not stored in DB
        order: q.order || 1,
        createdAt: q.createdAt || new Date().toISOString(),
        updatedAt: q.updatedAt || new Date().toISOString(),
      }))
      .sort((a, b) => a.order - b.order);

    return {
      questions,
      total: questions.length,
    };
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw error;
  }
};

// Get questions for a specific version
export const getQuestionsForVersion = async (
  versionId: string
): Promise<QuestionListResponse> => {
  try {
    // Fetch questions from the database for the specific version
    const { data: questionsData, errors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: versionId },
          isActive: { eq: true },
        },
      });

    if (errors) {
      console.error('Errors fetching interview questions for version:', errors);
      throw new Error('Failed to fetch interview questions for version');
    }

    // Transform the database response to match the expected InterviewQuestion type
    const questions: InterviewQuestion[] = (questionsData || [])
      .map(q => ({
        id: q.id,
        text: q.questionTitle,
        type: q.type as QuestionType,
        category: 'personal' as QuestionCategory, // Default category since it's not stored in DB
        required: true, // Default value since it's not stored in DB
        helpText: q.questionDescription || '',
        placeholder: '', // Default value since it's not stored in DB
        options: q.options ? JSON.parse(q.options as string) : [],
        validation: [], // Default value since it's not stored in DB
        conditionalLogic: [], // Default value since it's not stored in DB
        defaultNextQuestionId: null, // Default value since it's not stored in DB
        templateMapping: '', // Default value since it's not stored in DB
        order: q.order || 1,
        createdAt: q.createdAt || new Date().toISOString(),
        updatedAt: q.updatedAt || new Date().toISOString(),
      }))
      .sort((a, b) => a.order - b.order);

    return {
      questions,
      total: questions.length,
    };
  } catch (error) {
    console.error('Error fetching questions for version:', error);
    throw error;
  }
};

// Helper function to get the latest version for an interview set
export const getLatestVersionForInterviewSet = async (
  interviewSetId: string
): Promise<string | null> => {
  try {
    const { data: versions, errors } =
      await client.models.InterviewSetVersion.list({
        filter: {
          interviewSetId: { eq: interviewSetId },
          isActive: { eq: true },
        },
      });

    if (errors) {
      console.error('Error fetching versions:', errors);
      return null;
    }

    if (!versions || versions.length === 0) {
      return null;
    }

    // Find the version with the highest version number
    const latestVersion = versions.reduce((latest, current) => {
      return (current.versionNumber || 0) > (latest.versionNumber || 0)
        ? current
        : latest;
    });

    return latestVersion.id;
  } catch (error) {
    console.error('Error getting latest version:', error);
    return null;
  }
};

// Helper function to copy questions from one version to another
export const copyQuestionsToNewVersion = async (
  sourceVersionId: string,
  targetVersionId: string
): Promise<InterviewQuestion[]> => {
  try {
    // Get all questions from the source version
    const { data: sourceQuestions, errors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: sourceVersionId },
          isActive: { eq: true },
        },
      });

    if (errors) {
      console.error('Error fetching source questions:', errors);
      throw new Error('Failed to fetch source questions');
    }

    if (!sourceQuestions || sourceQuestions.length === 0) {
      return [];
    }

    const copiedQuestions: InterviewQuestion[] = [];
    const now = new Date().toISOString();

    // Copy each question to the new version
    for (const sourceQuestion of sourceQuestions) {
      const { data: newQuestionData, errors: createErrors } =
        await client.models.InterviewQuestion.create({
          questionTitle: sourceQuestion.questionTitle,
          questionDescription: sourceQuestion.questionDescription || '',
          type: sourceQuestion.type,
          options: sourceQuestion.options,
          order: sourceQuestion.order,
          isActive: true,
          versionId: targetVersionId,
        });

      if (createErrors) {
        console.error('Error copying question:', createErrors);
        continue; // Skip this question but continue with others
      }

      if (newQuestionData) {
        const copiedQuestion: InterviewQuestion = {
          id: newQuestionData.id,
          text: newQuestionData.questionTitle,
          type: sourceQuestion.type as QuestionType,
          category: 'personal' as QuestionCategory, // Default category
          required: true, // Default value
          helpText: newQuestionData.questionDescription || '',
          placeholder: '', // Default value
          options: newQuestionData.options
            ? JSON.parse(newQuestionData.options as string)
            : [],
          validation: [], // Default value
          conditionalLogic: [], // Default value
          defaultNextQuestionId: null, // Default value
          templateMapping: '', // Default value
          order: newQuestionData.order || 1,
          createdAt: now,
          updatedAt: now,
        };
        copiedQuestions.push(copiedQuestion);
      }
    }

    return copiedQuestions;
  } catch (error) {
    console.error('Error copying questions to new version:', error);
    throw error;
  }
};

// Helper function to create a new version with all questions from the previous version
export const createNewVersionWithQuestions = async (
  interviewSetId: string
): Promise<string> => {
  try {
    const now = new Date().toISOString();

    // Get the latest version to determine the next version number
    const { data: existingVersions, errors: versionErrors } =
      await client.models.InterviewSetVersion.list({
        filter: {
          interviewSetId: { eq: interviewSetId },
          isActive: { eq: true },
        },
      });

    if (versionErrors) {
      console.error('Error fetching existing versions:', versionErrors);
      throw new Error('Failed to fetch existing versions');
    }

    const nextVersionNumber =
      existingVersions && existingVersions.length > 0
        ? Math.max(...existingVersions.map(v => v.versionNumber || 0)) + 1
        : 1;

    // Create the new version
    const { data: newVersion, errors: createErrors } =
      await client.models.InterviewSetVersion.create({
        versionNumber: nextVersionNumber,
        interviewSetId: interviewSetId,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });

    if (createErrors) {
      console.error('Error creating new version:', createErrors);
      throw new Error('Failed to create new version');
    }

    if (!newVersion) {
      throw new Error('No data returned from create new version');
    }

    // If there are existing versions, copy questions from the latest one
    if (existingVersions && existingVersions.length > 0) {
      const latestVersion = existingVersions.reduce((latest, current) => {
        return (current.versionNumber || 0) > (latest.versionNumber || 0)
          ? current
          : latest;
      });

      await copyQuestionsToNewVersion(latestVersion.id, newVersion.id);
    }

    return newVersion.id;
  } catch (error) {
    console.error('Error creating new version with questions:', error);
    throw error;
  }
};

export const createQuestion = async (
  interviewSetId: string,
  data: CreateQuestionRequest
): Promise<InterviewQuestion> => {
  try {
    const now = new Date().toISOString();

    // Create a new version with all existing questions
    const newVersionId = await createNewVersionWithQuestions(interviewSetId);

    // Get the maximum order from existing questions in the new version to calculate the next order
    const { data: existingQuestions, errors: fetchErrors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: newVersionId },
          isActive: { eq: true },
        },
      });

    if (fetchErrors) {
      console.error(
        'Error fetching existing questions for order calculation:',
        fetchErrors
      );
      throw new Error('Failed to fetch existing questions');
    }

    // Calculate the next order (max order + 1, or 1 if no questions exist)
    const maxOrder =
      existingQuestions && existingQuestions.length > 0
        ? Math.max(...existingQuestions.map(q => q.order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    // Create the new question in the new version
    const { data: newQuestionData, errors } =
      await client.models.InterviewQuestion.create({
        questionTitle: data.text,
        questionDescription: data.helpText || '',
        type: data.type,
        options: data.options ? JSON.stringify(data.options) : null,
        order: nextOrder,
        isActive: true,
        versionId: newVersionId,
      });

    if (errors) {
      console.error('Errors creating interview question:', errors);
      throw new Error('Failed to create interview question');
    }

    if (!newQuestionData) {
      throw new Error('No data returned from create interview question');
    }

    // Transform the database response to match the expected InterviewQuestion type
    const newQuestion: InterviewQuestion = {
      id: newQuestionData.id,
      text: newQuestionData.questionTitle,
      type: data.type,
      category: data.category,
      required: data.required,
      helpText: newQuestionData.questionDescription || '',
      placeholder: data.placeholder,
      options: newQuestionData.options
        ? JSON.parse(newQuestionData.options as string)
        : [],
      validation: data.validation || [],
      conditionalLogic: data.conditionalLogic || [],
      defaultNextQuestionId: data.defaultNextQuestionId || null,
      templateMapping: data.templateMapping,
      order: newQuestionData.order || nextOrder,
      createdAt: now,
      updatedAt: now,
    };

    return newQuestion;
  } catch (error) {
    console.error('Error creating question:', error);
    throw error;
  }
};

export const updateQuestion = async (
  interviewSetId: string,
  data: UpdateQuestionRequest
): Promise<InterviewQuestion | null> => {
  try {
    const now = new Date().toISOString();

    // Get the current question to find its version
    const { data: currentQuestion, errors: fetchErrors } =
      await client.models.InterviewQuestion.get({ id: data.id });

    if (fetchErrors || !currentQuestion) {
      console.error('Error fetching current question:', fetchErrors);
      throw new Error('Question not found');
    }

    // Create a new version with all existing questions
    const newVersionId = await createNewVersionWithQuestions(interviewSetId);

    // Get all questions from the new version (they were copied from the previous version)
    const { data: newVersionQuestions, errors: newVersionErrors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: newVersionId },
        },
      });

    if (newVersionErrors || !newVersionQuestions) {
      console.error('Error fetching new version questions:', newVersionErrors);
      throw new Error('Failed to fetch questions from new version');
    }

    // Find the question to update in the new version (it should have the same order as the original)
    const questionToUpdate = newVersionQuestions.find(
      q => q.order === currentQuestion.order
    );

    if (!questionToUpdate) {
      console.error('Question to update not found in new version');
      throw new Error('Question to update not found in new version');
    }

    // Update the question in the new version
    const { data: updatedQuestionData, errors: updateErrors } =
      await client.models.InterviewQuestion.update({
        id: questionToUpdate.id,
        questionTitle: data.text || questionToUpdate.questionTitle,
        questionDescription:
          data.helpText !== undefined
            ? data.helpText
            : questionToUpdate.questionDescription || '',
        type: data.type || questionToUpdate.type,
        options:
          data.options !== undefined
            ? data.options.length > 0
              ? JSON.stringify(data.options)
              : null
            : questionToUpdate.options,
        order: data.order !== undefined ? data.order : questionToUpdate.order,
        isActive: true,
      });

    if (updateErrors || !updatedQuestionData) {
      console.error('Error updating question:', updateErrors);
      throw new Error('Failed to update question');
    }

    // Convert the database question to InterviewQuestion format
    let parsedOptions: QuestionOption[] = [];
    if (updatedQuestionData.options) {
      try {
        // Handle different types of options data
        if (typeof updatedQuestionData.options === 'string') {
          parsedOptions = JSON.parse(updatedQuestionData.options);
        } else if (Array.isArray(updatedQuestionData.options)) {
          parsedOptions = updatedQuestionData.options as QuestionOption[];
        } else if (typeof updatedQuestionData.options === 'object') {
          parsedOptions = updatedQuestionData.options as QuestionOption[];
        }
      } catch (error) {
        console.error('Error parsing question options:', error);
        parsedOptions = [];
      }
    }

    const updatedQuestion: InterviewQuestion = {
      id: updatedQuestionData.id,
      text: updatedQuestionData.questionTitle || '',
      type: updatedQuestionData.type as QuestionType,
      category: (data.category !== undefined
        ? data.category
        : 'personal') as QuestionCategory,
      required: data.required !== undefined ? data.required : false,
      helpText: updatedQuestionData.questionDescription || '',
      placeholder: data.placeholder !== undefined ? data.placeholder : '',
      options: parsedOptions,
      validation: data.validation || [],
      conditionalLogic: data.conditionalLogic || [],
      defaultNextQuestionId:
        data.defaultNextQuestionId !== undefined
          ? data.defaultNextQuestionId
          : '',
      templateMapping:
        data.templateMapping !== undefined ? data.templateMapping : '',
      order: updatedQuestionData.order || 1,
      createdAt: updatedQuestionData.createdAt || now,
      updatedAt: now,
    };

    return updatedQuestion;
  } catch (error) {
    console.error('Error in updateQuestion:', error);
    throw error;
  }
};

export const deleteQuestion = async (
  interviewSetId: string,
  questionId: string
): Promise<boolean> => {
  try {
    // Get the latest version for this interview set
    const latestVersionId =
      await getLatestVersionForInterviewSet(interviewSetId);

    if (!latestVersionId) {
      console.error('No versions found for interview set');
      return false;
    }

    // Fetch all existing questions from the current version
    const { data: existingQuestions, errors: fetchErrors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: latestVersionId },
        },
      });

    if (fetchErrors) {
      console.error('Error fetching existing questions:', fetchErrors);
      return false;
    }

    if (!existingQuestions || existingQuestions.length === 0) {
      console.error('No questions found in current version');
      return false;
    }

    // Check if the question to delete exists
    const questionToDelete = existingQuestions.find(q => q.id === questionId);
    if (!questionToDelete) {
      console.error('Question to delete not found');
      return false;
    }

    // Create a new version with all existing questions
    const newVersionId = await createNewVersionWithQuestions(interviewSetId);

    // Get all questions from the new version (they were copied from the previous version)
    const { data: newVersionQuestions, errors: newVersionErrors } =
      await client.models.InterviewQuestion.list({
        filter: {
          versionId: { eq: newVersionId },
        },
      });

    if (newVersionErrors) {
      console.error(
        'Error fetching questions from new version:',
        newVersionErrors
      );
      return false;
    }

    if (!newVersionQuestions) {
      console.error('No questions found in new version');
      return false;
    }

    // Find and deactivate the question to delete in the new version
    // We need to find the corresponding question in the new version (it will have a different ID)
    const questionToDeleteInNewVersion = newVersionQuestions.find(
      q =>
        q.questionTitle === questionToDelete.questionTitle &&
        q.order === questionToDelete.order
    );

    if (!questionToDeleteInNewVersion) {
      console.error('Could not find corresponding question in new version');
      return false;
    }

    // Deactivate the question (soft delete)
    const { errors: deleteErrors } =
      await client.models.InterviewQuestion.delete({
        id: questionToDeleteInNewVersion.id,
      });

    if (deleteErrors) {
      console.error('Error deleting question:', deleteErrors);
      return false;
    }

    // Get remaining active questions and reorder them
    const remainingQuestions = newVersionQuestions
      .filter(q => q.id !== questionToDeleteInNewVersion.id)
      .sort((a, b) => (a.order || 0) - (b.order || 0));

    // Update the order of remaining questions to be sequential (1, 2, 3, ...)
    for (let i = 0; i < remainingQuestions.length; i++) {
      const question = remainingQuestions[i];
      if (question && question.order !== i + 1) {
        const { errors: updateErrors } =
          await client.models.InterviewQuestion.update({
            id: question.id,
            order: i + 1,
          });

        if (updateErrors) {
          console.error(
            `Error updating order for question ${question.id}:`,
            updateErrors
          );
          // Continue with other questions even if one fails
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Error in deleteQuestion:', error);
    return false;
  }
};

export const getTemplateVariables = async (): Promise<TemplateVariable[]> => {
  await delay(200);
  return mockTemplateVariables;
};

export const duplicateVersion = async (
  versionId: string
): Promise<InterviewVersion | null> => {
  await delay(700);

  const original = mockVersions.find(v => v.id === versionId);
  if (!original) return null;

  const duplicate: InterviewVersion = {
    ...original,
    id: `v${mockVersions.length + 1}`,
    version: mockVersions.length + 1,
    name: `${original.name} (Copy)`,
    status: 'draft',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    publishedAt: undefined,
    parentVersionId: versionId,
  };

  mockVersions.push(duplicate);
  mockInterview.currentVersion = duplicate.version;
  mockInterview.updatedAt = new Date().toISOString();
  return duplicate;
};

// Validation helpers
export const validateQuestionData = (
  data: CreateQuestionRequest | UpdateQuestionRequest
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!data.text?.trim()) {
    errors.text = 'Question text is required';
  }

  if (!data.type) {
    errors.type = 'Question type is required';
  }

  if (!data.category) {
    errors.category = 'Question category is required';
  }

  if (
    (data.type === 'radio' ||
      data.type === 'select' ||
      data.type === 'checkbox') &&
    (!data.options || data.options.length === 0)
  ) {
    errors.options = 'Options are required for this question type';
  }

  return errors;
};

export const validateVersionData = (
  data: CreateVersionRequest | UpdateVersionRequest
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!data.name?.trim()) {
    errors.name = 'Version name is required';
  }

  if (!data.description?.trim()) {
    errors.description = 'Version description is required';
  }

  return errors;
};

export const validateInterviewData = (
  data: CreateInterviewRequest | UpdateInterviewRequest
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!data.name?.trim()) {
    errors.name = 'Interview name is required';
  }

  if (!data.description?.trim()) {
    errors.description = 'Interview description is required';
  }

  return errors;
};

export const reorderQuestions = async (
  _interviewId: string,
  questionIds: string[]
): Promise<boolean> => {
  await delay(400);

  // Update the order of questions based on the new array
  questionIds.forEach((questionId, index) => {
    const questionIndex = mockQuestions.findIndex(q => q.id === questionId);
    if (questionIndex !== -1) {
      const question = mockQuestions[questionIndex];
      if (question) {
        question.order = index + 1;
      }
    }
  });

  return true;
};
