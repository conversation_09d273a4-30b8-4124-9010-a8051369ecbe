'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '@/types/account';
import {
  fetchUsers,
  fetchUserByCognitoId,
  fetchUsersAssignedToWelonTrust,
} from '@/lib/data/users';
import { useAuth } from '@/app/context/AuthContext';

interface UserContextType {
  selectedUser: User | null;
  setSelectedUser: (user: User | null) => void;
  availableUsers: User[];
  isLoading: boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user: authUser, loading: authLoading } = useAuth();

  useEffect(() => {
    // Wait for auth to load before fetching users
    if (authLoading) return;

    // Fetch users assigned to current welontrust user
    const loadUsers = async () => {
      try {
        setIsLoading(true);

        // If no authenticated user, set empty array
        if (!authUser?.userId) {
          setAvailableUsers([]);
          return;
        }

        // Get current user's database record using their Cognito ID
        const currentUser = await fetchUserByCognitoId(authUser.userId);

        if (!currentUser) {
          console.error('Current user not found in database');
          setAvailableUsers([]);
          return;
        }
        // Check if current user is a Welon Trust user
        if (currentUser.role === 'WelonTrust') {
          // Fetch only users assigned to this Welon Trust user
          const assignedUsers = await fetchUsersAssignedToWelonTrust();

          setAvailableUsers(assignedUsers);
        } else {
          // If not a Welon Trust user, show empty list or fallback behavior
          console.warn(
            'Current user is not a Welon Trust user, showing empty user list'
          );
          setAvailableUsers([]);
        }
      } catch (error) {
        console.error('Failed to load users:', error);
        // Fallback to empty array if fetch fails
        setAvailableUsers([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [authUser, authLoading]);

  // Persist selected user in localStorage
  useEffect(() => {
    if (selectedUser) {
      localStorage.setItem(
        'welon-trust-selected-user',
        JSON.stringify(selectedUser)
      );
    } else {
      localStorage.removeItem('welon-trust-selected-user');
    }
  }, [selectedUser]);

  // Load selected user from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('welon-trust-selected-user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        setSelectedUser(user);
      } catch (error) {
        console.error('Failed to parse saved user:', error);
        localStorage.removeItem('welon-trust-selected-user');
      }
    }
  }, []);

  const value: UserContextType = {
    selectedUser,
    setSelectedUser,
    availableUsers,
    isLoading,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUserContext() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
}

// Optional version that doesn't throw error if not in provider
export function useUserContextOptional() {
  const context = useContext(UserContext);
  return (
    context || {
      selectedUser: null,
      setSelectedUser: () => {},
      availableUsers: [],
      isLoading: false,
    }
  );
}

// Hook for getting user-scoped data
export function useUserScopedData<T>(
  getData: (userId: string) => T | Promise<T>,
  defaultValue: T
) {
  const { selectedUser } = useUserContext();
  const [data, setData] = useState<T>(defaultValue);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!selectedUser) {
      setData(defaultValue);
      return;
    }

    const loadData = async () => {
      setIsLoading(true);
      try {
        const result = await getData(selectedUser.id);
        setData(result);
      } catch (error) {
        console.error('Failed to load user-scoped data:', error);
        setData(defaultValue);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [selectedUser, getData, defaultValue]);

  return { data, isLoading, userId: selectedUser?.id || null };
}
