'use client';

import Image from 'next/image';
import { useTheme } from 'next-themes';

interface LogoProps {
  variant?: 'default' | 'small' | 'large';
  className?: string;
}

export function Logo({ variant = 'default', className = '' }: LogoProps) {
  const { theme, resolvedTheme } = useTheme();
  const currentTheme = theme === 'system' ? resolvedTheme : theme;
  // Default to light theme if theme is undefined (during SSR)
  const logoSrc =
    currentTheme === 'dark' ? '/logo-dark.svg' : '/logo-light.svg';

  const sizes = {
    small: { width: 100, height: 25 },
    default: { width: 150, height: 40 },
    large: { width: 200, height: 50 },
  };

  const { width, height } = sizes[variant];

  return (
    <Image
      src={logoSrc}
      alt='Childfree Legacy Logo'
      width={width}
      height={height}
      className={`h-auto ${className}`}
    />
  );
}
