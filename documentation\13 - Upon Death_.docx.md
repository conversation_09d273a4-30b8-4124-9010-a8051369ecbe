# 13 \- Upon Death

# **Document 13 \- Functional Specification: Upon Death**

## **Overview**

The "Upon Death" process is a critical feature of the Childfree Legacy Web Application, ensuring that upon a member's passing, their estate planning documents are securely and permanently transferred to designated individuals, such as emergency contacts or Welon Trust members. Built on **AWS Amplify**’s serverless architecture, this process integrates seamlessly with the Emergency Features and Dead Man’s Switch, leveraging shared mechanisms while adapting to the unique requirements of death verification and document access. Tailored for members aged 65+ who are childfree, the system prioritizes usability, security (HIPAA and SOC2 compliance), and legal compliance, providing peace of mind and maintaining trust. This specification outlines the steps for verifying a member's death, granting permanent access to estate documents, notifying relevant parties, and ensuring compliance through AWS Amplify’s scalable, secure services.

---

## **1\. Verification of Death**

The "Upon Death" process begins with a manual verification step to confirm a member's passing, ensuring that access to sensitive estate documents is granted only after proper evidence is provided.

### **Key Requirements**

#### **Submission of Death Certificate**

- A designated individual (e.g., Administrator or <PERSON>lon-Basic) submits a valid death certificate through the platform to initiate the process.

#### **Administrator Verification**

- Administrators manually review and authenticate the death certificate to confirm its validity before proceeding.

#### **Compliance**

- Adheres to HIPAA (for any medical-related data) and SOC2 standards.
- All actions are logged in an audit trail using **Amazon CloudWatch**.

### **User Flow**

1. A user with the member’s login navigates to /emergency/submit-evidence and is provided an email address for document submission.
   - **OR**  
      A Welon user role logs in, navigates to the appropriate section, and uploads the death certificate as a file (e.g., PDF, JPG, DOCX) up to 10MB (100MB for video formats), per the File Uploads definition.
2. The system securely stores the uploaded file in **Amazon S3** and logs the submission in the evidence_submissions table in **Amazon DynamoDB**.
3. Sends an in-app and email notification to Welon-Advanced via **Amazon Simple Notification Service (SNS)** or **Amazon Simple Email Service (SES)**.
4. Welon-Administrators access /admin/emergency, review the submitted death certificate for authenticity (e.g., official seals, signatures), and approve or reject it using the /admin/approve-evidence endpoint via **AWS AppSync**.
5. Upon approval, the system updates the submission status to "Deceased" in **DynamoDB** and triggers the access grant and notification processes via **AWS Lambda**.

### **Edge Cases**

- **Invalid or Forged Certificates**: If invalid, administrators reject the submission via **AWS AppSync**, notifying the submitter via **Amazon SES** to provide corrected documentation.
- **Multiple Submissions**: Processes the first valid submission and flags duplicates in **DynamoDB** for administrator review.
- **Wrong Contact**: If a user contacts an administrator instead of a Welon user role, a manual email is sent to Welon via **Amazon SES** to initiate the process.

---

## **2\. Access to Estate Documents**

Upon verification of a member's death, the system grants permanent access to the member's estate planning documents (e.g., wills, trusts, powers of attorney) and living documents to designated individuals.

### **Key Requirements**

#### **Permanent Access**

- Access is granted indefinitely, distinguishing it from temporary access for incapacitation scenarios.

#### **Authorized Individuals**

- Access is limited to Welon user roles with read-only and download permissions, managed via **AWS Cognito**.

### **User Flow**

1. After death certificate verification, the Welon role changes the system status to “Deceased” via **AWS AppSync**.
2. The system adjusts the following parameters under “Deceased”:
   - Suspends member login via **AWS Cognito**.
   - “Freezes” all estate and living documents from modification, ensuring immutability using **Amazon S3** versioning.
   - Archives the member after 1 year into a permanent database using **Amazon S3 Glacier** for long-term storage.
3. Authorized individuals access documents via a **GraphQL API** powered by **AWS AppSync**, ensuring real-time synchronization and secure retrieval.

---

## **3\. Notifications**

The system notifies relevant parties when a member’s death is verified and access is granted, ensuring transparency and coordination.

### **Key Requirements**

#### **Notification to Members**

- A final notification is sent to the member’s email address on file via **Amazon SES**. This helps prevent fraud and informs the data administrator handling the member’s electronic information.

#### **Notification to Administrators**

- Administrators receive confirmation of access grants and any issues (e.g., failed verifications) via **Amazon SNS**.

#### **Audit Logging**

- All notifications are logged in **Amazon CloudWatch** for compliance, even though the deceased member cannot be notified.

### **User Flow**

1. After death certificate verification, **AWS Lambda** triggers notifications to the member and administrators via **Amazon SES** and **Amazon SNS**.
2. In-app notifications are delivered via **AWS AppSync** subscriptions for real-time updates.

### **Edge Cases**

- **Undeliverable Notifications**: If delivery fails (e.g., invalid email), the system flags the contact’s account in **DynamoDB** for manual follow-up.

---

## **4\. User Flow for Death**

The complete process from death certificate submission to document access is streamlined using **AWS Step Functions** for orchestration, ensuring efficiency and security.

### **Step-by-Step Flow**

1. **Submission**: A designated contact submits a death certificate via /emergency/submit-evidence.
2. **Verification**: Welon-Advanced verifies the certificate using /admin/approve-evidence via **AWS AppSync**.
3. **Notification**: Sends notifications to the member and administrator via **Amazon SES** and **Amazon SNS**.
4. **Access Grant**: Permanent access is granted, orchestrated by **AWS Step Functions**.

---

## **5\. Integration with Emergency Features and Dead Man’s Switch**

The "Upon Death" process leverages the "Emergency Features and Dead Man’s Switch" specification, adapting its mechanisms to handle death-specific workflows while maintaining compatibility.

### **Key Integration Points**

#### **Access Mechanisms**

- Uses the same secure access infrastructure as incapacitation (e.g., /emergency/submit-evidence, /emergency/access), but grants permanent access instead of temporary, managed via **AWS AppSync**.

#### **Administrator Oversight**

- Administrators use the same dashboard (/admin/emergency) to review evidence and approve access, ensuring consistency across emergency scenarios.

### **Differences from Incapacitation**

- **Trigger**: Death is manually triggered by a death certificate, not automated via missed check-ins.
- **Access Duration**: Permanent, with no ability to edit estate or living documents.
- **Verification**: Requires a death certificate rather than medical certification.

---

## **6\. Compliance and Security**

The "Upon Death" process adheres to strict standards to protect sensitive data and ensure legal validity.

### **Key Requirements**

#### **HIPAA Compliance**

- Encrypts all data, including any medical information in the death certificate, using **AWS Key Management Service (KMS)** and TLS/SSL.

#### **SOC2 Compliance**

- Maintains detailed audit trails in **Amazon CloudWatch** for all actions (submissions, verifications, access grants).

#### **Encryption**

- Documents, access logs, and notifications are encrypted per **AWS KMS** standards.

#### **State Laws**

- Ensures access complies with local estate and privacy regulations, verified during certificate review.

### **Implementation Notes**

- Uses **AWS Identity and Access Management (IAM)** for fine-grained access control.
- Logs all interactions with timestamps, user IDs, and action details in **CloudWatch** for regulatory audits.

---

## **7\. Technical Specifications**

The technical implementation leverages AWS Amplify’s serverless services, tailored for the death scenario.

### **API Endpoints (GraphQL via AWS AppSync)**

- **submitEvidence**:
  - **Input**: { userId, evidenceType: "death_cert", document }
  - **Output**: { success, submissionId }
  - **Purpose**: Submits the death certificate for verification.
- **approveEvidence**:
  - **Input**: { submissionId, approve: true }
  - **Output**: { success }
  - **Purpose**: Approves the death certificate, triggering access.
- **getEmergencyAccess**:
  - **Input**: { token }
  - **Output**: { documents: \[{ id, type, url }\] }
  - **Purpose**: Provides permanent access to estate documents with a valid token.

### **Database Tables (Amazon DynamoDB)**

- **evidence_submissions**:
  - **Columns**: id, user_id, submitter_id, evidence_type (death_cert), document_url (S3), status, submission_date.
- **emergency_access**:
  - **Columns**: id, user_id, contact_id, trigger_type (death), access_expiry (null), status.

### **Storage**

- Death certificates and estate documents are stored in **Amazon S3**, encrypted with **AWS KMS**.

### **Business Logic**

- **AWS Lambda** handles verification workflows, notification triggers, and access updates.

### **Logging**

- Tracks all submissions, approvals, access grants, and document views in **CloudWatch** with detailed entries (e.g., timestamp, user_id, action: "access_granted").

---

## **8\. Testing and Validation**

Comprehensive testing ensures the "Upon Death" process is reliable, secure, and user-friendly, leveraging **AWS Amplify** testing tools.

### **Test Types**

- **Unit Tests**: Validate evidence submission, approval, and permanent access logic.
- **Integration Tests**: Simulate the full process from certificate submission to document access using **AWS Step Functions**.
- **Security Testing**: Ensure only authorized contacts access documents, validated via **AWS Cognito** and **IAM**.
- **User Acceptance Testing (UAT)**: Verify usability for administrators and contacts, adhering to **WCAG 2.1 Level AA** accessibility standards.

### **Test Cases**

| Scenario                                       | Expected Outcome                                     |
| ---------------------------------------------- | ---------------------------------------------------- |
| Valid death certificate submitted and approved | Permanent access granted, notifications sent         |
| Invalid death certificate submitted            | Submission rejected, submitter notified              |
| Multiple certificates submitted                | First valid submission processed, duplicates flagged |

---

## **Scalability Enhancements with AWS Amplify**

- **Serverless Architecture**: Uses **AWS Lambda** and **AWS AppSync** to handle increased load without provisioning servers.
- **Managed Services**: Relies on **Amazon S3**, **DynamoDB**, and **SES/SNS** for auto-scaling storage, database, and notification capabilities.
- **Real-Time Updates**: **AWS AppSync** ensures real-time synchronization of access and notifications.

---

## **Summary**

The "Upon Death" process, powered by AWS Amplify, ensures that a member’s estate planning documents are securely and permanently accessible to designated individuals upon their passing. By integrating with the Emergency Features and Dead Man’s Switch—adapting its contact setup and access mechanisms—this system provides a robust, compliant solution for handling death scenarios. With manual verification via death certificates, permanent access grants, and comprehensive notifications, it balances usability and security while adhering to HIPAA, SOC2, and state-specific legal standards. This specification serves as a clear guide for developers and stakeholders, reinforcing the Childfree Legacy platform’s commitment to trust and reliability.
