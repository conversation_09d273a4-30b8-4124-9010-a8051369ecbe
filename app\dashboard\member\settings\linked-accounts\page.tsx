'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Users,
  Mail,
  UserPlus,
  UserMinus,
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  FileText,
  Heart,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

// Types for linked accounts
interface LinkedAccount {
  id: string;
  linkedUserName: string;
  linkedUserEmail: string;
  status: 'pending' | 'active' | 'declined';
  linkType: 'spouse' | 'partner' | 'family' | 'friend';
  createdAt: string;
  permissions: LinkPermission[];
}

// Permission types
type LinkPermission =
  | 'view_documents'
  | 'edit_shared_documents'
  | 'create_shared_documents'
  | 'approve_shared_documents'
  | 'view_emergency_contacts'
  | 'edit_emergency_contacts'
  | 'emergency_access'
  | 'view_dead_man_switch'
  | 'edit_dead_man_switch'
  | 'execute_emergency_protocol'
  | 'view_billing'
  | 'edit_billing';

// Permission definitions with descriptions
const PERMISSION_DEFINITIONS = {
  view_documents: {
    label: 'View Documents',
    description: 'Can view your individual estate planning documents',
    icon: <FileText className='w-4 h-4' />,
    category: 'documents',
  },
  edit_shared_documents: {
    label: 'Edit Shared Documents',
    description: 'Can edit shared documents like joint trusts',
    icon: <FileText className='w-4 h-4' />,
    category: 'documents',
  },
  create_shared_documents: {
    label: 'Create Shared Documents',
    description: 'Can initiate creation of new shared documents',
    icon: <FileText className='w-4 h-4' />,
    category: 'documents',
  },
  approve_shared_documents: {
    label: 'Approve Shared Documents',
    description: 'Can approve and finalize shared documents',
    icon: <CheckCircle className='w-4 h-4' />,
    category: 'documents',
  },
  view_emergency_contacts: {
    label: 'View Emergency Contacts',
    description: 'Can see your emergency contact information',
    icon: <Shield className='w-4 h-4' />,
    category: 'emergency',
  },
  edit_emergency_contacts: {
    label: 'Edit Emergency Contacts',
    description: 'Can modify your emergency contact list',
    icon: <Shield className='w-4 h-4' />,
    category: 'emergency',
  },
  emergency_access: {
    label: 'Emergency Access',
    description: 'Can access your documents in emergency situations',
    icon: <AlertTriangle className='w-4 h-4' />,
    category: 'emergency',
  },
  view_dead_man_switch: {
    label: "View Dead Man's Switch",
    description: 'Can see your DMS configuration and status',
    icon: <Clock className='w-4 h-4' />,
    category: 'emergency',
  },
  edit_dead_man_switch: {
    label: "Edit Dead Man's Switch",
    description: 'Can modify your DMS settings',
    icon: <Clock className='w-4 h-4' />,
    category: 'emergency',
  },
  execute_emergency_protocol: {
    label: 'Execute Emergency Protocol',
    description: 'Can trigger emergency protocols on your behalf',
    icon: <AlertTriangle className='w-4 h-4' />,
    category: 'emergency',
  },
  view_billing: {
    label: 'View Billing',
    description: 'Can see billing and subscription information',
    icon: <Heart className='w-4 h-4' />,
    category: 'account',
  },
  edit_billing: {
    label: 'Edit Billing',
    description: 'Can modify billing and payment methods',
    icon: <Heart className='w-4 h-4' />,
    category: 'account',
  },
} as const;

// Permission templates for common scenarios
const PERMISSION_TEMPLATES = {
  spouse: {
    label: 'Spouse/Partner (Full Access)',
    description: 'Complete access to all documents and emergency features',
    permissions: [
      'view_documents',
      'edit_shared_documents',
      'create_shared_documents',
      'approve_shared_documents',
      'view_emergency_contacts',
      'edit_emergency_contacts',
      'emergency_access',
      'view_dead_man_switch',
      'execute_emergency_protocol',
    ] as LinkPermission[],
  },
  family: {
    label: 'Family Member (Emergency Only)',
    description: 'Emergency access and contact viewing only',
    permissions: [
      'view_emergency_contacts',
      'emergency_access',
      'view_dead_man_switch',
    ] as LinkPermission[],
  },
  trusted_friend: {
    label: 'Trusted Friend (Limited)',
    description: 'Can view shared documents and emergency contacts',
    permissions: [
      'view_documents',
      'view_emergency_contacts',
      'emergency_access',
    ] as LinkPermission[],
  },
  financial_advisor: {
    label: 'Financial Advisor',
    description: 'Access to shared documents and billing information',
    permissions: [
      'view_documents',
      'edit_shared_documents',
      'view_billing',
    ] as LinkPermission[],
  },
} as const;

interface PendingInvitation {
  id: string;
  inviteeEmail: string;
  status: 'sent' | 'expired';
  sentAt: string;
  expiresAt: string;
}

export default function LinkedAccountsPage() {
  const router = useRouter();
  const [linkedAccounts, setLinkedAccounts] = useState<LinkedAccount[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<
    PendingInvitation[]
  >([]);
  const [inviteEmail, setInviteEmail] = useState('');
  const [linkType, setLinkType] = useState<LinkedAccount['linkType']>('spouse');
  const [selectedPermissions, setSelectedPermissions] = useState<
    LinkPermission[]
  >([]);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isUnlinkDialogOpen, setIsUnlinkDialogOpen] = useState(false);
  const [selectedAccountToUnlink, setSelectedAccountToUnlink] =
    useState<LinkedAccount | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    // Simulate API call
    setLinkedAccounts([
      {
        id: '1',
        linkedUserName: 'Sarah Johnson',
        linkedUserEmail: '<EMAIL>',
        status: 'active',
        linkType: 'spouse',
        createdAt: '2024-01-15',
        permissions: [
          'view_documents',
          'edit_shared_documents',
          'create_shared_documents',
          'approve_shared_documents',
          'view_emergency_contacts',
          'emergency_access',
        ],
      },
    ]);

    setPendingInvitations([
      {
        id: '1',
        inviteeEmail: '<EMAIL>',
        status: 'sent',
        sentAt: '2024-01-20',
        expiresAt: '2024-01-27',
      },
    ]);
  }, []);

  // Permission management helpers
  const handlePermissionChange = (
    permission: LinkPermission,
    checked: boolean
  ) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permission]);
    } else {
      setSelectedPermissions(prev => prev.filter(p => p !== permission));
    }
  };

  const handleSelectAllPermissions = (category: string) => {
    const categoryPermissions = Object.entries(PERMISSION_DEFINITIONS)
      .filter(([_, def]) => def.category === category)
      .map(([key, _]) => key as LinkPermission);

    setSelectedPermissions(prev => {
      const newPermissions = [...prev];
      categoryPermissions.forEach(permission => {
        if (!newPermissions.includes(permission)) {
          newPermissions.push(permission);
        }
      });
      return newPermissions;
    });
  };

  const handleDeselectAllPermissions = (category: string) => {
    const categoryPermissions = Object.entries(PERMISSION_DEFINITIONS)
      .filter(([_, def]) => def.category === category)
      .map(([key, _]) => key as LinkPermission);

    setSelectedPermissions(prev =>
      prev.filter(permission => !categoryPermissions.includes(permission))
    );
  };

  const getPermissionsByCategory = () => {
    const categories: { [key: string]: LinkPermission[] } = {};

    Object.entries(PERMISSION_DEFINITIONS).forEach(([key, def]) => {
      if (!categories[def.category]) {
        categories[def.category] = [];
      }
      categories[def.category].push(key as LinkPermission);
    });

    return categories;
  };

  const resetInviteForm = () => {
    setInviteEmail('');
    setLinkType('spouse');
    setSelectedPermissions([]);
    setError('');
  };

  const applyPermissionTemplate = (
    templateKey: keyof typeof PERMISSION_TEMPLATES
  ) => {
    const template = PERMISSION_TEMPLATES[templateKey];
    setSelectedPermissions(template.permissions);
  };

  const handleSendInvite = async () => {
    if (!inviteEmail.trim()) {
      setError('Please enter an email address');
      return;
    }

    if (selectedPermissions.length === 0) {
      setError('Please select at least one permission');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Add to pending invitations
      const newInvitation: PendingInvitation = {
        id: Date.now().toString(),
        inviteeEmail: inviteEmail,
        status: 'sent',
        sentAt: new Date().toISOString().split('T')[0],
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0],
      };

      setPendingInvitations(prev => [...prev, newInvitation]);
      setSuccess(
        `Invitation sent to ${inviteEmail} with ${selectedPermissions.length} permissions`
      );
      resetInviteForm();
      setIsInviteDialogOpen(false);
    } catch (err) {
      setError('Failed to send invitation. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnlinkAccount = async () => {
    if (!selectedAccountToUnlink) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setLinkedAccounts(prev =>
        prev.filter(account => account.id !== selectedAccountToUnlink.id)
      );
      setSuccess(
        `Successfully unlinked ${selectedAccountToUnlink.linkedUserName}`
      );
      setIsUnlinkDialogOpen(false);
      setSelectedAccountToUnlink(null);
    } catch (err) {
      setError('Failed to unlink account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant='default' className='bg-green-100 text-green-800'>
            <CheckCircle className='w-3 h-3 mr-1' />
            Active
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant='secondary'>
            <Clock className='w-3 h-3 mr-1' />
            Pending
          </Badge>
        );
      case 'declined':
        return <Badge variant='destructive'>Declined</Badge>;
      default:
        return <Badge variant='secondary'>{status}</Badge>;
    }
  };

  return (
    <>
      <div className='space-y-6'>
        <div>
          <h1 className='text-3xl font-geologica font-semibold mb-2'>
            Linked Accounts
          </h1>
          <p className='text-muted-foreground'>
            Manage shared access to your estate planning documents with trusted
            family members or partners.
          </p>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className='h-4 w-4' />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Invite New Account */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <UserPlus className='h-5 w-5' />
              Link New Account
            </CardTitle>
            <CardDescription>
              Invite a trusted person to collaborate on shared estate planning
              documents.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog
              open={isInviteDialogOpen}
              onOpenChange={setIsInviteDialogOpen}
            >
              <DialogTrigger asChild>
                <Button>
                  <Mail className='w-4 h-4 mr-2' />
                  Send Invitation
                </Button>
              </DialogTrigger>
              <DialogContent className='max-w-2xl max-h-[80vh] overflow-y-auto'>
                <DialogHeader>
                  <DialogTitle>Invite Account Link</DialogTitle>
                  <DialogDescription>
                    Enter the email address and select permissions for the
                    person you want to link accounts with.
                  </DialogDescription>
                </DialogHeader>
                <div className='space-y-6'>
                  {/* Basic Information */}
                  <div className='space-y-4'>
                    <div>
                      <Label htmlFor='invite-email'>Email Address</Label>
                      <Input
                        id='invite-email'
                        type='email'
                        placeholder='<EMAIL>'
                        value={inviteEmail}
                        onChange={e => setInviteEmail(e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor='link-type'>Relationship Type</Label>
                      <Select
                        value={linkType}
                        onValueChange={(value: LinkedAccount['linkType']) =>
                          setLinkType(value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select relationship' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='spouse'>Spouse</SelectItem>
                          <SelectItem value='partner'>Partner</SelectItem>
                          <SelectItem value='family'>Family Member</SelectItem>
                          <SelectItem value='friend'>Friend</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className='border-t border-gray-200 my-4'></div>

                  {/* Permissions Selection */}
                  <div>
                    <div className='mb-4'>
                      <h3 className='text-lg font-medium'>Permissions</h3>
                      <p className='text-sm text-muted-foreground'>
                        Select what this person can access and do with your
                        account.
                      </p>
                    </div>

                    {/* Permission Templates */}
                    <div className='mb-6'>
                      <h4 className='font-medium text-sm mb-3'>
                        Quick Templates
                      </h4>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                        {Object.entries(PERMISSION_TEMPLATES).map(
                          ([key, template]) => (
                            <div
                              key={key}
                              className='p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors'
                              onClick={() =>
                                applyPermissionTemplate(
                                  key as keyof typeof PERMISSION_TEMPLATES
                                )
                              }
                            >
                              <div className='flex items-start justify-between'>
                                <div className='flex-1'>
                                  <p className='font-medium text-sm'>
                                    {template.label}
                                  </p>
                                  <p className='text-xs text-muted-foreground mt-1'>
                                    {template.description}
                                  </p>
                                  <p className='text-xs text-blue-600 mt-1'>
                                    {template.permissions.length} permissions
                                  </p>
                                </div>
                                <Button
                                  type='button'
                                  variant='outline'
                                  size='sm'
                                  onClick={e => {
                                    e.stopPropagation();
                                    applyPermissionTemplate(
                                      key as keyof typeof PERMISSION_TEMPLATES
                                    );
                                  }}
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    <div className='border-t border-gray-200 my-4'></div>

                    <div className='mb-4'>
                      <h4 className='font-medium text-sm'>
                        Custom Permissions
                      </h4>
                      <p className='text-xs text-muted-foreground'>
                        Or select individual permissions below
                      </p>
                    </div>

                    {Object.entries(getPermissionsByCategory()).map(
                      ([category, permissions]) => {
                        const categoryLabel =
                          category.charAt(0).toUpperCase() + category.slice(1);
                        const allSelected = permissions.every(p =>
                          selectedPermissions.includes(p)
                        );
                        const someSelected = permissions.some(p =>
                          selectedPermissions.includes(p)
                        );

                        return (
                          <div key={category} className='mb-6'>
                            <div className='flex items-center justify-between mb-3'>
                              <h4 className='font-medium text-sm flex items-center gap-2'>
                                {category === 'documents' && (
                                  <FileText className='w-4 h-4' />
                                )}
                                {category === 'emergency' && (
                                  <Shield className='w-4 h-4' />
                                )}
                                {category === 'account' && (
                                  <Heart className='w-4 h-4' />
                                )}
                                {categoryLabel}
                              </h4>
                              <div className='flex gap-2'>
                                <Button
                                  type='button'
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    handleSelectAllPermissions(category)
                                  }
                                  disabled={allSelected}
                                >
                                  Select All
                                </Button>
                                <Button
                                  type='button'
                                  variant='outline'
                                  size='sm'
                                  onClick={() =>
                                    handleDeselectAllPermissions(category)
                                  }
                                  disabled={!someSelected}
                                >
                                  Clear
                                </Button>
                              </div>
                            </div>

                            <div className='space-y-3 pl-4 border-l-2 border-gray-100'>
                              {permissions.map(permission => {
                                const def = PERMISSION_DEFINITIONS[permission];
                                return (
                                  <div
                                    key={permission}
                                    className='flex items-start space-x-3'
                                  >
                                    <Checkbox
                                      id={permission}
                                      checked={selectedPermissions.includes(
                                        permission
                                      )}
                                      onCheckedChange={checked =>
                                        handlePermissionChange(
                                          permission,
                                          checked as boolean
                                        )
                                      }
                                    />
                                    <div className='flex-1'>
                                      <label
                                        htmlFor={permission}
                                        className='text-sm font-medium cursor-pointer flex items-center gap-2'
                                      >
                                        {def.icon}
                                        {def.label}
                                      </label>
                                      <p className='text-xs text-muted-foreground mt-1'>
                                        {def.description}
                                      </p>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      }
                    )}

                    {selectedPermissions.length > 0 && (
                      <div className='mt-4 p-3 bg-blue-50 rounded-lg'>
                        <p className='text-sm font-medium text-blue-900'>
                          Selected Permissions ({selectedPermissions.length})
                        </p>
                        <div className='flex flex-wrap gap-1 mt-2'>
                          {selectedPermissions.map(permission => (
                            <Badge
                              key={permission}
                              variant='secondary'
                              className='text-xs'
                            >
                              {PERMISSION_DEFINITIONS[permission].label}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant='outline'
                    onClick={() => {
                      setIsInviteDialogOpen(false);
                      resetInviteForm();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleSendInvite} disabled={isLoading}>
                    {isLoading ? 'Sending...' : 'Send Invitation'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* Pending Invitations */}
        {pendingInvitations.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Clock className='h-5 w-5' />
                Pending Invitations
              </CardTitle>
              <CardDescription>
                Invitations waiting for response
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {pendingInvitations.map(invitation => (
                  <div
                    key={invitation.id}
                    className='flex items-center justify-between p-3 border rounded-lg'
                  >
                    <div>
                      <p className='font-medium'>{invitation.inviteeEmail}</p>
                      <p className='text-sm text-muted-foreground'>
                        Sent on {invitation.sentAt} • Expires{' '}
                        {invitation.expiresAt}
                      </p>
                    </div>
                    <Badge variant='secondary'>
                      <Clock className='w-3 h-3 mr-1' />
                      {invitation.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Active Linked Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              Linked Accounts ({linkedAccounts.length})
            </CardTitle>
            <CardDescription>
              People who have access to your shared documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            {linkedAccounts.length === 0 ? (
              <div className='text-center py-8 text-muted-foreground'>
                <Users className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <p>No linked accounts yet</p>
                <p className='text-sm'>Send an invitation to get started</p>
              </div>
            ) : (
              <div className='space-y-4'>
                {linkedAccounts.map(account => (
                  <div
                    key={account.id}
                    className='flex items-center justify-between p-4 border rounded-lg'
                  >
                    <div className='flex-1'>
                      <div className='flex items-center gap-3 mb-2'>
                        <h3 className='font-medium'>
                          {account.linkedUserName}
                        </h3>
                        {getStatusBadge(account.status)}
                      </div>
                      <p className='text-sm text-muted-foreground mb-2'>
                        {account.linkedUserEmail}
                      </p>
                      <p className='text-xs text-muted-foreground'>
                        Linked on {account.createdAt} • {account.linkType}
                      </p>
                      <div className='flex flex-wrap gap-1 mt-2'>
                        {account.permissions.map(permission => (
                          <Badge
                            key={permission}
                            variant='outline'
                            className='text-xs'
                          >
                            {PERMISSION_DEFINITIONS[permission]?.label ||
                              permission.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => {
                        setSelectedAccountToUnlink(account);
                        setIsUnlinkDialogOpen(true);
                      }}
                    >
                      <UserMinus className='w-4 h-4 mr-2' />
                      Unlink
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Unlink Confirmation Dialog */}
        <Dialog open={isUnlinkDialogOpen} onOpenChange={setIsUnlinkDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Unlink Account</DialogTitle>
              <DialogDescription>
                Are you sure you want to unlink{' '}
                {selectedAccountToUnlink?.linkedUserName}? This will remove
                their access to shared documents and cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <Alert>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>
                Any shared documents will be transferred to your individual
                account. The other person will lose access to these documents.
              </AlertDescription>
            </Alert>
            <DialogFooter>
              <Button
                variant='outline'
                onClick={() => setIsUnlinkDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant='destructive'
                onClick={handleUnlinkAccount}
                disabled={isLoading}
              >
                {isLoading ? 'Unlinking...' : 'Unlink Account'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
}
