/**
 * ContentSearch Component
 *
 * A search component for filtering educational content.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { ContentType, ContentFilters } from '@/types/education';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ContentSearchProps {
  filters: ContentFilters;
  onFiltersChange: (filters: ContentFilters) => void;
  availableTags: string[];
}

export function ContentSearch({
  filters,
  onFiltersChange,
  availableTags,
}: ContentSearchProps) {
  const [searchInput, setSearchInput] = useState(filters.search || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(
    filters.tags || []
  );
  const [selectedType, setSelectedType] = useState<ContentType | undefined>(
    filters.type
  );

  // Update filters when inputs change
  useEffect(() => {
    const newFilters: ContentFilters = { ...filters };

    if (searchInput) {
      newFilters.search = searchInput;
    } else {
      delete newFilters.search;
    }

    if (selectedTags.length > 0) {
      newFilters.tags = selectedTags;
    } else {
      delete newFilters.tags;
    }

    if (selectedType) {
      newFilters.type = selectedType;
    } else {
      delete newFilters.type;
    }

    onFiltersChange(newFilters);
  }, [searchInput, selectedTags, selectedType]);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  // Handle tag selection
  const handleTagSelect = (tag: string) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter(t => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  // Handle content type selection
  const handleTypeChange = (value: string) => {
    if (value === 'all') {
      setSelectedType(undefined);
    } else {
      setSelectedType(value as ContentType);
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchInput('');
    setSelectedTags([]);
    setSelectedType(undefined);
  };

  return (
    <div className='space-y-4'>
      <div className='flex flex-col sm:flex-row gap-3'>
        {/* Search Input */}
        <div className='flex-1'>
          <Input
            placeholder='Search for content...'
            value={searchInput}
            onChange={handleSearchChange}
            className='w-full'
          />
        </div>

        {/* Content Type Filter */}
        <div className='w-full sm:w-48'>
          <Select
            value={selectedType || 'all'}
            onValueChange={handleTypeChange}
          >
            <SelectTrigger>
              <SelectValue placeholder='Content Type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Types</SelectItem>
              <SelectItem value={ContentType.VIDEO}>Videos</SelectItem>
              <SelectItem value={ContentType.ARTICLE}>Articles</SelectItem>
              <SelectItem value={ContentType.INFOGRAPHIC}>
                Infographics
              </SelectItem>
              <SelectItem value={ContentType.AVATAR}>
                Interactive Assistants
              </SelectItem>
              <SelectItem value={ContentType.TOOLTIP}>
                Quick Definitions
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Clear Filters Button */}
        <Button
          variant='outline'
          onClick={clearFilters}
          disabled={!searchInput && selectedTags.length === 0 && !selectedType}
        >
          Clear Filters
        </Button>
      </div>

      {/* Tags */}
      <div>
        <h3 className='text-sm font-medium mb-2'>Popular Tags</h3>
        <div className='flex flex-wrap gap-2'>
          {availableTags.slice(0, 10).map(tag => (
            <Badge
              key={tag}
              variant={selectedTags.includes(tag) ? 'default' : 'outline'}
              className='cursor-pointer'
              onClick={() => handleTagSelect(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      </div>

      {/* Active Filters Summary */}
      {(searchInput || selectedTags.length > 0 || selectedType) && (
        <div className='pt-2'>
          <h3 className='text-sm font-medium mb-2'>Active Filters:</h3>
          <div className='flex flex-wrap gap-2'>
            {searchInput && (
              <Badge variant='secondary' className='flex items-center gap-1'>
                Search: {searchInput}
                <button
                  onClick={() => setSearchInput('')}
                  className='ml-1 text-xs'
                >
                  ✕
                </button>
              </Badge>
            )}

            {selectedType && (
              <Badge variant='secondary' className='flex items-center gap-1'>
                Type: {selectedType}
                <button
                  onClick={() => setSelectedType(undefined)}
                  className='ml-1 text-xs'
                >
                  ✕
                </button>
              </Badge>
            )}

            {selectedTags.map(tag => (
              <Badge
                key={tag}
                variant='secondary'
                className='flex items-center gap-1'
              >
                {tag}
                <button
                  onClick={() => handleTagSelect(tag)}
                  className='ml-1 text-xs'
                >
                  ✕
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
