'use client';

import React from 'react';
import { cn } from '../../../lib/utils';
import { RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface SelectionCardProps {
  id: string;
  value: string;
  label: string;
  checked: boolean;
}

export function SelectionCard({
  id,
  value,
  label,
  checked,
}: SelectionCardProps) {
  return (
    <div
      className={cn(
        'flex items-center space-x-2 border p-4 rounded-md transition-all cursor-pointer',
        checked ? 'border-[#7FAD00] bg-[#7FAD00]/10' : 'hover:border-gray-300'
      )}
      onClick={() => {
        // Programmatically click the radio input when the card is clicked
        const radioInput = document.getElementById(id);
        if (radioInput) {
          (radioInput as HTMLInputElement).click();
        }
      }}
    >
      <RadioGroupItem value={value} id={id} />
      <Label htmlFor={id} className='flex-1 cursor-pointer'>
        {label}
      </Label>
    </div>
  );
}
