// Attorney review types for the application

export interface AttorneyReview {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  userState: string;
  status: 'Requested' | 'In Progress' | 'Completed' | 'Cancelled';
  requestedAt: string;
  assignedAttorneyId?: string;
  assignedAttorneyName?: string;
  completedAt?: string;
  notes?: string;
  documents: AttorneyReviewDocument[];
}

export interface AttorneyReviewDocument {
  id: string;
  name: string;
  type: string;
}

export interface AuditLogEntry {
  id: string;
  userId: string;
  action: string;
  timestamp: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
}

// Attorney management types for admin dashboard
export interface Attorney {
  id: string;
  name: string;
  firm?: string;
  phone: string;
  email: string;
  address?: string;
  city?: string;
  state: string;
  zipCode?: string;
  specialties?: string[];
  barNumber?: string;
  yearsExperience?: number;
  rating?: number;
  isPreferred?: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CreateAttorneyRequest {
  name: string;
  phone: string;
  email: string;
  state: string;
  firm?: string;
  address?: string;
  city?: string;
  zipCode?: string;
  specialties?: string[];
  barNumber?: string;
  yearsExperience?: number;
}

export interface UpdateAttorneyRequest extends Partial<CreateAttorneyRequest> {
  id: string;
}

export interface AttorneyFilters {
  state?: string;
  search?: string;
  isActive?: boolean;
}
