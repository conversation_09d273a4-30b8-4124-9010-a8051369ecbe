'use client';

import React from 'react';

interface DocumentPreviewProps {
  documentType: string;
  documentContent: React.ReactNode;
  showWatermark?: boolean;
  className?: string;
}

export function DocumentPreview({
  documentType,
  documentContent,
  showWatermark = true,
  className = '',
}: DocumentPreviewProps) {
  return (
    <div
      className={`border p-6 rounded-md bg-background shadow-sm relative ${className}`}
    >
      {/* Watermark */}
      {showWatermark && (
        <div className='absolute inset-0 flex items-center justify-center opacity-20 rotate-45 text-red-500 text-6xl font-bold pointer-events-none'>
          DRAFT
        </div>
      )}

      {/* Document content */}
      <div className='relative z-10'>{documentContent}</div>
    </div>
  );
}

// Example will template component
export function WillTemplate({
  name = '____________________',
  address = '____________________',
  executor = '____________________',
  homeBeneficiary = '____________________',
  bankBeneficiary = '____________________',
  investmentBeneficiary = '____________________',
}) {
  return (
    <>
      <h3 className='text-center text-xl font-bold mb-6'>
        LAST WILL AND TESTAMENT
      </h3>

      <p className='mb-4'>
        I, {name}, residing at {address}, being of sound mind, do hereby make,
        publish, and declare this to be my Last Will and Testament, hereby
        revoking all wills and codicils previously made by me.
      </p>

      <p className='mb-4'>
        I hereby nominate and appoint {executor} to be the Executor of this, my
        Last Will and Testament.
      </p>

      <h4 className='font-bold mt-6 mb-2'>
        ARTICLE I: DISTRIBUTION OF PROPERTY
      </h4>
      <p className='mb-4'>
        I give, devise, and bequeath my property as follows:
      </p>
      <ol className='list-decimal list-inside mb-4 pl-4'>
        <li className='mb-2'>
          My home located at {address}, I give to {homeBeneficiary}.
        </li>
        <li className='mb-2'>
          All funds in my bank accounts, I give to {bankBeneficiary}.
        </li>
        <li className='mb-2'>
          My investment accounts and retirement funds, I give to{' '}
          {investmentBeneficiary}.
        </li>
      </ol>

      <h4 className='font-bold mt-6 mb-2'>ARTICLE II: EXECUTOR POWERS</h4>
      <p className='mb-4'>
        I grant to my Executor all powers necessary to administer my estate...
      </p>

      <div className='mt-8 text-center'>
        <p>
          IN WITNESS WHEREOF, I have hereunto set my hand this _____ day of
          ____________, 20___.
        </p>
        <div className='mt-6 border-t pt-4'>
          <p>____________________________</p>
          <p>{name}, Testator</p>
        </div>
      </div>
    </>
  );
}

// Example trust template component
export function TrustTemplate({
  name = '____________________',
  address = '____________________',
  trustee = '____________________',
  successorTrustee = '____________________',
  beneficiary = '____________________',
}) {
  return (
    <>
      <h3 className='text-center text-xl font-bold mb-6'>
        REVOCABLE LIVING TRUST
      </h3>

      <p className='mb-4'>
        I, {name}, residing at {address}, hereby establish this Revocable Living
        Trust.
      </p>

      <h4 className='font-bold mt-6 mb-2'>ARTICLE I: TRUST CREATION</h4>
      <p className='mb-4'>
        This Trust shall be known as the {name} Revocable Living Trust, dated
        ____________.
      </p>

      <h4 className='font-bold mt-6 mb-2'>ARTICLE II: TRUSTEE</h4>
      <p className='mb-4'>
        I hereby appoint {trustee} as the Trustee of this Trust. If {trustee} is
        unable or unwilling to serve, I appoint {successorTrustee} as Successor
        Trustee.
      </p>

      <h4 className='font-bold mt-6 mb-2'>ARTICLE III: BENEFICIARIES</h4>
      <p className='mb-4'>
        Upon my death, the Trustee shall distribute the Trust assets to{' '}
        {beneficiary}.
      </p>

      <div className='mt-8 text-center'>
        <p>
          IN WITNESS WHEREOF, I have hereunto set my hand this _____ day of
          ____________, 20___.
        </p>
        <div className='mt-6 border-t pt-4'>
          <p>____________________________</p>
          <p>{name}, Grantor</p>
        </div>
      </div>
    </>
  );
}
