'use client';

import Link from 'next/link';

export function Footer() {
  return (
    <footer className='bg-black-6c text-gray-6197c py-8'>
      <div className='container mx-auto'>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
          <div>
            <div className='mb-4'>
              <span className='text-xl font-bold text-gray-6197c'>
                ChildFree
              </span>
            </div>
            <p className='text-sm mb-4'>
              Empowering the childfree community to plan an amazing life.
            </p>
            <div>
              <h4 className='text-lg font-geologica font-semibold mb-2'>
                Contact
              </h4>
              <p className='text-sm'>Email: <EMAIL></p>
              <p className='text-sm'>Phone: (*************</p>
            </div>
          </div>
          <div className='grid grid-cols-2 gap-4'>
            <div>
              <h4 className='text-lg font-geologica font-semibold mb-2'>
                Quick Links
              </h4>
              <ul className='space-y-1'>
                <li>
                  <Link
                    href='/'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Services
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Resources
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className='text-lg font-geologica font-semibold mb-2'>
                Legal
              </h4>
              <ul className='space-y-1'>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href='#'
                    className='text-sm hover:text-green-2010c transition-colors'
                  >
                    Cookie Policy
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className='border-t border-gray-6197c/20 mt-8 pt-8 text-center text-sm'>
          <p>
            &copy; {new Date().getFullYear()} Childfree Legacy. All rights
            reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
