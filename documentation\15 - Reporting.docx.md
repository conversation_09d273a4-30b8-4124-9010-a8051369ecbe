# 15 \- Reporting

# **Document 15 \- Functional Specification: Reporting**

## **Overview**

The Reporting feature in the Childfree Legacy Web Application, powered by **AWS Amplify**’s serverless architecture, equips Administrators with robust tools to generate, view, and schedule reports on system usage, member activities, financial transactions, and compliance metrics. It also enables Members to access summaries of their own activities, enhancing transparency. This feature is critical for monitoring system performance, managing resources, ensuring compliance with standards such as HIPAA and SOC2, and supporting the estate planning needs of the childfree community, particularly those aged 65+. Designed with security, accessibility, and usability in mind, the Reporting feature fosters trust and operational efficiency through scalable, real-time reporting capabilities.

---

## **1\. Key Requirements**

The Reporting feature must meet the following requirements to effectively support its stakeholders:

### **Report Types**

- **System Usage Reports**: Track overall system performance, user logins, and activity logs (e.g., page load times, concurrent users).
- **Member Activity Reports**: Monitor member interactions, including document updates, payment histories, and subscription statuses.
- **Financial Reports**: Detail billing information, payment statuses, and subscription revenues (e.g., monthly payment trends, missed payments).
- **Compliance Reports**: Ensure adherence to HIPAA, SOC2, and state-specific standards, including audit trails and permission changes.
- **Welon Reports**: Report on member activity status changes and internal notes.
- **Professional Reports**: Allow Administrators to run and download reports on professional activities (e.g., legal review selection, financial advisor).
- **Custom Reports**: Enable Administrators to create ad-hoc reports based on specific criteria (e.g., Members by state, opt-out rates).
- **Member Activity Summaries** (Future): Provide Members with personal summaries of their recent activities, document updates, and payment history.

### **Access Control**

- Restricted to Administrators with "Administrator \- Reporting" or "Administrator \- Advanced" subroles for full report generation and viewing, managed via **AWS Cognito User Groups**.
- Members can only access their own activity summaries via their dashboard (future phase).
- Role-Based Access Control (RBAC) ensures granular permissions (e.g., "Administrator \- Finance" can access financial reports only).

### **Report Formats**

- Exportable in PDF, CSV, or Excel formats for flexibility.
- On-screen previews available before exporting for review.

### **Scheduled Reports**

- Administrators can schedule reports to run at configurable intervals (e.g., daily, weekly, monthly) using **Amazon EventBridge**.
- Automated delivery via email to designated recipients using **Amazon Simple Email Service (SES)**.

### **Data Filtering and Sorting**

- Users can filter reports by parameters such as date ranges, member types (e.g., active, deceased), states, or subroles.
- Sorting options available (e.g., by date, member ID, payment amount).

### **Real-time Data**

- Reports reflect the most current data available, with a maximum latency of 200ms for API responses, powered by **AWS AppSync**.

### **Performance**

- Report generation must complete within 10 seconds for 95% of requests, even with up to 1,000 concurrent users, leveraging **AWS Lambda** for scalability.

---

## **2\. User Flows**

The Reporting feature supports distinct workflows for Administrators and Members:

### **Administrator Generating a Report**

1. Administrator navigates to the reporting dashboard at /admin/reports.
2. Selects the report type from a dropdown (e.g., "Member Activity Report").
3. Applies filters and parameters (e.g., date range: last 30 days, member status: active).
4. Previews the report in a table format on-screen.
5. Chooses to:
   - Export the report in PDF, CSV, or Excel.
   - Schedule the report for automated generation and email delivery (e.g., <NAME_EMAIL>).

### **Member Viewing Their Activity Summary (Future)**

1. Member navigates to their account dashboard at /member/dashboard.
2. Clicks "View Activity Summary" in the navigation menu.
3. Views a pre-generated summary of recent activities (e.g., document updates, payments) in a simplified table or list format.

---

## **3\. Edge Cases**

The system must handle the following edge cases gracefully:

- **Large Data Sets**: Reports with thousands of records must generate efficiently without exceeding the 10-second response time limit. Pagination or lazy loading may be used for on-screen previews.
- **No Data Available**: If no data matches the report criteria, display "No data found for the selected parameters" with suggestions (e.g., "Try adjusting the date range").
- **Permission Denied**: Users without proper permissions attempting to access /admin/reports are redirected to their dashboard with an "Access denied" notification.
- **Scheduled Report Failures**: If a scheduled report fails (e.g., due to server issues), log the failure in **Amazon CloudWatch** and notify the Administrator via **Amazon SNS** with retry options.
- **Inactive Members**: Ensure reports can include data for archived or deceased Members when relevant (e.g., financial history).

---

## **4\. Compliance Considerations**

The Reporting feature must adhere to the following regulatory and security standards:

- **Data Privacy**: Reports must not expose sensitive member information (e.g., full Social Security Numbers) beyond what is necessary, per HIPAA and CCPA requirements.
- **Audit Trails**: Log all report generations, exports, and schedule creations with timestamps, user IDs, and report parameters in **Amazon CloudWatch** for compliance audits (SOC2).
- **Retention Policies**: Retain report data and logs for at least 10 years or as required by law, managed via **Amazon S3 Lifecycle Policies**.
- **Accessibility**: Ensure report previews and summaries meet WCAG 2.1 Level AA standards (e.g., 4.5:1 contrast ratio, resizable text).

---

## **5\. UI Components**

The Reporting feature includes the following user interface elements:

| Element                   | Description                                                             |
| ------------------------- | ----------------------------------------------------------------------- |
| Report Selection Dropdown | Allows users to choose the report type (e.g., System Usage, Financial). |
| Filter Inputs             | Inputs for filtering (e.g., date pickers, member type dropdowns).       |
| Preview Table             | Displays report data on-screen with sortable columns.                   |
| Export Buttons            | Buttons for exporting in PDF, CSV, or Excel formats.                    |
| Schedule Report Form      | Form to set report frequency (e.g., daily) and email recipients.        |
| Error Message             | Displays issues (e.g., "No data found") with guidance.                  |
| Activity Summary List     | Simplified list or table for Members’ personal summaries.               |

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /admin/reports: Reporting dashboard for Administrators.
  - /member/reports: Member activity summary page.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - getReportTypes:
    - **Input**: None
    - **Output**: { success, reportTypes: \["System Usage", "Member Activity", ...\] }
    - Lists available report types.
  - generateReport:
    - **Input**: { reportType, filters: { startDate, endDate, memberType }, format }
    - **Output**: { success, reportData, fileUrl (if exported) }
    - Generates a report based on parameters.
  - scheduleReport:
    - **Input**: { reportType, filters, frequency, recipients }
    - **Output**: { success, scheduleId }
    - Schedules a report for automated delivery.
  - getMemberActivitySummary:
    - **Input**: None
    - **Output**: { success, summary: { activities: \[...\], payments: \[...\] } }
    - Retrieves a Member’s activity summary.

### **Database (Amazon DynamoDB)**

- **reports**:
  - **Columns**: id, type, parameters (JSON), generated_at, user_id, file_url.
  - Stores generated report metadata and links to exported files.
- **report_schedules**:
  - **Columns**: id, report_type, frequency, recipients (JSON), next_run, created_by.
  - Manages scheduled reports.

### **Security**

- **HTTPS (TLS/SSL)**: Enforced for all report-related communications.
- **RBAC**: Implemented via **AWS Cognito** and **AWS AppSync resolvers** to restrict API access by subrole.
- **Encryption**: Sensitive report data encrypted at rest using **AWS KMS**.

### **Infrastructure**

- **AWS Services**:
  - **Amazon S3**: Stores exported report files securely.
  - **AWS Lambda**: Handles report generation for scalability.
  - **Amazon CloudWatch**: Monitors performance and logs failures.
  - **Amazon EventBridge**: Schedules automated report generation and delivery.

---

## **7\. Testing and Validation**

The Reporting feature requires rigorous testing to ensure reliability and compliance:

- **Unit Tests**: Validate report generation logic, data filtering, and export functionality (e.g., correct CSV formatting).
- **Integration Tests**: Verify integration with **DynamoDB**, **AWS AppSync**, and **Amazon S3**.
- **Security Testing**: Confirm RBAC enforcement and data privacy (e.g., no unauthorized access to financial reports).
- **Performance Testing**: Ensure report generation meets the 10-second threshold with 1,000 concurrent users using **AWS Lambda**.
- **User Acceptance Testing (UAT)**: Validate usability for Administrators and Members, focusing on the 65+ demographic (e.g., readable previews).

### **Test Cases**

| Scenario                          | Expected Outcome                                 |
| --------------------------------- | ------------------------------------------------ |
| Generate Member Activity Report   | Report generated in \<10s with accurate data     |
| Export Financial Report as PDF    | PDF file downloaded with correct formatting      |
| Schedule Weekly Compliance Report | Report emailed weekly to specified recipients    |
| Member Views Activity Summary     | Summary displayed within 3s, readable format     |
| Unauthorized Report Access        | "Access denied" message, redirected to dashboard |

---

## **8\. Summary**

The Reporting feature, powered by AWS Amplify, is a cornerstone of the Childfree Legacy platform, empowering Administrators to monitor system health, member activities, and financial transactions while ensuring compliance with legal standards. For Members, it provides transparency into their own estate planning activities. By adhering to this specification, developers can deliver a secure, efficient, and user-friendly reporting system that supports the platform’s mission to empower the childfree community. With robust access controls, real-time data, and flexible delivery options, this feature meets the diverse needs of its stakeholders while maintaining stringent security and accessibility standards.
