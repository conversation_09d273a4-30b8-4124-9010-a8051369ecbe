import { SidebarSkeleton } from './sidebar-skeleton';
import { DashboardContentSkeleton } from './dashboard-content-skeleton';

interface DashboardContentLayoutSkeletonProps {
  sidebarItemCount?: number;
  contentCardCount?: number;
  showAdditionalContent?: boolean;
}

/**
 * Dashboard layout skeleton without header - for use when header is handled by parent layout
 */
export function DashboardContentLayoutSkeleton({
  sidebarItemCount = 6,
  contentCardCount = 6,
  showAdditionalContent = true,
}: DashboardContentLayoutSkeletonProps) {
  return (
    <div className='min-h-[calc(100vh-4rem)] bg-background flex'>
      {/* Sidebar Skeleton */}
      <SidebarSkeleton
        className='w-64 bg-background border-r border-gray-200 min-h-[calc(100vh-4rem)] shadow-sm'
        itemCount={sidebarItemCount}
      />

      {/* Main Content Skeleton */}
      <DashboardContentSkeleton
        showHeader={true}
        cardCount={contentCardCount}
        showAdditionalContent={showAdditionalContent}
      />
    </div>
  );
}
