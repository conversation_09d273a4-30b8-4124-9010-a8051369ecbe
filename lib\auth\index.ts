// Auth Guards
export { AuthGuard } from './auth-guard';
export { AdminGuard } from './admin-guard';
export { RoleGuard } from './role-guard';

// Higher-Order Components
export {
  withAdminAccess,
  withFullAdminAccess,
  withAnyAdminAccess,
  withWelonTrustAccess,
} from './with-admin-access';

// Re-export hooks for convenience
export {
  useAdminAccess,
  useHasAdminRole,
  useHasAnyAdminRole,
  useHasAllAdminRoles,
  useAdminNavigation,
} from '../hooks/useAdminAccess';

// Re-export utilities for convenience
export {
  ADMIN_ROLES,
  isAdmin,
  isWelonTrust,
  hasAnyAdminRole,
  hasAdminRole,
  hasAnyOfAdminRoles,
  hasAllAdminRoles,
  getAdminPermissions,
  canPerformAdminAction,
  getRoleDisplayNames,
  canAccessAdminRoute,
  getUnauthorizedMessage,
} from '../utils/admin-utils';

export type { AdminRole } from '../utils/admin-utils';
export type { AdminAccessInfo } from '../hooks/useAdminAccess';
