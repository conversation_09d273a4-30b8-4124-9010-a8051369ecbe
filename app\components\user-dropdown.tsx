'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  User,
  Settings,
  LogOut,
  ChevronDown,
  UserCircle,
  RefreshCw,
  Shield,
} from 'lucide-react';
// import { useRole } from "@/lib/roles/role-context";
// import { RoleManager } from "@/lib/roles/role-manager";

interface UserDropdownProps {
  userRole: string;
  onLogout: () => void;
}

export function UserDropdown({ userRole, onLogout }: UserDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showRoleSwitcher, setShowRoleSwitcher] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  // const { userContext, switchRole } = useRole();
  // const roleManager = RoleManager.getInstance();
  const userContext = null;
  const switchRole = () => {};

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown when pressing Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleLogout = () => {
    setIsOpen(false);
    onLogout();
  };

  const handleRoleSwitch = (role: string, subrole?: string) => {
    // switchRole(role as any, subrole);
    setShowRoleSwitcher(false);
    setIsOpen(false);

    // Redirect to appropriate dashboard based on role
    const getRedirectPath = (role: string) => {
      switch (role) {
        case 'administrator':
          return '/admin';
        case 'welon_trust':
          return '/emergency';
        case 'linked_account':
          return '/linked';
        case 'member':
        default:
          return '/dashboard';
      }
    };

    const redirectPath = getRedirectPath(role);
    window.location.href = redirectPath;
  };

  // const testUsers = roleManager.getTestUsers();
  const testUsers: any[] = [];
  const currentDisplayName = (userContext as any)?.displayName || userRole;

  return (
    <div className='relative' ref={dropdownRef}>
      {/* User Button Trigger */}
      <Button
        variant='ghost'
        onClick={() => setIsOpen(!isOpen)}
        className='flex items-center space-x-2 px-3 py-2 h-auto hover:bg-gray-100'
        aria-expanded={isOpen}
        aria-haspopup='true'
      >
        <UserCircle className='h-6 w-6 text-[var(--custom-gray-medium)]' />
        <span className='text-sm font-medium text-[var(--custom-gray-dark)]'>
          {currentDisplayName}
        </span>
        <ChevronDown
          className={`h-4 w-4 text-[var(--custom-gray-medium)] transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </Button>

      {/* Dropdown Menu */}
      {isOpen && !showRoleSwitcher && (
        <div className='absolute right-0 mt-2 w-64 bg-background border border-gray-200 rounded-lg shadow-lg z-50'>
          <div className='py-2'>
            {/* Profile Section */}
            <div className='px-4 py-2 border-b border-gray-100'>
              <p className='text-sm font-medium'>Signed in as</p>
              <p className='text-sm text-[var(--custom-gray-medium)]'>
                {currentDisplayName}
              </p>
            </div>

            {/* Menu Items */}
            <div className='py-1'>
              {/* <Link
                href="/profile"
                className="flex items-center px-4 py-2 text-sm text-[var(--custom-gray-dark)] hover:bg-gray-100 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User className="h-4 w-4 mr-3" />
                Profile
              </Link> */}

              {/* <Link
                href="/settings"
                className="flex items-center px-4 py-2 text-sm text-[var(--custom-gray-dark)] hover:bg-gray-100 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Settings className="h-4 w-4 mr-3" />
                Settings
              </Link> */}

              {/* Role Switcher */}
              <button
                onClick={() => setShowRoleSwitcher(true)}
                className='flex items-center w-full px-4 py-2 text-sm text-[var(--custom-gray-dark)] hover:bg-gray-100 transition-colors cursor-pointer'
              >
                <RefreshCw className='h-4 w-4 mr-3' />
                Switch Role (Testing)
              </button>
            </div>

            {/* Logout Section */}
            <div className='border-t border-gray-100 py-1'>
              <button
                onClick={handleLogout}
                className='flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors cursor-pointer'
              >
                <LogOut className='h-4 w-4 mr-3' />
                Sign out
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Role Switcher Menu */}
      {isOpen && showRoleSwitcher && (
        <div className='absolute right-0 mt-2 w-72 bg-background border border-gray-200 rounded-lg shadow-lg z-50'>
          <div className='py-2'>
            {/* Header */}
            <div className='px-4 py-2 border-b border-gray-100'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Switch Role
                  </p>
                  <p className='text-xs text-[var(--custom-gray-medium)]'>
                    For testing purposes
                  </p>
                </div>
                <button
                  onClick={() => setShowRoleSwitcher(false)}
                  className='text-[var(--custom-gray-medium)] hover:text-[var(--custom-gray-medium)] cursor-pointer p-0 h-auto w-auto bg-transparent border-0 text-lg leading-none'
                >
                  ×
                </button>
              </div>
            </div>

            {/* Role Options */}
            <div className='py-1 max-h-64 overflow-y-auto'>
              {testUsers.map((user, index) => (
                <button
                  key={index}
                  onClick={() => handleRoleSwitch(user.role, user.subrole)}
                  className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 transition-colors cursor-pointer ${
                    (userContext as any)?.role === user.role &&
                    (userContext as any)?.subrole === user.subrole
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-[var(--custom-gray-dark)]'
                  }`}
                >
                  <Shield className='h-4 w-4 mr-3' />
                  <div className='text-left'>
                    <div className='font-medium'>{user.name}</div>
                    <div className='text-xs text-[var(--custom-gray-medium)]'>
                      {user.role.replace('_', ' ')}{' '}
                      {user.subrole ? `- ${user.subrole}` : ''}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
