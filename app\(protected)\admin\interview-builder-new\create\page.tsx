'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  MessageSquare,
  AlertTriangle,
  Save,
  Loader2,
} from 'lucide-react';
import {
  CreateInterviewNewRequest,
  InterviewFormData,
  ValidationErrors,
} from '@/types/interview-builder-new';
import {
  createInterview,
  validateInterviewData,
} from '@/lib/api/interview-builder-new';

export default function CreateInterviewPage() {
  const router = useRouter();

  const [formData, setFormData] = useState<InterviewFormData>({
    name: '',
    description: '',
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[name as keyof ValidationErrors]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name as keyof ValidationErrors];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    const validationErrors = validateInterviewData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      const createData: CreateInterviewNewRequest = {
        name: formData.name,
        description: formData.description || undefined,
      };

      const newInterview = await createInterview(createData);

      // Redirect to build page to start editing the interview
      router.push(`/admin/interview-builder-new/build/${newInterview.id}`);
    } catch (err) {
      setErrors({ submit: 'Failed to create interview. Please try again.' });
      console.error('Error creating interview:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/interview-builder-new');
  };

  return (
    <div className='max-w-4xl mx-auto'>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center space-x-4'>
          <Button onClick={handleCancel} variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Interview Builder
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
              <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
              Create New Interview
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Create a new interview template for data collection
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Interview Details</CardTitle>
          <CardDescription>
            Provide basic information about your new interview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {errors.submit && (
              <Alert variant='destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Interview Name */}
            <div className='space-y-2'>
              <Label htmlFor='name' className='text-sm font-medium'>
                Interview Name *
              </Label>
              <Input
                id='name'
                name='name'
                type='text'
                value={formData.name}
                onChange={handleInputChange}
                placeholder='Enter interview name'
                className={errors.name ? 'border-red-500' : ''}
                disabled={saving}
              />
              {errors.name && (
                <p className='text-sm text-red-600'>{errors.name}</p>
              )}
            </div>

            {/* Interview Description */}
            <div className='space-y-2'>
              <Label htmlFor='description' className='text-sm font-medium'>
                Description
              </Label>
              <Textarea
                id='description'
                name='description'
                value={formData.description}
                onChange={handleInputChange}
                placeholder='Enter interview description (optional)'
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
                disabled={saving}
              />
              {errors.description && (
                <p className='text-sm text-red-600'>{errors.description}</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className='flex justify-end space-x-3 pt-6'>
              <Button
                type='button'
                variant='outline'
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={saving} className='min-w-[120px]'>
                {saving ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className='mr-2 h-4 w-4' />
                    Create Interview
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
