'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  Pa<PERSON>ationPrevious,
  PaginationEllipsis,
} from '@/components/ui/pagination';
import {
  FileText,
  Download,
  Eye,
  Filter,
  Search,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Calendar,
  User,
  Shield,
  AlertCircle,
  CheckCircle,
  Send,
  Edit,
  Clock,
  BookOpen,
  Scale,
  PenTool,
  ChevronDown,
  ChevronUp,
  Users,
  UserCheck,
} from 'lucide-react';
import { Headline, Subhead } from '../../components/ui/brand/typography';
import { useUserContextOptional } from '@/components/welon-trust/user-context';
import { useRole } from '@/lib/roles/role-context';
import { useLinkedAccountContextOptional } from '@/lib/contexts/linked-account-context';

interface Document {
  id: string;
  title: string;
  type: 'Legal' | 'Medical' | 'Personal' | 'Financial';
  status:
    | 'draft'
    | 'awaiting_verification'
    | 'verified'
    | 'active'
    | 'archived'
    | 'pending_review'
    | 'expired';
  dateCreated: string;
  lastModified: string;
  version: string;
  userId: string;
  userName: string;
  fileSize: string;
  description: string;
  isVerified?: boolean;
  verificationDate?: string;
  canEdit?: boolean;
}

// Generate mock documents
const generateDocuments = (
  includeAllUsers: boolean = false,
  specificUserId?: string
) => {
  const users = [
    { id: 'user-1', name: 'Emily Rodriguez' },
    { id: 'user-2', name: 'Michael Chen' },
    { id: 'user-3', name: 'Sarah Johnson' },
    { id: 'user-4', name: 'David Wilson' },
    { id: 'user-5', name: 'Lisa Anderson' },
    { id: 'user-6', name: 'Robert Brown' },
    { id: 'user-7', name: 'Jennifer Davis' },
    { id: 'user-8', name: 'James Miller' },
  ];

  const documentTypes: Document['type'][] = [
    'Legal',
    'Medical',
    'Personal',
    'Financial',
  ];
  const statuses: Document['status'][] = [
    'draft',
    'awaiting_verification',
    'verified',
    'active',
    'archived',
    'pending_review',
    'expired',
  ];

  const documentTitles = {
    Legal: [
      'Last Will and Testament',
      'Power of Attorney',
      'Trust Agreement',
      'Living Will',
      'Estate Plan',
    ],
    Medical: [
      'Healthcare Directive',
      'Medical Power of Attorney',
      'DNR Order',
      'Medical Records',
      'Insurance Info',
    ],
    Personal: [
      'Personal Instructions',
      'Digital Assets',
      'Pet Care Instructions',
      'Funeral Wishes',
      'Personal Letters',
    ],
    Financial: [
      'Bank Account Info',
      'Investment Portfolio',
      'Insurance Policies',
      'Retirement Accounts',
      'Tax Documents',
    ],
  };

  const documents: Document[] = [];
  const usersToProcess = includeAllUsers
    ? users
    : users.filter(u => !specificUserId || u.id === specificUserId);

  usersToProcess.forEach(user => {
    const seed = user.id
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const random = (min: number, max: number) =>
      Math.floor(
        (((seed * 9301 + 49297) % 233280) / 233280) * (max - min + 1)
      ) + min;

    const numDocs = random(3, 8);
    for (let i = 0; i < numDocs; i++) {
      const type = documentTypes[i % documentTypes.length];
      const titles = documentTitles[type];
      const title = titles[random(0, titles.length - 1)];
      const status = statuses[random(0, statuses.length - 1)];

      const createdDate = new Date(
        Date.now() - random(1, 365) * 24 * 60 * 60 * 1000
      );
      const modifiedDate = new Date(
        createdDate.getTime() + random(0, 30) * 24 * 60 * 60 * 1000
      );

      // Determine verification status
      const isVerified =
        status === 'verified' ||
        status === 'active' ||
        status === 'pending_review';
      const verificationDate = isVerified
        ? new Date(
            modifiedDate.getTime() + random(0, 7) * 24 * 60 * 60 * 1000
          ).toISOString()
        : undefined;
      const canEdit = status === 'draft' || status === 'awaiting_verification';

      documents.push({
        id: `doc-${user.id}-${i}`,
        title,
        type,
        status,
        dateCreated: createdDate.toISOString(),
        lastModified: modifiedDate.toISOString(),
        version: `${random(1, 5)}.${random(0, 9)}`,
        userId: user.id,
        userName: user.name,
        fileSize: `${random(100, 5000)} KB`,
        description: `${type} document for ${user.name}`,
        isVerified,
        verificationDate,
        canEdit,
      });
    }
  });

  return documents.sort(
    (a, b) =>
      new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
  );
};

// Generate documents for review (documents that need user review)
const generateReviewDocuments = () => {
  const reviewDocs: Document[] = [
    {
      id: 'review-1',
      title: 'Updated Privacy Policy',
      type: 'Legal',
      status: 'pending_review',
      dateCreated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(
        Date.now() - 2 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '2.1',
      userId: 'user-1',
      userName: 'System',
      fileSize: '45 KB',
      description:
        'Updated privacy policy requires your review and acknowledgment',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-2',
      title: 'Annual Estate Plan Review',
      type: 'Legal',
      status: 'pending_review',
      dateCreated: new Date(
        Date.now() - 10 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 3 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.0',
      userId: 'user-1',
      userName: 'Legal Team',
      fileSize: '120 KB',
      description: 'Annual review of your estate planning documents',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-3',
      title: 'Healthcare Directive Update',
      type: 'Medical',
      status: 'pending_review',
      dateCreated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(
        Date.now() - 1 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.2',
      userId: 'user-1',
      userName: 'Medical Team',
      fileSize: '78 KB',
      description: 'Updated healthcare directive based on recent legal changes',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-4',
      title: 'Terms of Service Update',
      type: 'Legal',
      status: 'pending_review',
      dateCreated: new Date(
        Date.now() - 12 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 4 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '3.0',
      userId: 'user-1',
      userName: 'Legal Team',
      fileSize: '67 KB',
      description: 'Updated terms of service with new compliance requirements',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-5',
      title: 'Insurance Policy Changes',
      type: 'Financial',
      status: 'pending_review',
      dateCreated: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(
        Date.now() - 2 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.5',
      userId: 'user-1',
      userName: 'Insurance Team',
      fileSize: '89 KB',
      description: 'Changes to your insurance policy coverage and benefits',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-6',
      title: 'Investment Strategy Review',
      type: 'Financial',
      status: 'pending_review',
      dateCreated: new Date(
        Date.now() - 15 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 5 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '2.0',
      userId: 'user-1',
      userName: 'Financial Advisor',
      fileSize: '134 KB',
      description: 'Annual review of your investment portfolio and strategy',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'review-7',
      title: 'Medical Records Access',
      type: 'Medical',
      status: 'pending_review',
      dateCreated: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(
        Date.now() - 1 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.0',
      userId: 'user-1',
      userName: 'Healthcare Provider',
      fileSize: '56 KB',
      description: 'Authorization for medical records access and sharing',
      isVerified: true,
      canEdit: false,
    },
  ];
  return reviewDocs;
};

// Generate documents for signing (documents ready for signature)
const generateSigningDocuments = () => {
  const signingDocs: Document[] = [
    {
      id: 'sign-1',
      title: 'Will Amendment',
      type: 'Legal',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 15 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 1 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '3.0',
      userId: 'user-1',
      userName: 'Legal Team',
      fileSize: '95 KB',
      description: 'Amendment to your will - ready for signature',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-2',
      title: 'Power of Attorney Update',
      type: 'Legal',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 12 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 2 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '2.0',
      userId: 'user-1',
      userName: 'Legal Team',
      fileSize: '67 KB',
      description:
        'Updated power of attorney document requiring your signature',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-3',
      title: 'Trust Agreement',
      type: 'Financial',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 20 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 3 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.0',
      userId: 'user-1',
      userName: 'Financial Advisor',
      fileSize: '156 KB',
      description: 'New trust agreement ready for execution',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-4',
      title: 'Healthcare Proxy',
      type: 'Medical',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 18 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 4 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.3',
      userId: 'user-1',
      userName: 'Medical Team',
      fileSize: '73 KB',
      description: 'Healthcare proxy designation requiring your signature',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-5',
      title: 'Investment Authorization',
      type: 'Financial',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 14 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 2 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '2.1',
      userId: 'user-1',
      userName: 'Financial Advisor',
      fileSize: '84 KB',
      description: 'Authorization for new investment strategy implementation',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-6',
      title: 'Property Transfer',
      type: 'Legal',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 25 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 5 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.0',
      userId: 'user-1',
      userName: 'Legal Team',
      fileSize: '112 KB',
      description: 'Property transfer documents ready for execution',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-7',
      title: 'Insurance Beneficiary Update',
      type: 'Financial',
      status: 'verified',
      dateCreated: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
      lastModified: new Date(
        Date.now() - 1 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '1.1',
      userId: 'user-1',
      userName: 'Insurance Team',
      fileSize: '58 KB',
      description: 'Update to insurance policy beneficiaries',
      isVerified: true,
      canEdit: false,
    },
    {
      id: 'sign-8',
      title: 'Living Will Update',
      type: 'Medical',
      status: 'verified',
      dateCreated: new Date(
        Date.now() - 22 * 24 * 60 * 60 * 1000
      ).toISOString(),
      lastModified: new Date(
        Date.now() - 6 * 24 * 60 * 60 * 1000
      ).toISOString(),
      version: '2.0',
      userId: 'user-1',
      userName: 'Medical Team',
      fileSize: '91 KB',
      description: 'Updated living will with new medical directives',
      isVerified: true,
      canEdit: false,
    },
  ];
  return signingDocs;
};

type SortField =
  | 'title'
  | 'type'
  | 'status'
  | 'dateCreated'
  | 'lastModified'
  | 'version'
  | 'userName';
type SortDirection = 'asc' | 'desc';

// Simple Tooltip component
interface TooltipProps {
  content: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

function Tooltip({ content, children, position = 'top' }: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);

  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 translate-y-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 -translate-x-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 translate-x-2',
  };

  return (
    <div
      className='relative inline-block'
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          className={`absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg whitespace-nowrap ${positionClasses[position]}`}
        >
          {content}
          {/* Arrow */}
          <div
            className={`absolute w-2 h-2 bg-gray-900 transform rotate-45 ${
              position === 'top'
                ? 'top-full left-1/2 -translate-x-1/2 -translate-y-1/2'
                : position === 'bottom'
                  ? 'bottom-full left-1/2 -translate-x-1/2 translate-y-1/2'
                  : position === 'left'
                    ? 'left-full top-1/2 -translate-y-1/2 -translate-x-1/2'
                    : 'right-full top-1/2 -translate-y-1/2 translate-x-1/2'
            }`}
          />
        </div>
      )}
    </div>
  );
}

export default function DocumentsPage() {
  const { hasPermission, userContext } = useRole();
  const { selectedUser } = useUserContextOptional();
  const linkedAccountContext = useLinkedAccountContextOptional();

  // Determine if user can see all documents (Welon Trust) or just their own
  const canViewAllDocuments =
    hasPermission('access_emergency_documents') ||
    userContext?.displayName?.includes('Welon') ||
    userContext?.displayName === 'Welon Trust' ||
    userContext?.role === 'welon_trust';

  // Check linked account permissions
  const canViewDocuments = linkedAccountContext?.canView('documents') ?? true;
  const canEditDocuments = linkedAccountContext?.canEdit('documents') ?? true;

  // Test state for forcing Welon Trust view
  const [forceWelonView, setForceWelonView] = useState(false);
  const effectiveCanViewAll = canViewAllDocuments || forceWelonView;

  // Debug logging
  console.log('Documents Page Debug:', {
    hasPermission: hasPermission('access_emergency_documents'),
    userContext: userContext,
    roleName: userContext?.displayName,
    includesWelon: userContext?.displayName?.includes('Welon'),
    canViewAllDocuments,
    selectedUser: selectedUser,
    effectiveCanViewAll: effectiveCanViewAll,
  });

  // Initialize documents on first load
  React.useEffect(() => {
    let initialDocs: Document[];
    if (effectiveCanViewAll) {
      if (selectedUser) {
        initialDocs = generateDocuments(false, selectedUser.id);
      } else {
        initialDocs = generateDocuments(true);
      }
    } else {
      const currentUserId = selectedUser?.id || 'user-1';
      initialDocs = generateDocuments(false, currentUserId);
    }
    setDocuments(initialDocs);
  }, [effectiveCanViewAll, selectedUser]);

  // State for filtering and sorting
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [userFilter, setUserFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('lastModified');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Pagination state for different tabs
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewCurrentPage, setReviewCurrentPage] = useState(1);
  const [signCurrentPage, setSignCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const reviewItemsPerPage = 5; // Smaller for card layout
  const signItemsPerPage = 5; // Smaller for card layout

  // Document management state
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);

  // Tab state
  const [activeTab, setActiveTab] = useState('manage');

  // Filters & Search collapsible state
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);

  // Filtered and sorted documents (without pagination)
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents.filter(doc => {
      const matchesSearch =
        doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = typeFilter === 'all' || doc.type === typeFilter;
      const matchesStatus =
        statusFilter === 'all' || doc.status === statusFilter;
      const matchesUser = userFilter === 'all' || doc.userId === userFilter;

      return matchesSearch && matchesType && matchesStatus && matchesUser;
    });

    // Sort documents
    filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'dateCreated' || sortField === 'lastModified') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [
    documents,
    searchTerm,
    typeFilter,
    statusFilter,
    userFilter,
    sortField,
    sortDirection,
  ]);

  // Pagination calculations
  const totalPages = Math.ceil(
    filteredAndSortedDocuments.length / itemsPerPage
  );
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedDocuments = filteredAndSortedDocuments.slice(
    startIndex,
    endIndex
  );

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, typeFilter, statusFilter, userFilter]);

  // Reset pagination when switching tabs
  React.useEffect(() => {
    setCurrentPage(1);
    setReviewCurrentPage(1);
    setSignCurrentPage(1);
  }, [activeTab]);

  // Generate visible page numbers for pagination
  const getVisiblePages = (current: number, total: number) => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (total <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (current > 3) {
        pages.push('...');
      }

      // Show pages around current page
      const start = Math.max(2, current - 1);
      const end = Math.min(total - 1, current + 1);

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== total) {
          pages.push(i);
        }
      }

      if (current < total - 2) {
        pages.push('...');
      }

      // Always show last page
      if (total > 1) {
        pages.push(total);
      }
    }

    return pages;
  };

  // Memoize review documents to avoid regeneration on every render
  const reviewDocuments = useMemo(() => generateReviewDocuments(), []);
  const reviewTotalPages = Math.ceil(
    reviewDocuments.length / reviewItemsPerPage
  );
  const reviewStartIndex = (reviewCurrentPage - 1) * reviewItemsPerPage;
  const reviewEndIndex = reviewStartIndex + reviewItemsPerPage;
  const paginatedReviewDocuments = reviewDocuments.slice(
    reviewStartIndex,
    reviewEndIndex
  );

  // Memoize sign documents to avoid regeneration on every render
  const signDocuments = useMemo(() => generateSigningDocuments(), []);
  const signTotalPages = Math.ceil(signDocuments.length / signItemsPerPage);
  const signStartIndex = (signCurrentPage - 1) * signItemsPerPage;
  const signEndIndex = signStartIndex + signItemsPerPage;
  const paginatedSignDocuments = signDocuments.slice(
    signStartIndex,
    signEndIndex
  );

  // Document counts for tab indicators
  const manageDocumentsCount = filteredAndSortedDocuments.length;
  const reviewDocumentsCount = reviewDocuments.length;
  const signDocumentsCount = signDocuments.length;

  // Status indicators for tabs
  const hasReviewDocuments = reviewDocumentsCount > 0;
  const hasSignDocuments = signDocumentsCount > 0;

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return (
        <Tooltip content={`Click to sort by ${field}`}>
          <ArrowUpDown className='h-4 w-4 cursor-help' />
        </Tooltip>
      );
    }
    return sortDirection === 'asc' ? (
      <Tooltip content={`Sorted ascending - click to sort descending`}>
        <ArrowUp className='h-4 w-4 cursor-help' />
      </Tooltip>
    ) : (
      <Tooltip content={`Sorted descending - click to sort ascending`}>
        <ArrowDown className='h-4 w-4 cursor-help' />
      </Tooltip>
    );
  };

  const getStatusBadge = (status: Document['status']) => {
    const variants = {
      draft: 'outline',
      awaiting_verification: 'secondary',
      verified: 'default',
      active: 'default',
      archived: 'secondary',
      pending_review: 'outline',
      expired: 'destructive',
    } as const;

    const labels = {
      draft: 'Draft',
      awaiting_verification: 'Awaiting Verification',
      verified: 'Verified',
      active: 'Active',
      archived: 'Archived',
      pending_review: 'Pending Review',
      expired: 'Expired',
    };

    const tooltips = {
      draft:
        'Document is in draft state and can be edited. Use Verify or Auto-Verify to proceed.',
      awaiting_verification:
        'Document is awaiting verification. You can still edit it before final submission.',
      verified:
        'Document has been verified and is ready to be submitted to Welon Trust.',
      active: 'Document is active and in use. No further action required.',
      archived: 'Document has been archived and is no longer active.',
      pending_review:
        'Document has been submitted to Welon Trust and is under review.',
      expired: 'Document has expired and may need to be updated or renewed.',
    };

    return (
      <Tooltip content={tooltips[status]}>
        <Badge variant={variants[status]} className='cursor-help'>
          {labels[status]}
        </Badge>
      </Tooltip>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePreview = (document: Document) => {
    // In real implementation, this would open document viewer
    alert(`Preview: ${document.title}`);
  };

  const handleDownload = (doc: Document) => {
    // In real implementation, this would download the document
    const element = document.createElement('a');
    const file = new Blob(
      [
        `Document: ${doc.title}\nType: ${doc.type}\nVersion: ${doc.version}\n\nThis is a mock document for demonstration purposes.`,
      ],
      { type: 'text/plain' }
    );
    element.href = URL.createObjectURL(file);
    element.download = `${doc.title.replace(/\s+/g, '_')}_v${doc.version}.txt`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const handleEdit = (document: Document) => {
    // In real implementation, this would open document editor
    alert(
      `Edit: ${document.title}\n\nThis document is editable because it's in ${document.status} status.`
    );
  };

  const handleVerify = async (document: Document) => {
    setIsProcessing(document.id);

    try {
      // Simulate verification process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update document status to awaiting_verification
      setDocuments(prevDocs =>
        prevDocs.map(doc =>
          doc.id === document.id
            ? {
                ...doc,
                status: 'awaiting_verification' as Document['status'],
                lastModified: new Date().toISOString(),
                canEdit: true, // Still editable in awaiting_verification
              }
            : doc
        )
      );

      alert(
        `Verification initiated for: ${document.title}\n\nDocument is now awaiting verification. You can still edit it before final submission.`
      );
    } catch (error) {
      alert('Verification failed. Please try again.');
    } finally {
      setIsProcessing(null);
    }
  };

  const handleSubmitToTrust = async (document: Document) => {
    setIsProcessing(document.id);

    try {
      // Simulate submission process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update document status to pending_review
      setDocuments(prevDocs =>
        prevDocs.map(doc =>
          doc.id === document.id
            ? {
                ...doc,
                status: 'pending_review' as Document['status'],
                lastModified: new Date().toISOString(),
                canEdit: false, // No longer editable once submitted
              }
            : doc
        )
      );

      alert(
        `Submitted to Trust: ${document.title}\n\nDocument submitted to Welon Trust for review. You will receive a notification when review is complete.`
      );
    } catch (error) {
      alert('Submission failed. Please try again.');
    } finally {
      setIsProcessing(null);
    }
  };

  // Auto-verification toggle function
  const handleAutoVerify = async (document: Document) => {
    setIsProcessing(document.id);

    try {
      // Simulate auto-verification process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update document status directly to verified
      setDocuments(prevDocs =>
        prevDocs.map(doc =>
          doc.id === document.id
            ? {
                ...doc,
                status: 'verified' as Document['status'],
                lastModified: new Date().toISOString(),
                isVerified: true,
                verificationDate: new Date().toISOString(),
                canEdit: false, // No longer editable once verified
              }
            : doc
        )
      );

      alert(
        `Auto-verified: ${document.title}\n\nDocument has been automatically verified and is ready for submission to Trust.`
      );
    } catch (error) {
      alert('Auto-verification failed. Please try again.');
    } finally {
      setIsProcessing(null);
    }
  };

  // Handle document review
  const handleReview = async (document: Document) => {
    setIsProcessing(document.id);

    try {
      // Simulate review process
      await new Promise(resolve => setTimeout(resolve, 1500));

      alert(
        `Review completed: ${document.title}\n\nDocument has been reviewed and acknowledged.`
      );
    } catch (error) {
      alert('Review failed. Please try again.');
    } finally {
      setIsProcessing(null);
    }
  };

  // Handle document signing
  const handleSign = async (document: Document) => {
    setIsProcessing(document.id);

    try {
      // Simulate signing process
      await new Promise(resolve => setTimeout(resolve, 2000));

      alert(
        `Document signed: ${document.title}\n\nDocument has been digitally signed and is now active.`
      );
    } catch (error) {
      alert('Signing failed. Please try again.');
    } finally {
      setIsProcessing(null);
    }
  };

  // Get unique users for filter dropdown
  const uniqueUsers = useMemo(() => {
    const users = Array.from(
      new Set(documents.map(doc => ({ id: doc.userId, name: doc.userName })))
    ).sort((a, b) => a.name.localeCompare(b.name));
    return users;
  }, [documents]);

  // Remove the no access check since Members should always see their documents
  // if (!canViewAllDocuments && !selectedUser) {
  //   return (
  //     <div className="container mx-auto py-8 px-4">
  //       <div className="max-w-4xl mx-auto">
  //         <Alert className="bg-amber-50 text-amber-800 border-amber-200">
  //           <User className="h-4 w-4" />
  //           <AlertTitle>No Access</AlertTitle>
  //           <AlertDescription>
  //             Please select a member from the dropdown to view documents, or contact your administrator for access permissions.
  //           </AlertDescription>
  //         </Alert>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <div className='mb-8'>
          <Headline className='mb-2'>Document Management</Headline>
          <Subhead className='text-muted-foreground'>
            {effectiveCanViewAll
              ? selectedUser
                ? `Manage documents for ${selectedUser.name}`
                : 'Manage and view all member documents'
              : 'Manage your personal documents'}
          </Subhead>
        </div>

        {/* Linked Account Access Notice */}
        {linkedAccountContext?.activeAccount?.mode === 'linked' && (
          <Alert className='mb-6 border-blue-200 bg-blue-50'>
            <Shield className='h-4 w-4 text-blue-600' />
            <AlertTitle className='text-blue-800'>
              Viewing Linked Account:{' '}
              {linkedAccountContext.activeAccount.user.name}
            </AlertTitle>
            <AlertDescription className='text-blue-700'>
              You are viewing documents for a linked account.
              {!canEditDocuments &&
                ' You have read-only access to these documents.'}
              {canEditDocuments &&
                ' You have edit permissions for these documents.'}
            </AlertDescription>
          </Alert>
        )}

        {/* Document Tabs */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className='space-y-6'
        >
          <div className='grid w-full grid-cols-3 gap-4 mb-6'>
            {/* Manage Documents Tab */}
            <div
              onClick={() => setActiveTab('manage')}
              className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                activeTab === 'manage'
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 bg-background hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className='flex items-center gap-3 mb-2'>
                <div
                  className={`p-2 rounded-lg ${
                    activeTab === 'manage' ? 'bg-blue-100' : 'bg-gray-100'
                  }`}
                >
                  <Tooltip content='Manage your personal documents - create, edit, verify and submit'>
                    <FileText
                      className={`h-5 w-5 cursor-help ${
                        activeTab === 'manage'
                          ? 'text-blue-600'
                          : 'text-[var(--custom-gray-medium)]'
                      }`}
                    />
                  </Tooltip>
                </div>
                <div className='flex-1'>
                  <div className='flex items-center gap-2'>
                    <h3
                      className={`font-semibold ${
                        activeTab === 'manage'
                          ? 'text-blue-900'
                          : 'text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      Manage Documents
                    </h3>
                    <Badge
                      variant='secondary'
                      className={`text-xs ${
                        activeTab === 'manage'
                          ? 'bg-blue-200 text-blue-800'
                          : 'bg-gray-200 text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      {manageDocumentsCount}
                    </Badge>
                  </div>
                  {activeTab === 'manage' && (
                    <Badge
                      variant='outline'
                      className='text-xs bg-blue-100 text-blue-700 border-blue-300 mt-1'
                    >
                      Active
                    </Badge>
                  )}
                </div>
              </div>
              <p
                className={`text-sm ${
                  activeTab === 'manage'
                    ? 'text-blue-700'
                    : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                Upload, review and organize your important estate planning
                documents.
              </p>
            </div>

            {/* Review Documents Tab */}
            <div
              onClick={() => setActiveTab('review')}
              className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                activeTab === 'review'
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 bg-background hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className='flex items-center gap-3 mb-2'>
                <div
                  className={`p-2 rounded-lg ${
                    activeTab === 'review' ? 'bg-orange-100' : 'bg-gray-100'
                  }`}
                >
                  <Tooltip content='Review documents that require your acknowledgment'>
                    <BookOpen
                      className={`h-5 w-5 cursor-help ${
                        activeTab === 'review'
                          ? 'text-orange-600'
                          : 'text-[var(--custom-gray-medium)]'
                      }`}
                    />
                  </Tooltip>
                </div>
                <div className='flex-1'>
                  <div className='flex items-center gap-2'>
                    <h3
                      className={`font-semibold ${
                        activeTab === 'review'
                          ? 'text-orange-900'
                          : 'text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      Review Documents
                    </h3>
                    <Badge
                      variant='secondary'
                      className={`text-xs ${
                        activeTab === 'review'
                          ? 'bg-orange-200 text-orange-800'
                          : 'bg-gray-200 text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      {reviewDocumentsCount}
                    </Badge>
                    {hasReviewDocuments && activeTab !== 'review' && (
                      <div
                        className='w-2 h-2 bg-orange-500 rounded-full animate-pulse'
                        title='New documents to review'
                      />
                    )}
                  </div>
                  {activeTab === 'review' && (
                    <Badge
                      variant='outline'
                      className='text-xs bg-orange-100 text-orange-700 border-orange-300 mt-1'
                    >
                      Active
                    </Badge>
                  )}
                </div>
              </div>
              <p
                className={`text-sm ${
                  activeTab === 'review'
                    ? 'text-orange-700'
                    : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                Review documents requiring your acknowledgment and approval
                before finalization.
              </p>
            </div>

            {/* Sign Documents Tab */}
            <div
              onClick={() => setActiveTab('sign')}
              className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                activeTab === 'sign'
                  ? 'border-green-500 bg-green-50 shadow-md'
                  : 'border-gray-200 bg-background hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className='flex items-center gap-3 mb-2'>
                <div
                  className={`p-2 rounded-lg ${
                    activeTab === 'sign' ? 'bg-green-100' : 'bg-gray-100'
                  }`}
                >
                  <Tooltip content='Digitally sign documents that are ready for execution'>
                    <PenTool
                      className={`h-5 w-5 cursor-help ${
                        activeTab === 'sign'
                          ? 'text-green-600'
                          : 'text-[var(--custom-gray-medium)]'
                      }`}
                    />
                  </Tooltip>
                </div>
                <div className='flex-1'>
                  <div className='flex items-center gap-2'>
                    <h3
                      className={`font-semibold ${
                        activeTab === 'sign'
                          ? 'text-green-900'
                          : 'text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      Sign Documents
                    </h3>
                    <Badge
                      variant='secondary'
                      className={`text-xs ${
                        activeTab === 'sign'
                          ? 'bg-green-200 text-green-800'
                          : 'bg-gray-200 text-[var(--custom-gray-dark)]'
                      }`}
                    >
                      {signDocumentsCount}
                    </Badge>
                    {hasSignDocuments && activeTab !== 'sign' && (
                      <div
                        className='w-2 h-2 bg-green-500 rounded-full animate-pulse'
                        title='Documents ready for signature'
                      />
                    )}
                  </div>
                  {activeTab === 'sign' && (
                    <Badge
                      variant='outline'
                      className='text-xs bg-green-100 text-green-700 border-green-300 mt-1'
                    >
                      Active
                    </Badge>
                  )}
                </div>
              </div>
              <p
                className={`text-sm ${
                  activeTab === 'sign'
                    ? 'text-green-700'
                    : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                Digitally sign verified documents to complete your estate
                planning process.
              </p>
            </div>
          </div>

          {/* Manage Documents Tab */}
          <TabsContent value='manage' className='space-y-6'>
            {/* Filters and Search */}
            <Card>
              <CardHeader className='pb-3'>
                <div className='flex items-center justify-between'>
                  <CardTitle className='flex items-center gap-2'>
                    <Tooltip content='Filter and search through your documents'>
                      <Filter className='h-5 w-5 cursor-help' />
                    </Tooltip>
                    Filters & Search
                  </CardTitle>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
                    className='flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground'
                  >
                    {isFiltersExpanded ? (
                      <>
                        <ChevronUp className='h-4 w-4' />
                        Hide Filters
                      </>
                    ) : (
                      <>
                        <ChevronDown className='h-4 w-4' />
                        Show Filters
                      </>
                    )}
                  </Button>
                </div>
                {!isFiltersExpanded && (
                  <div className='mt-4'>
                    <div className='flex items-center gap-4'>
                      <div className='flex-1'>
                        <div className='relative'>
                          <Tooltip content='Quick search documents by title or description'>
                            <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground cursor-help' />
                          </Tooltip>
                          <Input
                            placeholder='Quick search documents...'
                            value={searchTerm}
                            onChange={e => setSearchTerm(e.target.value)}
                            className='pl-10'
                          />
                        </div>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        Need more options? Use "Show Filters" for advanced
                        search.
                      </p>
                    </div>
                  </div>
                )}
              </CardHeader>
              {isFiltersExpanded && (
                <CardContent>
                  <div
                    className={`grid gap-4 ${
                      effectiveCanViewAll && !selectedUser
                        ? 'md:grid-cols-2 lg:grid-cols-5'
                        : 'md:grid-cols-2 lg:grid-cols-4'
                    }`}
                  >
                    {/* Search */}
                    <div className='md:col-span-2'>
                      <Label htmlFor='search'>Search Documents</Label>
                      <div className='relative'>
                        <Tooltip content='Search documents by title or description'>
                          <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground cursor-help' />
                        </Tooltip>
                        <Input
                          id='search'
                          placeholder='Search by title or description...'
                          value={searchTerm}
                          onChange={e => setSearchTerm(e.target.value)}
                          className='pl-10'
                        />
                      </div>
                    </div>

                    {/* Type Filter */}
                    <div>
                      <Label htmlFor='type-filter'>Document Type</Label>
                      <Select value={typeFilter} onValueChange={setTypeFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder='All Types' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Types</SelectItem>
                          <SelectItem value='Legal'>Legal</SelectItem>
                          <SelectItem value='Medical'>Medical</SelectItem>
                          <SelectItem value='Personal'>Personal</SelectItem>
                          <SelectItem value='Financial'>Financial</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Status Filter */}
                    <div>
                      <Label htmlFor='status-filter'>Status</Label>
                      <Select
                        value={statusFilter}
                        onValueChange={setStatusFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='All Statuses' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Statuses</SelectItem>
                          <SelectItem value='draft'>Draft</SelectItem>
                          <SelectItem value='awaiting_verification'>
                            Awaiting Verification
                          </SelectItem>
                          <SelectItem value='verified'>Verified</SelectItem>
                          <SelectItem value='active'>Active</SelectItem>
                          <SelectItem value='archived'>Archived</SelectItem>
                          <SelectItem value='pending_review'>
                            Pending Review
                          </SelectItem>
                          <SelectItem value='expired'>Expired</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* User Filter (only for Welon Trust when no user selected) */}
                    {effectiveCanViewAll && !selectedUser && (
                      <div>
                        <Label htmlFor='user-filter'>Member</Label>
                        <Select
                          value={userFilter}
                          onValueChange={setUserFilter}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='All Members' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='all'>All Members</SelectItem>
                            {uniqueUsers.map(user => (
                              <SelectItem key={user.id} value={user.id}>
                                {user.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Documents Table */}
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <CardTitle>
                    Your Estate Planning Document Library (
                    {filteredAndSortedDocuments.length})
                    {totalPages > 1 && (
                      <span className='text-sm font-normal text-muted-foreground ml-2'>
                        Showing {startIndex + 1}-
                        {Math.min(endIndex, filteredAndSortedDocuments.length)}{' '}
                        of {filteredAndSortedDocuments.length}
                      </span>
                    )}
                  </CardTitle>
                  <div className='flex items-center gap-2'>
                    {effectiveCanViewAll && (
                      <Badge
                        variant='outline'
                        className='flex items-center gap-1'
                      >
                        <Tooltip content='You are viewing documents with administrative privileges'>
                          <Shield className='h-3 w-3 cursor-help' />
                        </Tooltip>
                        {selectedUser
                          ? `${selectedUser.name}'s Documents`
                          : 'Welon Trust View'}
                      </Badge>
                    )}
                    {/* Document View Selector */}
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant='outline'
                          size='sm'
                          className='flex items-center gap-2'
                        >
                          {forceWelonView ? (
                            <>
                              <Tooltip content='Currently viewing documents with Welon Trust administrative privileges'>
                                <Shield className='h-4 w-4 text-blue-600 cursor-help' />
                              </Tooltip>
                              Welon Trust View
                            </>
                          ) : (
                            <>
                              <Tooltip content='Currently viewing your personal documents'>
                                <User className='h-4 w-4 text-green-600 cursor-help' />
                              </Tooltip>
                              My Documents
                            </>
                          )}
                          <ChevronDown className='h-4 w-4' />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent align='end' className='w-80 p-0'>
                        <div className='space-y-1'>
                          <button
                            onClick={() => setForceWelonView(false)}
                            className={`w-full flex items-start gap-3 p-4 text-left hover:bg-gray-50 transition-colors ${
                              !forceWelonView
                                ? 'bg-green-50 border-l-4 border-l-green-500'
                                : 'border-l-4 border-l-transparent'
                            }`}
                          >
                            <User
                              className={`h-5 w-5 mt-0.5 ${!forceWelonView ? 'text-green-600' : 'text-[var(--custom-gray-medium)]'}`}
                            />
                            <div className='flex-1'>
                              <div className='flex items-center gap-2'>
                                <span className='font-medium'>
                                  My Documents
                                </span>
                                {!forceWelonView && (
                                  <Badge
                                    variant='outline'
                                    className='text-xs bg-green-100 text-green-700 border-green-300'
                                  >
                                    Active
                                  </Badge>
                                )}
                              </div>
                              <p className='text-sm text-muted-foreground mt-1'>
                                View and manage your personal documents, create
                                new documents, and handle your own document
                                workflow.
                              </p>
                            </div>
                          </button>

                          <div className='border-t border-gray-200' />

                          <button
                            onClick={() => setForceWelonView(true)}
                            className={`w-full flex items-start gap-3 p-4 text-left hover:bg-gray-50 transition-colors ${
                              forceWelonView
                                ? 'bg-blue-50 border-l-4 border-l-blue-500'
                                : 'border-l-4 border-l-transparent'
                            }`}
                          >
                            <Shield
                              className={`h-5 w-5 mt-0.5 ${forceWelonView ? 'text-blue-600' : 'text-[var(--custom-gray-medium)]'}`}
                            />
                            <div className='flex-1'>
                              <div className='flex items-center gap-2'>
                                <span className='font-medium'>
                                  Welon Trust View
                                </span>
                                {forceWelonView && (
                                  <Badge
                                    variant='outline'
                                    className='text-xs bg-blue-100 text-blue-700 border-blue-300'
                                  >
                                    Active
                                  </Badge>
                                )}
                              </div>
                              <p className='text-sm text-muted-foreground mt-1'>
                                Switch to view and manage documents on behalf of
                                members you support via Welon Trust.
                                Administrative access required.
                              </p>
                            </div>
                          </button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {filteredAndSortedDocuments.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    <FileText className='h-12 w-12 mx-auto mb-4 opacity-50' />
                    <p className='text-lg font-medium mb-2'>
                      No documents found
                    </p>
                    <p>Try adjusting your search criteria or filters.</p>
                  </div>
                ) : (
                  <>
                    <div className='overflow-x-auto'>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('title')}
                            >
                              <div className='flex items-center gap-2'>
                                Document Title
                                {getSortIcon('title')}
                              </div>
                            </TableHead>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('type')}
                            >
                              <div className='flex items-center gap-2'>
                                Type
                                {getSortIcon('type')}
                              </div>
                            </TableHead>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('status')}
                            >
                              <div className='flex items-center gap-2'>
                                Status
                                {getSortIcon('status')}
                              </div>
                            </TableHead>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('dateCreated')}
                            >
                              <div className='flex items-center gap-2'>
                                Date Created
                                {getSortIcon('dateCreated')}
                              </div>
                            </TableHead>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('lastModified')}
                            >
                              <div className='flex items-center gap-2'>
                                Last Modified
                                {getSortIcon('lastModified')}
                              </div>
                            </TableHead>
                            <TableHead
                              className='cursor-pointer hover:bg-muted/50'
                              onClick={() => handleSort('version')}
                            >
                              <div className='flex items-center gap-2'>
                                Version
                                {getSortIcon('version')}
                              </div>
                            </TableHead>
                            {effectiveCanViewAll && !selectedUser && (
                              <TableHead
                                className='cursor-pointer hover:bg-muted/50'
                                onClick={() => handleSort('userName')}
                              >
                                <div className='flex items-center gap-2'>
                                  Member
                                  {getSortIcon('userName')}
                                </div>
                              </TableHead>
                            )}
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {paginatedDocuments.map(document => (
                            <TableRow
                              key={document.id}
                              className='hover:bg-muted/50'
                            >
                              <TableCell className='font-medium'>
                                <div>
                                  <p className='font-medium'>
                                    {document.title}
                                  </p>
                                  <p className='text-xs text-muted-foreground'>
                                    {document.fileSize}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant='outline'>{document.type}</Badge>
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(document.status)}
                              </TableCell>
                              <TableCell className='text-sm'>
                                {formatDate(document.dateCreated)}
                              </TableCell>
                              <TableCell className='text-sm'>
                                {formatDate(document.lastModified)}
                              </TableCell>
                              <TableCell className='font-mono text-sm'>
                                v{document.version}
                              </TableCell>
                              {effectiveCanViewAll && !selectedUser && (
                                <TableCell className='text-sm'>
                                  {document.userName}
                                </TableCell>
                              )}
                              <TableCell>
                                <div className='flex items-center gap-1 flex-wrap'>
                                  {/* Always show Preview and Download */}
                                  <Tooltip content='Preview document content'>
                                    <Button
                                      variant='outline'
                                      size='sm'
                                      onClick={() => handlePreview(document)}
                                    >
                                      <Eye className='h-4 w-4' />
                                    </Button>
                                  </Tooltip>
                                  <Tooltip content='Download document file'>
                                    <Button
                                      variant='outline'
                                      size='sm'
                                      onClick={() => handleDownload(document)}
                                    >
                                      <Download className='h-4 w-4' />
                                    </Button>
                                  </Tooltip>

                                  {/* Edit button for editable documents */}
                                  {document.canEdit && canEditDocuments && (
                                    <Tooltip content='Edit document content and details'>
                                      <Button
                                        variant='outline'
                                        size='sm'
                                        onClick={() => handleEdit(document)}
                                        className='text-blue-600 hover:text-blue-700'
                                      >
                                        <Edit className='h-4 w-4' />
                                      </Button>
                                    </Tooltip>
                                  )}

                                  {/* Verify button for draft documents */}
                                  {document.status === 'draft' &&
                                    canEditDocuments && (
                                      <>
                                        <Tooltip content='Start manual verification process - document will be awaiting verification and remain editable'>
                                          <Button
                                            variant='outline'
                                            size='sm'
                                            onClick={() =>
                                              handleVerify(document)
                                            }
                                            className='text-green-600 hover:text-green-700'
                                            disabled={
                                              isProcessing === document.id
                                            }
                                          >
                                            {isProcessing === document.id ? (
                                              <Clock className='h-4 w-4 animate-spin' />
                                            ) : (
                                              <CheckCircle className='h-4 w-4' />
                                            )}
                                          </Button>
                                        </Tooltip>
                                        <Tooltip content='Automatically verify document - document will be immediately verified and ready for submission'>
                                          <Button
                                            variant='outline'
                                            size='sm'
                                            onClick={() =>
                                              handleAutoVerify(document)
                                            }
                                            className='text-blue-600 hover:text-blue-700'
                                            disabled={
                                              isProcessing === document.id
                                            }
                                          >
                                            {isProcessing === document.id ? (
                                              <Clock className='h-4 w-4 animate-spin' />
                                            ) : (
                                              <CheckCircle className='h-4 w-4' />
                                            )}
                                          </Button>
                                        </Tooltip>
                                      </>
                                    )}

                                  {/* Submit to Trust button for verified documents */}
                                  {document.status === 'verified' &&
                                    canEditDocuments && (
                                      <Tooltip content='Submit verified document to Welon Trust for final review and processing'>
                                        <Button
                                          variant='outline'
                                          size='sm'
                                          onClick={() =>
                                            handleSubmitToTrust(document)
                                          }
                                          className='text-purple-600 hover:text-purple-700'
                                          disabled={
                                            isProcessing === document.id
                                          }
                                        >
                                          {isProcessing === document.id ? (
                                            <Clock className='h-4 w-4 animate-spin' />
                                          ) : (
                                            <Send className='h-4 w-4' />
                                          )}
                                        </Button>
                                      </Tooltip>
                                    )}

                                  {/* Status indicator for awaiting verification */}
                                  {document.status ===
                                    'awaiting_verification' && (
                                    <Tooltip content='Document is awaiting verification. You can still edit it before final submission.'>
                                      <div className='flex items-center text-amber-600 cursor-help'>
                                        <Clock className='h-4 w-4' />
                                      </div>
                                    </Tooltip>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <div className='mt-6 flex justify-center'>
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <Tooltip content='Previous page'>
                                <PaginationPrevious
                                  onClick={() =>
                                    setCurrentPage(Math.max(1, currentPage - 1))
                                  }
                                  className={
                                    currentPage === 1
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>

                            {getVisiblePages(currentPage, totalPages).map(
                              (page, index) => (
                                <PaginationItem key={index}>
                                  {page === '...' ? (
                                    <PaginationEllipsis />
                                  ) : (
                                    <Tooltip content={`Go to page ${page}`}>
                                      <PaginationLink
                                        onClick={() =>
                                          setCurrentPage(page as number)
                                        }
                                        isActive={currentPage === page}
                                        className='cursor-pointer'
                                      >
                                        {page}
                                      </PaginationLink>
                                    </Tooltip>
                                  )}
                                </PaginationItem>
                              )
                            )}

                            <PaginationItem>
                              <Tooltip content='Next page'>
                                <PaginationNext
                                  onClick={() =>
                                    setCurrentPage(
                                      Math.min(totalPages, currentPage + 1)
                                    )
                                  }
                                  className={
                                    currentPage === totalPages
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Review Documents Tab */}
          <TabsContent value='review' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Tooltip content='Documents that need your review and acknowledgment'>
                    <BookOpen className='h-5 w-5 cursor-help' />
                  </Tooltip>
                  Estate Planning Documents Awaiting Your Review
                </CardTitle>
                <CardDescription>
                  Important documents requiring your careful review and
                  acknowledgment before proceeding to the next step
                </CardDescription>
              </CardHeader>
              <CardContent>
                {reviewDocuments.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    <Tooltip content='No documents require review at this time'>
                      <BookOpen className='h-12 w-12 mx-auto mb-4 opacity-50 cursor-help' />
                    </Tooltip>
                    <p className='text-lg font-medium mb-2'>
                      No documents to review
                    </p>
                    <p>All documents have been reviewed and acknowledged.</p>
                  </div>
                ) : (
                  <>
                    <div className='flex items-center justify-between mb-4'>
                      <p className='text-sm text-muted-foreground'>
                        Showing {reviewStartIndex + 1}-
                        {Math.min(reviewEndIndex, reviewDocuments.length)} of{' '}
                        {reviewDocuments.length} documents
                      </p>
                      <Badge
                        variant='outline'
                        className='flex items-center gap-1'
                      >
                        <Tooltip content='Documents requiring your review and acknowledgment'>
                          <BookOpen className='h-3 w-3 cursor-help' />
                        </Tooltip>
                        Review Queue
                      </Badge>
                    </div>
                    <div className='space-y-4'>
                      {paginatedReviewDocuments.map(document => (
                        <div
                          key={document.id}
                          className='border rounded-lg p-4 hover:bg-gray-50'
                        >
                          <div className='flex items-center justify-between'>
                            <div className='flex-1'>
                              <div className='flex items-center gap-3 mb-2'>
                                <h3 className='font-medium'>
                                  {document.title}
                                </h3>
                                <Badge variant='outline'>{document.type}</Badge>
                                {getStatusBadge(document.status)}
                              </div>
                              <p className='text-sm text-muted-foreground mb-2'>
                                {document.description}
                              </p>
                              <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                                <span>Version: {document.version}</span>
                                <span>Size: {document.fileSize}</span>
                                <span>
                                  Modified: {formatDate(document.lastModified)}
                                </span>
                              </div>
                            </div>
                            <div className='flex items-center gap-2'>
                              <Tooltip content='Preview document content'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => handlePreview(document)}
                                >
                                  <Eye className='h-4 w-4' />
                                </Button>
                              </Tooltip>
                              <Tooltip content='Download document file'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => handleDownload(document)}
                                >
                                  <Download className='h-4 w-4' />
                                </Button>
                              </Tooltip>
                              <Tooltip content='Review and acknowledge this document'>
                                <Button
                                  variant='default'
                                  size='sm'
                                  onClick={() => handleReview(document)}
                                  disabled={isProcessing === document.id}
                                  className='bg-blue-600 hover:bg-blue-700'
                                >
                                  {isProcessing === document.id ? (
                                    <Clock className='h-4 w-4 animate-spin' />
                                  ) : (
                                    <CheckCircle className='h-4 w-4' />
                                  )}
                                  Review
                                </Button>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Review Pagination */}
                    {reviewTotalPages > 1 && (
                      <div className='mt-6 flex justify-center'>
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <Tooltip content='Previous page'>
                                <PaginationPrevious
                                  onClick={() =>
                                    setReviewCurrentPage(
                                      Math.max(1, reviewCurrentPage - 1)
                                    )
                                  }
                                  className={
                                    reviewCurrentPage === 1
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>

                            {getVisiblePages(
                              reviewCurrentPage,
                              reviewTotalPages
                            ).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === '...' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <Tooltip content={`Go to page ${page}`}>
                                    <PaginationLink
                                      onClick={() =>
                                        setReviewCurrentPage(page as number)
                                      }
                                      isActive={reviewCurrentPage === page}
                                      className='cursor-pointer'
                                    >
                                      {page}
                                    </PaginationLink>
                                  </Tooltip>
                                )}
                              </PaginationItem>
                            ))}

                            <PaginationItem>
                              <Tooltip content='Next page'>
                                <PaginationNext
                                  onClick={() =>
                                    setReviewCurrentPage(
                                      Math.min(
                                        reviewTotalPages,
                                        reviewCurrentPage + 1
                                      )
                                    )
                                  }
                                  className={
                                    reviewCurrentPage === reviewTotalPages
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Sign Documents Tab */}
          <TabsContent value='sign' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Tooltip content='Documents that are ready for your digital signature'>
                    <PenTool className='h-5 w-5 cursor-help' />
                  </Tooltip>
                  Finalize Your Estate Planning with Digital Signatures
                </CardTitle>
                <CardDescription>
                  Complete your estate planning process by digitally signing
                  verified documents that are ready for execution
                </CardDescription>
              </CardHeader>
              <CardContent>
                {signDocuments.length === 0 ? (
                  <div className='text-center py-8 text-muted-foreground'>
                    <Tooltip content='No documents are ready for signature at this time'>
                      <PenTool className='h-12 w-12 mx-auto mb-4 opacity-50 cursor-help' />
                    </Tooltip>
                    <p className='text-lg font-medium mb-2'>
                      No documents to sign
                    </p>
                    <p>
                      All documents have been signed or are not yet ready for
                      signature.
                    </p>
                  </div>
                ) : (
                  <>
                    <div className='flex items-center justify-between mb-4'>
                      <p className='text-sm text-muted-foreground'>
                        Showing {signStartIndex + 1}-
                        {Math.min(signEndIndex, signDocuments.length)} of{' '}
                        {signDocuments.length} documents
                      </p>
                      <Badge
                        variant='outline'
                        className='flex items-center gap-1'
                      >
                        <Tooltip content='Documents ready for your digital signature'>
                          <PenTool className='h-3 w-3 cursor-help' />
                        </Tooltip>
                        Signature Queue
                      </Badge>
                    </div>
                    <div className='space-y-4'>
                      {paginatedSignDocuments.map(document => (
                        <div
                          key={document.id}
                          className='border rounded-lg p-4 hover:bg-gray-50'
                        >
                          <div className='flex items-center justify-between'>
                            <div className='flex-1'>
                              <div className='flex items-center gap-3 mb-2'>
                                <h3 className='font-medium'>
                                  {document.title}
                                </h3>
                                <Badge variant='outline'>{document.type}</Badge>
                                {getStatusBadge(document.status)}
                              </div>
                              <p className='text-sm text-muted-foreground mb-2'>
                                {document.description}
                              </p>
                              <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                                <span>Version: {document.version}</span>
                                <span>Size: {document.fileSize}</span>
                                <span>
                                  Modified: {formatDate(document.lastModified)}
                                </span>
                              </div>
                            </div>
                            <div className='flex items-center gap-2'>
                              <Tooltip content='Preview document content'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => handlePreview(document)}
                                >
                                  <Eye className='h-4 w-4' />
                                </Button>
                              </Tooltip>
                              <Tooltip content='Download document file'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => handleDownload(document)}
                                >
                                  <Download className='h-4 w-4' />
                                </Button>
                              </Tooltip>
                              <Tooltip content='Digitally sign this document'>
                                <Button
                                  variant='default'
                                  size='sm'
                                  onClick={() => handleSign(document)}
                                  disabled={isProcessing === document.id}
                                  className='bg-green-600 hover:bg-green-700'
                                >
                                  {isProcessing === document.id ? (
                                    <Clock className='h-4 w-4 animate-spin' />
                                  ) : (
                                    <Scale className='h-4 w-4' />
                                  )}
                                  Sign
                                </Button>
                              </Tooltip>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Sign Pagination */}
                    {signTotalPages > 1 && (
                      <div className='mt-6 flex justify-center'>
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <Tooltip content='Previous page'>
                                <PaginationPrevious
                                  onClick={() =>
                                    setSignCurrentPage(
                                      Math.max(1, signCurrentPage - 1)
                                    )
                                  }
                                  className={
                                    signCurrentPage === 1
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>

                            {getVisiblePages(
                              signCurrentPage,
                              signTotalPages
                            ).map((page, index) => (
                              <PaginationItem key={index}>
                                {page === '...' ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <Tooltip content={`Go to page ${page}`}>
                                    <PaginationLink
                                      onClick={() =>
                                        setSignCurrentPage(page as number)
                                      }
                                      isActive={signCurrentPage === page}
                                      className='cursor-pointer'
                                    >
                                      {page}
                                    </PaginationLink>
                                  </Tooltip>
                                )}
                              </PaginationItem>
                            ))}

                            <PaginationItem>
                              <Tooltip content='Next page'>
                                <PaginationNext
                                  onClick={() =>
                                    setSignCurrentPage(
                                      Math.min(
                                        signTotalPages,
                                        signCurrentPage + 1
                                      )
                                    )
                                  }
                                  className={
                                    signCurrentPage === signTotalPages
                                      ? 'pointer-events-none opacity-50'
                                      : 'cursor-pointer'
                                  }
                                />
                              </Tooltip>
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
