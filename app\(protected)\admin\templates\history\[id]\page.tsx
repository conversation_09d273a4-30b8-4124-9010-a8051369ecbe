'use client';

import React, { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, FileEdit, Eye, AlertTriangle, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useTemplateWithHistory } from '@/hooks/useTemplateWithHistory';
import { useCanEditTemplates } from '@/lib/auth/template-management-guard';
import type { Schema } from '@/amplify/data/resource';

type TemplateVersion = Schema['TemplateVersion']['type'];

// Extended type for UI display that includes additional fields
interface TemplateVersionDisplay extends TemplateVersion {
  summary?: string;
  legalUpdateId?: string;
  startDate?: string;
  endDate?: string;
}

export default function TemplateHistoryPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const canEditTemplates = useCanEditTemplates();

  const { template, versions, isLoading, error } = useTemplateWithHistory(id);
  const [selectedVersion, setSelectedVersion] =
    useState<TemplateVersionDisplay | null>(null);

  const handleViewVersion = (version: TemplateVersionDisplay) => {
    setSelectedVersion(version);
  };

  const handleRestoreVersion = (version: TemplateVersionDisplay) => {
    // In a real implementation, this would create a new draft from the selected version
    // For now, navigate to the edit page
    router.push(`/admin/templates/edit/${id}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='flex items-center space-x-2'>
            <Loader2 className='h-6 w-6 animate-spin' />
            <span>Loading template history...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center mb-6'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => router.push('/admin/templates')}
          className='mr-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Templates
        </Button>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
            {template
              ? `Version History: ${template.templateState} ${template.type}`
              : 'Template History'}
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            View all versions and changes for this template
          </p>
        </div>
      </div>

      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}

      {template && (
        <Card className='mb-6'>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
            <CardDescription>
              Current details about this template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  State
                </p>
                <p>{template.templateState}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Document Type
                </p>
                <p>{template.type}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Template Name
                </p>
                <p>{template.templateName}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Status
                </p>
                <p>{template.isActive ? 'Active' : 'Inactive'}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Created
                </p>
                <p>{formatDate(template.createdAt)}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Last Updated
                </p>
                <p>{formatDate(template.updatedAt)}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Total Versions
                </p>
                <p>{versions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Version History</CardTitle>
          <CardDescription>All versions of this template</CardDescription>
        </CardHeader>
        <CardContent>
          {versions.length === 0 ? (
            <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
              No version history found for this template.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Version</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Summary</TableHead>
                  <TableHead>Legal Update</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {versions.map(version => (
                  <TableRow
                    key={`${version.templateId}-${version.versionNumber}`}
                  >
                    <TableCell>v{version.versionNumber}</TableCell>
                    <TableCell>{formatDateTime(version.createdAt)}</TableCell>
                    <TableCell>
                      {version.startDate
                        ? formatDate(version.startDate)
                        : formatDate(version.createdAt)}
                    </TableCell>
                    <TableCell>
                      {version.endDate
                        ? formatDate(version.endDate)
                        : 'Current'}
                    </TableCell>
                    <TableCell>
                      {version.summary || `Version ${version.versionNumber}`}
                    </TableCell>
                    <TableCell>{version.legalUpdateId ?? 'N/A'}</TableCell>
                    <TableCell>
                      <div className='flex space-x-1'>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleViewVersion(version)}
                              className='h-8 w-8 p-0'
                            >
                              <Eye className='h-4 w-4' />
                              <span className='sr-only'>View</span>
                            </Button>
                          </DialogTrigger>
                          <DialogContent className='max-w-4xl max-h-[80vh] overflow-y-auto'>
                            <DialogHeader>
                              <DialogTitle>
                                Template Version v{version.versionNumber}
                              </DialogTitle>
                              <DialogDescription>
                                Created on {formatDateTime(version.createdAt)}
                              </DialogDescription>
                            </DialogHeader>
                            <div className='border p-6 rounded-md bg-background whitespace-pre-wrap font-serif text-sm'>
                              {version.content}
                            </div>
                          </DialogContent>
                        </Dialog>

                        {canEditTemplates && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleRestoreVersion(version)}
                            className='h-8 w-8 p-0'
                            title='Create new version from this template'
                          >
                            <FileEdit className='h-4 w-4' />
                            <span className='sr-only'>Restore</span>
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
