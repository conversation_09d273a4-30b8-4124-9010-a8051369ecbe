'use client';

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  User,
  Home,
  ChevronRight,
  FileText,
  Shield,
  Upload,
  AlertTriangle,
  Settings,
  Users,
  ArrowLeft,
} from 'lucide-react';
import { useUserContext } from './user-context';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

export function UserBreadcrumb() {
  const { selectedUser } = useUserContext();
  const pathname = usePathname();
  const router = useRouter();

  // Generate breadcrumb items based on current path
  const getBreadcrumbItems = (): BreadcrumbItem[] => {
    const items: BreadcrumbItem[] = [
      {
        label: 'Welon Trust Dashboard',
        href: '/emergency',
        icon: <Home className='h-4 w-4' />,
      },
    ];

    // Add path-specific breadcrumbs
    if (pathname.includes('/emergency/documents')) {
      items.push({
        label: 'Emergency Documents',
        href: '/emergency/documents',
        icon: <FileText className='h-4 w-4' />,
      });
    } else if (pathname.includes('/emergency/submit-evidence')) {
      items.push({
        label: 'Submit Evidence',
        href: '/emergency/submit-evidence',
        icon: <Shield className='h-4 w-4' />,
      });
    } else if (pathname.includes('/staff/documents/upload')) {
      items.push({
        label: 'Document Upload',
        href: '/staff/documents/upload',
        icon: <Upload className='h-4 w-4' />,
      });
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  if (!selectedUser && breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <Card className='mb-6 border-blue-200 bg-blue-50/50'>
      <CardContent className='p-4'>
        <div className='flex items-center justify-between'>
          {/* Breadcrumb Navigation */}
          <div className='flex items-center space-x-4'>
            {/* Back Button - only show if not on main dashboard */}
            {breadcrumbItems.length > 1 && (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => router.back()}
                className='h-8 px-2 text-muted-foreground hover:text-foreground'
              >
                <ArrowLeft className='h-4 w-4 mr-1' />
                Back
              </Button>
            )}

            <div className='flex items-center space-x-2 text-sm'>
              {breadcrumbItems.map((item, index) => (
                <React.Fragment key={index}>
                  <div className='flex items-center gap-1.5'>
                    {item.icon}
                    {item.href && index < breadcrumbItems.length - 1 ? (
                      <button
                        onClick={() => router.push(item.href!)}
                        className='text-muted-foreground hover:text-foreground transition-colors cursor-pointer underline-offset-4 hover:underline'
                      >
                        {item.label}
                      </button>
                    ) : (
                      <span
                        className={
                          index === breadcrumbItems.length - 1
                            ? 'font-medium'
                            : 'text-muted-foreground'
                        }
                      >
                        {item.label}
                      </span>
                    )}
                  </div>
                  {index < breadcrumbItems.length - 1 && (
                    <ChevronRight className='h-4 w-4 text-muted-foreground' />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* User Context Display */}
          {selectedUser && (
            <div className='flex items-center gap-3'>
              <div className='flex items-center gap-2 px-3 py-1.5 bg-background border border-blue-200 rounded-lg'>
                <User className='h-4 w-4 text-blue-600' />
                <div className='text-sm'>
                  <span className='font-medium text-blue-800'>Managing:</span>
                  <span className='ml-1 text-blue-700'>
                    {selectedUser.name}
                  </span>
                </div>
                <Badge
                  variant={
                    selectedUser.status === 'active' ? 'default' : 'outline'
                  }
                  className='text-xs ml-2'
                >
                  {selectedUser.status}
                </Badge>
              </div>
            </div>
          )}
        </div>

        {/* Additional User Info */}
        {selectedUser && (
          <div className='mt-3 pt-3 border-t border-blue-200'>
            <div className='flex items-center justify-between text-xs text-blue-600'>
              <div className='flex items-center gap-4'>
                <span>Email: {selectedUser.email}</span>
                <span>ID: {selectedUser.id}</span>
                <span>Role: {selectedUser.role}</span>
                {selectedUser.subrole && (
                  <span>Subrole: {selectedUser.subrole}</span>
                )}
              </div>

              {/* Quick Actions for Selected User */}
              <div className='flex items-center gap-2'>
                <Button
                  variant='outline'
                  size='sm'
                  className='h-6 px-2 text-xs border-blue-300 text-blue-700 hover:bg-blue-100'
                  onClick={() => router.push('/emergency/documents')}
                >
                  <FileText className='h-3 w-3 mr-1' />
                  Documents
                </Button>

                <Button
                  variant='outline'
                  size='sm'
                  className='h-6 px-2 text-xs border-blue-300 text-blue-700 hover:bg-blue-100'
                  onClick={() => router.push('/staff/documents/upload')}
                >
                  <Upload className='h-3 w-3 mr-1' />
                  Upload
                </Button>

                <Button
                  variant='outline'
                  size='sm'
                  className='h-6 px-2 text-xs border-blue-300 text-blue-700 hover:bg-blue-100'
                  onClick={() => router.push('/emergency/submit-evidence')}
                >
                  <Shield className='h-3 w-3 mr-1' />
                  Evidence
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* No User Selected State */}
        {!selectedUser && (
          <div className='mt-3 pt-3 border-t border-blue-200'>
            <div className='flex items-center gap-2 text-sm text-blue-600'>
              <AlertTriangle className='h-4 w-4' />
              <span>
                No member selected. Use the member selector above to scope your
                view to a specific member's documents and actions.
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
