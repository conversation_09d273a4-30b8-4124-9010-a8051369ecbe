'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';
import { PricingInfo } from './types';

interface SubscriptionCardProps {
  pricing: PricingInfo;
  selectedCycle: 'Monthly' | 'Annual';
  onSelect: () => void;
  isSelected?: boolean;
}

export function SubscriptionCard({
  pricing,
  selectedCycle,
  onSelect,
  isSelected = false,
}: SubscriptionCardProps) {
  const price =
    selectedCycle === 'Monthly' ? pricing.monthlyPrice : pricing.annualPrice;
  const annualSavings = (
    pricing.monthlyPrice * 12 -
    pricing.annualPrice
  ).toFixed(2);

  return (
    <Card
      className={`w-full transition-all ${isSelected ? 'border-green-2010c ring-1 ring-green-2010c' : 'hover:border-gray-300'}`}
    >
      <CardHeader>
        {pricing.isPopular && (
          <Badge className='w-fit mb-2 bg-green-2010c'>Most Popular</Badge>
        )}
        <CardTitle>{pricing.tier} Plan</CardTitle>
        <CardDescription>
          {selectedCycle === 'Annual' && (
            <span className='text-green-2010c font-medium'>
              Save ${annualSavings} annually
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-baseline'>
          <span className='text-3xl font-bold'>${price}</span>
          <span className='text-sm text-muted-foreground ml-2'>
            /{selectedCycle === 'Monthly' ? 'month' : 'year'}
          </span>
        </div>

        <ul className='space-y-2'>
          {pricing.features.map((feature, index) => (
            <li key={index} className='flex items-start'>
              <Check className='h-5 w-5 text-green-2010c shrink-0 mr-2' />
              <span className='text-sm'>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button
          onClick={onSelect}
          className={`w-full ${isSelected ? 'bg-green-2010c hover:bg-green-2010c/90' : ''}`}
          variant={isSelected ? 'default' : 'outline'}
        >
          {isSelected ? 'Selected' : 'Select Plan'}
        </Button>
      </CardFooter>
    </Card>
  );
}
