'use client';

import { useState } from 'react';
import { ContactForm } from '@/components/emergency-contacts/ContactForm';
import {
  useEmergencyContacts,
  type EmergencyContact,
} from '@/hooks/useEmergencyContacts';
import { useRouter } from 'next/navigation';
import { ContactFormSkeleton } from '@/components/ui/skeletons/contact-form-skeleton';

// Define a storage key constant
const STORAGE_KEY = 'emergency-contact-form-data';

export default function AddContactPage() {
  const router = useRouter();
  const { addContact } = useEmergencyContacts();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddContact = async (formData: Omit<EmergencyContact, 'id'>) => {
    setIsSubmitting(true);
    try {
      await addContact(formData);
      return; // The form component will handle navigation and toast
    } catch (error) {
      console.error('Error adding contact:', error);
      setIsSubmitting(false);
      throw error;
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      {isSubmitting ? (
        <ContactFormSkeleton />
      ) : (
        <ContactForm
          onSubmit={handleAddContact}
          isEditing={false}
          isLoading={false}
          storageKey={STORAGE_KEY}
        />
      )}
    </div>
  );
}
