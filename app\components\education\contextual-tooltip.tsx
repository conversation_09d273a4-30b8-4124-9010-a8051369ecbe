'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  X,
  HelpCircle,
  BookOpen,
  Video,
  ExternalLink,
  ChevronRight,
} from 'lucide-react';

interface ContextualTooltipProps {
  term: string;
  definition: string;
  relatedContent?: {
    id: string;
    title: string;
    type: 'video' | 'article' | 'guide';
    url: string;
  }[];
  position?: 'top' | 'bottom' | 'left' | 'right';
  onClose?: () => void;
  onDisable?: () => void;
  showCount?: number;
  maxShows?: number;
}

export function ContextualTooltip({
  term,
  definition,
  relatedContent = [],
  position = 'top',
  onClose,
  onDisable,
  showCount = 0,
  maxShows = 3,
}: ContextualTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldAutoHide, setShouldAutoHide] = useState(false);

  useEffect(() => {
    // Auto-hide after max shows reached
    if (showCount >= maxShows) {
      setShouldAutoHide(true);
    }
  }, [showCount, maxShows]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  const handleDisable = () => {
    setIsVisible(false);
    onDisable?.();
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
      case 'bottom':
        return 'top-full mt-2 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'right-full mr-2 top-1/2 transform -translate-y-1/2';
      case 'right':
        return 'left-full ml-2 top-1/2 transform -translate-y-1/2';
      default:
        return 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
    }
  };

  const getIconForContentType = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className='h-4 w-4' />;
      case 'article':
        return <BookOpen className='h-4 w-4' />;
      case 'guide':
        return <ExternalLink className='h-4 w-4' />;
      default:
        return <HelpCircle className='h-4 w-4' />;
    }
  };

  if (shouldAutoHide) {
    return null;
  }

  return (
    <div className='relative inline-block'>
      {/* Trigger */}
      <button
        className='inline-flex items-center text-blue-600 hover:text-blue-800 underline decoration-dotted underline-offset-2'
        onClick={() => setIsVisible(!isVisible)}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setTimeout(() => setIsVisible(false), 300)}
      >
        {term}
        <HelpCircle className='h-3 w-3 ml-1' />
      </button>

      {/* Tooltip */}
      {isVisible && (
        <div className={`absolute z-50 w-80 ${getPositionClasses()}`}>
          <Card className='shadow-lg border-2 border-blue-200'>
            <CardHeader className='pb-2'>
              <div className='flex justify-between items-start'>
                <CardTitle className='text-lg text-blue-900'>{term}</CardTitle>
                <div className='flex gap-1'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleClose}
                    className='h-6 w-6 p-0'
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className='text-sm text-[var(--custom-gray-dark)] mb-4'>
                {definition}
              </CardDescription>

              {relatedContent.length > 0 && (
                <div className='space-y-2'>
                  <h4 className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Learn More:
                  </h4>
                  {relatedContent.map(content => (
                    <a
                      key={content.id}
                      href={content.url}
                      className='flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-2 rounded transition-colors'
                    >
                      {getIconForContentType(content.type)}
                      <span className='flex-1'>{content.title}</span>
                      <ChevronRight className='h-3 w-3' />
                    </a>
                  ))}
                </div>
              )}

              {showCount >= 2 && (
                <div className='mt-4 pt-3 border-t border-gray-200'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleDisable}
                    className='text-xs'
                  >
                    Don't show these tips anymore
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Arrow */}
          <div
            className={`absolute w-3 h-3 bg-background border-l border-t border-blue-200 transform rotate-45 ${
              position === 'top'
                ? 'top-full -mt-2 left-1/2 -translate-x-1/2'
                : position === 'bottom'
                  ? 'bottom-full -mb-2 left-1/2 -translate-x-1/2'
                  : position === 'left'
                    ? 'left-full -ml-2 top-1/2 -translate-y-1/2'
                    : 'right-full -mr-2 top-1/2 -translate-y-1/2'
            }`}
          />
        </div>
      )}
    </div>
  );
}

// Hook for managing contextual tooltips
export function useContextualTooltips() {
  const [tooltipCounts, setTooltipCounts] = useState<Record<string, number>>(
    {}
  );
  const [disabledTooltips, setDisabledTooltips] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    // Load from localStorage
    const saved = localStorage.getItem('tooltipCounts');
    const disabled = localStorage.getItem('disabledTooltips');

    if (saved) {
      setTooltipCounts(JSON.parse(saved));
    }
    if (disabled) {
      setDisabledTooltips(new Set(JSON.parse(disabled)));
    }
  }, []);

  const incrementTooltipCount = (term: string) => {
    const newCounts = {
      ...tooltipCounts,
      [term]: (tooltipCounts[term] || 0) + 1,
    };
    setTooltipCounts(newCounts);
    localStorage.setItem('tooltipCounts', JSON.stringify(newCounts));
  };

  const disableTooltip = (term: string) => {
    const newDisabled = new Set(disabledTooltips).add(term);
    setDisabledTooltips(newDisabled);
    localStorage.setItem('disabledTooltips', JSON.stringify([...newDisabled]));
  };

  const isTooltipDisabled = (term: string) => {
    return disabledTooltips.has(term);
  };

  const getTooltipCount = (term: string) => {
    return tooltipCounts[term] || 0;
  };

  return {
    incrementTooltipCount,
    disableTooltip,
    isTooltipDisabled,
    getTooltipCount,
  };
}

// Predefined tooltips for common estate planning terms
export const ESTATE_PLANNING_TOOLTIPS = {
  trustee: {
    definition:
      "A person or institution appointed to manage and distribute assets held in a trust according to the trust's terms.",
    relatedContent: [
      {
        id: 'trust-basics',
        title: 'Understanding Trusts and Trustees',
        type: 'video' as const,
        url: '/education/videos/trust-basics',
      },
      {
        id: 'choosing-trustee',
        title: 'How to Choose the Right Trustee',
        type: 'article' as const,
        url: '/education/articles/choosing-trustee',
      },
    ],
  },
  executor: {
    definition:
      'The person named in a will to carry out the instructions and wishes of the deceased person.',
    relatedContent: [
      {
        id: 'executor-duties',
        title: 'Executor Responsibilities Explained',
        type: 'video' as const,
        url: '/education/videos/executor-duties',
      },
    ],
  },
  beneficiary: {
    definition:
      'A person or entity designated to receive assets or benefits from a will, trust, insurance policy, or other legal document.',
    relatedContent: [
      {
        id: 'beneficiary-guide',
        title: 'Naming Beneficiaries: A Complete Guide',
        type: 'article' as const,
        url: '/education/articles/beneficiary-guide',
      },
    ],
  },
  'power of attorney': {
    definition:
      'A legal document that gives someone the authority to act on your behalf in financial, legal, or medical matters.',
    relatedContent: [
      {
        id: 'poa-types',
        title: 'Types of Power of Attorney',
        type: 'video' as const,
        url: '/education/videos/poa-types',
      },
      {
        id: 'poa-guide',
        title: 'Power of Attorney Decision Tree',
        type: 'guide' as const,
        url: '/education/guides/poa-decision-tree',
      },
    ],
  },
};
