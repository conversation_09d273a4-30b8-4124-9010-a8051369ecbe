/**
 * Question validation utilities for interview answers
 */

export type QuestionValidationType =
  | 'number'
  | 'email'
  | 'phone'
  | 'required'
  | 'text';

export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

export interface ValidationOptions {
  required?: boolean;
  maxLength?: number;
  minLength?: number;
  sanitizeHtml?: boolean;
}

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(value: string): string {
  if (!value) return value;

  // Remove HTML tags and decode HTML entities
  return value
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/')
    .trim();
}

/**
 * Validate required field
 */
export function validateRequired(value: string): ValidationResult {
  const trimmedValue = value?.trim() || '';

  if (!trimmedValue) {
    return {
      isValid: false,
      errorMessage: 'This field is required',
    };
  }

  return { isValid: true };
}

/**
 * Validate text length
 */
export function validateLength(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';
  const { minLength = 0, maxLength = 5000 } = options;

  if (trimmedValue.length < minLength) {
    return {
      isValid: false,
      errorMessage: `Minimum length is ${minLength} characters`,
    };
  }

  if (trimmedValue.length > maxLength) {
    return {
      isValid: false,
      errorMessage: `Maximum length is ${maxLength} characters`,
    };
  }

  return { isValid: true };
}

/**
 * Validate a number input
 */
export function validateNumber(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Check if it's a valid number
  if (isNaN(Number(trimmedValue))) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  // Check for valid number format (no leading/trailing spaces, no multiple decimals)
  const numberRegex = /^-?\d*\.?\d+$/;
  if (!numberRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  return { isValid: true };
}

/**
 * Validate an email input
 */
export function validateEmail(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  // Additional checks for common email issues
  if (trimmedValue.length > 255) {
    return {
      isValid: false,
      errorMessage: 'Email address is too long',
    };
  }

  if (
    trimmedValue.includes('..') ||
    trimmedValue.startsWith('.') ||
    trimmedValue.endsWith('.')
  ) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  return { isValid: true };
}

/**
 * Validate a phone number input
 */
export function validatePhone(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  const trimmedValue = value?.trim() || '';

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(trimmedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  if (!trimmedValue) {
    return { isValid: true }; // Empty values are allowed if not required
  }

  // US phone number regex - supports various formats
  const phoneRegex =
    /^(\+1\s?)?(\([0-9]{3}\)|[0-9]{3})[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}$/;

  if (!phoneRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a valid US phone number (e.g., (*************)',
    };
  }

  return { isValid: true };
}

/**
 * Validate text input with HTML sanitization and length checks
 */
export function validateText(
  value: string,
  options: ValidationOptions = {}
): ValidationResult {
  let processedValue = value || '';

  // Sanitize HTML if requested
  if (options.sanitizeHtml !== false) {
    // Default to true
    processedValue = sanitizeHtml(processedValue);
  }

  // Check required first
  if (options.required) {
    const requiredResult = validateRequired(processedValue);
    if (!requiredResult.isValid) return requiredResult;
  }

  // Check length constraints
  const lengthResult = validateLength(processedValue, options);
  if (!lengthResult.isValid) return lengthResult;

  return { isValid: true };
}

/**
 * Main validation function that routes to specific validators
 */
export function validateQuestionAnswer(
  value: string,
  validationType?: QuestionValidationType,
  options: ValidationOptions = {}
): ValidationResult {
  // Default options for better UX
  const defaultOptions: ValidationOptions = {
    required: true, // Make fields required by default
    maxLength: 200, // Reasonable max length
    sanitizeHtml: true, // Sanitize HTML by default
    ...options,
  };

  if (!validationType || validationType === 'text') {
    return validateText(value, defaultOptions);
  }

  switch (validationType) {
    case 'number':
      return validateNumber(value, defaultOptions);
    case 'email':
      return validateEmail(value, defaultOptions);
    case 'phone':
      return validatePhone(value, defaultOptions);
    case 'required':
      return validateRequired(value);
    default:
      return validateText(value, defaultOptions);
  }
}

/**
 * Get user-friendly validation rule description
 */
export function getValidationDescription(
  validationType?: QuestionValidationType,
  options: ValidationOptions = {}
): string {
  const { required = true, maxLength = 5000 } = options;

  let description = '';

  switch (validationType) {
    case 'number':
      description = 'Only numeric values are allowed';
      break;
    case 'email':
      description = 'Please enter a valid email address';
      break;
    case 'phone':
      description = 'Please enter a valid US phone number';
      break;
    case 'text':
    default:
      description = 'Text input';
      break;
  }

  if (required) {
    description += ' (required)';
  }

  if (maxLength && maxLength < 5000) {
    description += ` - Maximum ${maxLength} characters`;
  }

  return description;
}
