'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ContactType, EmergencyContact } from './types';
import { UserPlus } from 'lucide-react';

interface ContactFormProps {
  onSubmit: (contact: Omit<EmergencyContact, 'id' | 'status'>) => void;
  onCancel: () => void;
  isEditing?: boolean;
  initialData?: Partial<EmergencyContact>;
  inModal?: boolean;
}

export function ContactForm({
  onSubmit,
  onCancel,
  isEditing = false,
  initialData = {},
  inModal = false,
}: ContactFormProps) {
  const [name, setName] = useState(initialData.name || '');
  const [relationship, setRelationship] = useState(
    initialData.relationship || ''
  );
  const [phone, setPhone] = useState(initialData.phone || '');
  const [email, setEmail] = useState(initialData.email || '');
  const [type, setType] = useState<ContactType>(initialData.type || 'Other');
  const [isPrimary, setIsPrimary] = useState(initialData.isPrimary || false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!name.trim()) newErrors.name = 'Name is required';
    if (!relationship.trim())
      newErrors.relationship = 'Relationship is required';

    if (!phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else {
      // Simple phone validation
      const phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
      if (!phoneRegex.test(phone)) {
        newErrors.phone = 'Please enter a valid phone number';
      }
    }

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else {
      // Simple email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        newErrors.email = 'Please enter a valid email';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        name,
        relationship,
        phone,
        email,
        type,
        isPrimary,
      });
    }
  };

  // If in modal, don't wrap in Card
  return inModal ? (
    <form onSubmit={handleSubmit} className='py-4'>
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Label htmlFor='name'>Full Name</Label>
          <Input
            id='name'
            value={name}
            onChange={e => setName(e.target.value)}
            placeholder="Enter contact's full name"
            className={errors.name ? 'border-destructive' : ''}
          />
          {errors.name && (
            <p className='text-sm text-destructive'>{errors.name}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='relationship'>Relationship</Label>
          <Input
            id='relationship'
            value={relationship}
            onChange={e => setRelationship(e.target.value)}
            placeholder='e.g., Friend, Sibling, Attorney'
            className={errors.relationship ? 'border-destructive' : ''}
          />
          {errors.relationship && (
            <p className='text-sm text-destructive'>{errors.relationship}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='phone'>Phone Number</Label>
          <Input
            id='phone'
            type='tel'
            value={phone}
            onChange={e => setPhone(e.target.value)}
            placeholder='(*************'
            className={errors.phone ? 'border-destructive' : ''}
          />
          {errors.phone && (
            <p className='text-sm text-destructive'>{errors.phone}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label htmlFor='email'>Email Address</Label>
          <Input
            id='email'
            type='email'
            value={email}
            onChange={e => setEmail(e.target.value)}
            placeholder='<EMAIL>'
            className={errors.email ? 'border-destructive' : ''}
          />
          {errors.email && (
            <p className='text-sm text-destructive'>{errors.email}</p>
          )}
        </div>

        <div className='space-y-2'>
          <Label>Contact Type</Label>
          <RadioGroup
            value={type}
            onValueChange={value => setType(value as ContactType)}
          >
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='Medical' id='medical' />
              <Label htmlFor='medical' className='font-normal'>
                Medical Contact (receives medical details)
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='Other' id='other' />
              <Label htmlFor='other' className='font-normal'>
                Other Contact (receives non-medical details)
              </Label>
            </div>
          </RadioGroup>
        </div>

        <div className='flex items-center space-x-2 pt-2'>
          <Checkbox
            id='primary'
            checked={isPrimary}
            onCheckedChange={checked => setIsPrimary(checked as boolean)}
          />
          <Label htmlFor='primary' className='font-normal'>
            Set as primary contact for this type
          </Label>
        </div>
      </div>

      <div className='flex justify-end space-x-2 pt-4'>
        <Button type='button' variant='outline' onClick={onCancel}>
          Cancel
        </Button>
        <Button type='submit'>
          <UserPlus className='mr-2 h-4 w-4' />
          {isEditing ? 'Update Contact' : 'Add Contact'}
        </Button>
      </div>
    </form>
  ) : (
    <Card className='w-full max-w-md mx-auto'>
      <CardHeader>
        <CardTitle className='text-xl font-bold'>
          {isEditing ? 'Edit Emergency Contact' : 'Add Emergency Contact'}
        </CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='name'>Full Name</Label>
            <Input
              id='name'
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter contact's full name"
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && (
              <p className='text-sm text-destructive'>{errors.name}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='relationship'>Relationship</Label>
            <Input
              id='relationship'
              value={relationship}
              onChange={e => setRelationship(e.target.value)}
              placeholder='e.g., Friend, Sibling, Attorney'
              className={errors.relationship ? 'border-destructive' : ''}
            />
            {errors.relationship && (
              <p className='text-sm text-destructive'>{errors.relationship}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='phone'>Phone Number</Label>
            <Input
              id='phone'
              type='tel'
              value={phone}
              onChange={e => setPhone(e.target.value)}
              placeholder='(*************'
              className={errors.phone ? 'border-destructive' : ''}
            />
            {errors.phone && (
              <p className='text-sm text-destructive'>{errors.phone}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='email'>Email Address</Label>
            <Input
              id='email'
              type='email'
              value={email}
              onChange={e => setEmail(e.target.value)}
              placeholder='<EMAIL>'
              className={errors.email ? 'border-destructive' : ''}
            />
            {errors.email && (
              <p className='text-sm text-destructive'>{errors.email}</p>
            )}
          </div>

          <div className='space-y-2'>
            <Label>Contact Type</Label>
            <RadioGroup
              value={type}
              onValueChange={value => setType(value as ContactType)}
            >
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='Medical' id='medical' />
                <Label htmlFor='medical' className='font-normal'>
                  Medical Contact (receives medical details)
                </Label>
              </div>
              <div className='flex items-center space-x-2'>
                <RadioGroupItem value='Other' id='other' />
                <Label htmlFor='other' className='font-normal'>
                  Other Contact (receives non-medical details)
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className='flex items-center space-x-2 pt-2'>
            <Checkbox
              id='primary'
              checked={isPrimary}
              onCheckedChange={checked => setIsPrimary(checked as boolean)}
            />
            <Label htmlFor='primary' className='font-normal'>
              Set as primary contact for this type
            </Label>
          </div>
        </CardContent>

        <CardFooter className='flex justify-between'>
          <Button type='button' variant='outline' onClick={onCancel}>
            Cancel
          </Button>
          <Button type='submit'>
            <UserPlus className='mr-2 h-4 w-4' />
            {isEditing ? 'Update Contact' : 'Add Contact'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
