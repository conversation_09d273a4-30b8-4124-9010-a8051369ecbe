/**
 * ArticleViewer Component
 *
 * A component for displaying article content with table of contents and navigation.
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ArticleContent } from '@/types/education';
import { Button } from '@/components/ui/button';

interface ArticleViewerProps {
  article: ArticleContent;
  onComplete?: () => void;
}

export function ArticleViewer({ article, onComplete }: ArticleViewerProps) {
  const [activeSection, setActiveSection] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const contentRef = useRef<HTMLDivElement>(null);
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  // Initialize section refs
  useEffect(() => {
    article.tableOfContents.forEach(section => {
      sectionRefs.current[section.id] = document.getElementById(section.id);
    });
  }, [article.tableOfContents]);

  // Track scroll position and update active section
  useEffect(() => {
    const content = contentRef.current;
    if (!content) return;

    const handleScroll = () => {
      // Calculate scroll progress
      const scrollTop = content.scrollTop;
      const scrollHeight = content.scrollHeight - content.clientHeight;
      const currentProgress = Math.round((scrollTop / scrollHeight) * 100);
      setProgress(currentProgress);

      // Check if we've reached the end (within 5% of the bottom)
      if (currentProgress >= 95 && onComplete) {
        onComplete();
      }

      // Determine active section
      let currentSection = '';

      for (const section of article.tableOfContents) {
        const element = sectionRefs.current[section.id];
        if (!element) continue;

        const rect = element.getBoundingClientRect();
        if (rect.top <= 100) {
          currentSection = section.id;
        } else {
          break;
        }
      }

      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    content.addEventListener('scroll', handleScroll);
    return () => content.removeEventListener('scroll', handleScroll);
  }, [article.tableOfContents, onComplete]);

  // Scroll to section
  const scrollToSection = (sectionId: string) => {
    const section = sectionRefs.current[sectionId];
    if (section && contentRef.current) {
      contentRef.current.scrollTo({
        top: section.offsetTop - 20,
        behavior: 'smooth',
      });
    }
  };

  // Scroll to top
  const scrollToTop = () => {
    if (contentRef.current) {
      contentRef.current.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  };

  // Convert markdown to HTML (simplified version)
  const renderMarkdown = (markdown: string) => {
    // This is a very simplified markdown renderer
    // In a real app, you would use a library like marked or remark

    // Replace headings
    let html = markdown
      .replace(/^# (.*$)/gm, '<h1 id="section-1">$1</h1>')
      .replace(/^## (.*$)/gm, '<h2 id="section-2">$1</h2>')
      .replace(/^### (.*$)/gm, '<h3 id="section-3">$1</h3>');

    // Replace paragraphs
    html = html.replace(/^(?!<h[1-6]>)(.*$)/gm, '<p>$1</p>');

    return html;
  };

  return (
    <div className='flex flex-col md:flex-row gap-6 w-full max-w-5xl mx-auto'>
      {/* Table of Contents */}
      <div className='md:w-1/4 flex flex-col space-y-2'>
        <h3 className='font-semibold text-lg mb-2'>Table of Contents</h3>
        <div className='h-[calc(100vh-200px)] overflow-y-auto'>
          <ul className='space-y-1'>
            {article.tableOfContents.map(section => (
              <li
                key={section.id}
                style={{ marginLeft: `${(section.level - 1) * 12}px` }}
              >
                <button
                  onClick={() => scrollToSection(section.id)}
                  className={`text-left w-full px-2 py-1 rounded hover:bg-accent ${
                    activeSection === section.id ? 'bg-accent font-medium' : ''
                  }`}
                >
                  {section.title}
                </button>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Article Content */}
      <div className='md:w-3/4 flex flex-col'>
        <div className='h-[calc(100vh-200px)] overflow-y-auto' ref={contentRef}>
          <div className='prose dark:prose-invert max-w-none'>
            <div
              dangerouslySetInnerHTML={{
                __html: renderMarkdown(article.content),
              }}
            />
          </div>
        </div>

        {/* Progress and Back to Top */}
        <div className='flex items-center justify-between mt-4'>
          <div className='text-sm'>Reading progress: {progress}%</div>
          <Button variant='outline' size='sm' onClick={scrollToTop}>
            Back to Top
          </Button>
        </div>
      </div>
    </div>
  );
}
