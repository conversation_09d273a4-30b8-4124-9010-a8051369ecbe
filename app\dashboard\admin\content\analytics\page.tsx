'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  Users,
  Eye,
  ThumbsUp,
  Clock,
  Download,
  Search,
  Filter,
  Calendar,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';

// Mock analytics data
const mockAnalytics = {
  overview: {
    totalViews: 45230,
    totalUsers: 3420,
    avgRating: 4.6,
    completionRate: 78,
    totalContent: 156,
    activeUsers: 892,
  },
  topContent: [
    {
      id: '1',
      title: 'Estate Planning Basics for Childfree Individuals',
      type: 'video',
      views: 8450,
      rating: 4.8,
      completionRate: 85,
      avgWatchTime: '12:30',
      trend: 'up',
    },
    {
      id: '2',
      title: 'Creating Your Will: Step by Step Guide',
      type: 'video',
      views: 7230,
      rating: 4.9,
      completionRate: 92,
      avgWatchTime: '18:45',
      trend: 'up',
    },
    {
      id: '3',
      title: '10 Common Estate Planning Mistakes',
      type: 'article',
      views: 6890,
      rating: 4.7,
      completionRate: 76,
      avgReadTime: '6:20',
      trend: 'down',
    },
    {
      id: '4',
      title: 'Power of Attorney Decision Tree',
      type: 'infographic',
      views: 5670,
      rating: 4.6,
      completionRate: 68,
      avgViewTime: '3:15',
      trend: 'up',
    },
  ],
  searchQueries: [
    { query: 'what is a will', count: 1240, trend: 'up' },
    { query: 'trust vs will', count: 980, trend: 'up' },
    { query: 'power of attorney', count: 850, trend: 'stable' },
    { query: 'estate planning checklist', count: 720, trend: 'down' },
    { query: 'digital assets', count: 650, trend: 'up' },
  ],
  userFeedback: [
    {
      contentId: '1',
      contentTitle: 'Estate Planning Basics',
      positiveRatings: 420,
      negativeRatings: 23,
      comments: 45,
      avgRating: 4.8,
    },
    {
      contentId: '2',
      contentTitle: 'Creating Your Will',
      positiveRatings: 380,
      negativeRatings: 18,
      comments: 52,
      avgRating: 4.9,
    },
    {
      contentId: '3',
      contentTitle: 'Common Mistakes',
      positiveRatings: 290,
      negativeRatings: 35,
      comments: 28,
      avgRating: 4.7,
    },
  ],
};

export default function ContentAnalyticsPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('30d');

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUp className='h-4 w-4 text-green-600' />;
      case 'down':
        return <ArrowDown className='h-4 w-4 text-red-600' />;
      default:
        return <div className='h-4 w-4' />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-[var(--custom-gray-medium)]';
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <div className='flex justify-between items-center mb-4'>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
            Content Analytics
          </h1>
          <div className='flex gap-2'>
            <select
              value={dateRange}
              onChange={e => setDateRange(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-[var(--eggplant)]'
            >
              <option value='7d'>Last 7 days</option>
              <option value='30d'>Last 30 days</option>
              <option value='90d'>Last 90 days</option>
              <option value='1y'>Last year</option>
            </select>
            <Button variant='outline'>
              <Download className='h-4 w-4 mr-2' />
              Export Report
            </Button>
          </div>
        </div>
        <p className='text-lg text-[var(--custom-gray-medium)]'>
          Detailed analytics and performance metrics for educational content
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-4 mb-8'>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='content'>Content Performance</TabsTrigger>
          <TabsTrigger value='users'>User Behavior</TabsTrigger>
          <TabsTrigger value='feedback'>Feedback & Ratings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value='overview'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'>
            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                      Total Views
                    </p>
                    <p className='text-2xl font-bold'>
                      {mockAnalytics.overview.totalViews.toLocaleString()}
                    </p>
                    <p className='text-xs text-green-600 flex items-center mt-1'>
                      <ArrowUp className='h-3 w-3 mr-1' />
                      +12% from last month
                    </p>
                  </div>
                  <Eye className='h-8 w-8 text-blue-600' />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                      Active Users
                    </p>
                    <p className='text-2xl font-bold'>
                      {mockAnalytics.overview.activeUsers.toLocaleString()}
                    </p>
                    <p className='text-xs text-green-600 flex items-center mt-1'>
                      <ArrowUp className='h-3 w-3 mr-1' />
                      +8% from last month
                    </p>
                  </div>
                  <Users className='h-8 w-8 text-green-600' />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                      Avg. Rating
                    </p>
                    <p className='text-2xl font-bold'>
                      {mockAnalytics.overview.avgRating}
                    </p>
                    <p className='text-xs text-green-600 flex items-center mt-1'>
                      <ArrowUp className='h-3 w-3 mr-1' />
                      +0.2 from last month
                    </p>
                  </div>
                  <ThumbsUp className='h-8 w-8 text-yellow-600' />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                      Completion Rate
                    </p>
                    <p className='text-2xl font-bold'>
                      {mockAnalytics.overview.completionRate}%
                    </p>
                    <p className='text-xs text-red-600 flex items-center mt-1'>
                      <ArrowDown className='h-3 w-3 mr-1' />
                      -3% from last month
                    </p>
                  </div>
                  <BarChart3 className='h-8 w-8 text-purple-600' />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between mb-2'>
                  <Clock className='h-8 w-8 text-indigo-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                    Total Content
                  </p>
                  <p className='text-2xl font-bold'>
                    {mockAnalytics.overview.totalContent}
                  </p>
                  <p className='text-xs text-green-600 flex items-center mt-1'>
                    <ArrowUp className='h-3 w-3 mr-1' />
                    +5 this month
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                      Total Users
                    </p>
                    <p className='text-2xl font-bold'>
                      {mockAnalytics.overview.totalUsers.toLocaleString()}
                    </p>
                    <p className='text-xs text-green-600 flex items-center mt-1'>
                      <ArrowUp className='h-3 w-3 mr-1' />
                      +15% from last month
                    </p>
                  </div>
                  <TrendingUp className='h-8 w-8 text-orange-600' />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>
                Content performance over the selected time period
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='h-64 flex items-center justify-center text-[var(--custom-gray-medium)]'>
                <p>
                  Chart visualization would be implemented here using a charting
                  library like Chart.js or Recharts
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Performance Tab */}
        <TabsContent value='content'>
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Content</CardTitle>
              <CardDescription>
                Most viewed and highest rated educational content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockAnalytics.topContent.map((content, index) => (
                  <div
                    key={content.id}
                    className='flex items-center justify-between p-4 border rounded-lg'
                  >
                    <div className='flex items-center gap-4'>
                      <div className='text-lg font-bold text-[var(--custom-gray-medium)]'>
                        #{index + 1}
                      </div>
                      <div>
                        <h3 className='font-semibold'>{content.title}</h3>
                        <div className='flex items-center gap-2 mt-1'>
                          <Badge variant='outline'>{content.type}</Badge>
                          <span className='text-sm text-[var(--custom-gray-medium)]'>
                            {content.views.toLocaleString()} views
                          </span>
                          <span className='text-sm text-[var(--custom-gray-medium)]'>
                            Rating: {content.rating}/5
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className='flex items-center gap-4 text-sm'>
                      <div className='text-center'>
                        <p className='font-medium'>{content.completionRate}%</p>
                        <p className='text-[var(--custom-gray-medium)]'>
                          Completion
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='font-medium'>
                          {content.type === 'video'
                            ? content.avgWatchTime
                            : content.type === 'article'
                              ? content.avgReadTime
                              : content.avgViewTime}
                        </p>
                        <p className='text-[var(--custom-gray-medium)]'>
                          Avg. Time
                        </p>
                      </div>
                      <div
                        className={`flex items-center ${getTrendColor(content.trend)}`}
                      >
                        {getTrendIcon(content.trend)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Behavior Tab */}
        <TabsContent value='users'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            <Card>
              <CardHeader>
                <CardTitle>Top Search Queries</CardTitle>
                <CardDescription>
                  Most popular search terms used by users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {mockAnalytics.searchQueries.map((query, index) => (
                    <div
                      key={index}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <Search className='h-4 w-4 text-[var(--custom-gray-medium)]' />
                        <span className='font-medium'>{query.query}</span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <span className='text-sm text-[var(--custom-gray-medium)]'>
                          {query.count}
                        </span>
                        {getTrendIcon(query.trend)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Engagement</CardTitle>
                <CardDescription>
                  How users interact with content
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between items-center'>
                    <span>Average Session Duration</span>
                    <span className='font-semibold'>24:30</span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span>Pages per Session</span>
                    <span className='font-semibold'>3.2</span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span>Bounce Rate</span>
                    <span className='font-semibold'>32%</span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span>Return Visitors</span>
                    <span className='font-semibold'>68%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Feedback Tab */}
        <TabsContent value='feedback'>
          <Card>
            <CardHeader>
              <CardTitle>Content Feedback & Ratings</CardTitle>
              <CardDescription>
                User ratings and feedback for educational content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockAnalytics.userFeedback.map(feedback => (
                  <div
                    key={feedback.contentId}
                    className='p-4 border rounded-lg'
                  >
                    <div className='flex justify-between items-start mb-3'>
                      <h3 className='font-semibold'>{feedback.contentTitle}</h3>
                      <div className='flex items-center gap-1'>
                        <ThumbsUp className='h-4 w-4 text-yellow-500' />
                        <span className='font-medium'>
                          {feedback.avgRating}
                        </span>
                      </div>
                    </div>
                    <div className='grid grid-cols-3 gap-4 text-sm'>
                      <div className='text-center'>
                        <p className='font-medium text-green-600'>
                          {feedback.positiveRatings}
                        </p>
                        <p className='text-[var(--custom-gray-medium)]'>
                          Positive
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='font-medium text-red-600'>
                          {feedback.negativeRatings}
                        </p>
                        <p className='text-[var(--custom-gray-medium)]'>
                          Negative
                        </p>
                      </div>
                      <div className='text-center'>
                        <p className='font-medium text-blue-600'>
                          {feedback.comments}
                        </p>
                        <p className='text-[var(--custom-gray-medium)]'>
                          Comments
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
