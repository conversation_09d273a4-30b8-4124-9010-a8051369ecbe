'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  AlertCircle,
  CheckCircle2,
  Clock,
  CreditCard,
  Calendar,
} from 'lucide-react';
import { Subscription, SubscriptionStatus } from './types';

interface SubscriptionStatusCardProps {
  subscription: Subscription;
  onUpdatePayment?: () => void;
  onCancelSubscription?: () => void;
  onReactivateSubscription?: () => void;
  onResolveDelinquency?: () => void;
}

export function SubscriptionStatusCard({
  subscription,
  onUpdatePayment,
  onCancelSubscription,
  onReactivateSubscription,
  onResolveDelinquency,
}: SubscriptionStatusCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case 'Active':
        return (
          <Badge className='bg-green-2010c'>
            <CheckCircle2 className='mr-1 h-3 w-3' />
            Active
          </Badge>
        );
      case 'Canceled':
        return (
          <Badge variant='secondary'>
            <AlertCircle className='mr-1 h-3 w-3' />
            Canceled
          </Badge>
        );
      case 'Delinquent':
        return (
          <Badge variant='destructive'>
            <AlertCircle className='mr-1 h-3 w-3' />
            Delinquent
          </Badge>
        );
      case 'Suspended':
        return (
          <Badge variant='destructive'>
            <AlertCircle className='mr-1 h-3 w-3' />
            Suspended
          </Badge>
        );
      case 'In Trust':
        return (
          <Badge
            variant='outline'
            className='text-blue-2157c border-blue-2157c'
          >
            <CheckCircle2 className='mr-1 h-3 w-3' />
            In Trust
          </Badge>
        );
      default:
        return null;
    }
  };

  const getDaysUntilNextBilling = () => {
    const today = new Date();
    const nextBillingDate = new Date(subscription.nextBillingDate);
    const diffTime = nextBillingDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilNextBilling = getDaysUntilNextBilling();

  return (
    <Card>
      <CardHeader className='pb-2'>
        <div className='flex justify-between items-center'>
          <CardTitle className='text-xl'>Subscription Status</CardTitle>
          {getStatusBadge(subscription.status)}
        </div>
        <CardDescription>
          {subscription.tier} Plan ({subscription.billingCycle})
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        {subscription.status === 'Active' && (
          <>
            <div className='flex items-center justify-between'>
              <div className='flex items-center text-sm'>
                <Calendar className='mr-2 h-4 w-4 text-muted-foreground' />
                <span>Next billing date</span>
              </div>
              <span className='font-medium'>
                {formatDate(subscription.nextBillingDate)}
                {daysUntilNextBilling <= 7 && (
                  <span className='ml-2 text-sm text-muted-foreground'>
                    ({daysUntilNextBilling} days)
                  </span>
                )}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <div className='flex items-center text-sm'>
                <CreditCard className='mr-2 h-4 w-4 text-muted-foreground' />
                <span>Billing amount</span>
              </div>
              <span className='font-medium'>
                {formatAmount(subscription.amount)}
                {subscription.discount && (
                  <Badge
                    variant='outline'
                    className='ml-2 text-green-2010c border-green-2010c'
                  >
                    {subscription.discount.type === 'Percentage'
                      ? `${subscription.discount.value}% off`
                      : `$${subscription.discount.value} off`}
                  </Badge>
                )}
              </span>
            </div>
          </>
        )}

        {subscription.status === 'Delinquent' && (
          <div className='bg-destructive/10 p-4 rounded-md'>
            <div className='flex items-start space-x-2'>
              <AlertCircle className='h-5 w-5 text-destructive shrink-0 mt-0.5' />
              <div>
                <h4 className='font-medium text-destructive'>Payment Failed</h4>
                <p className='text-sm text-muted-foreground mt-1'>
                  Your last payment attempt failed. Please update your payment
                  method to avoid service interruption.
                </p>
                {onResolveDelinquency && (
                  <Button
                    onClick={onResolveDelinquency}
                    className='mt-2 bg-destructive hover:bg-destructive/90'
                    size='sm'
                  >
                    Resolve Now
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {subscription.status === 'Canceled' && (
          <div className='bg-secondary/50 p-4 rounded-md'>
            <div className='flex items-start space-x-2'>
              <Clock className='h-5 w-5 text-muted-foreground shrink-0 mt-0.5' />
              <div>
                <h4 className='font-medium'>Subscription Canceled</h4>
                <p className='text-sm text-muted-foreground mt-1'>
                  Your subscription has been canceled and will end on{' '}
                  {formatDate(subscription.endDate || '')}.
                </p>
                {subscription.cancelReason && (
                  <p className='text-sm text-muted-foreground mt-2'>
                    <span className='font-medium'>Reason:</span>{' '}
                    {subscription.cancelReason}
                  </p>
                )}
                {onReactivateSubscription && (
                  <Button
                    onClick={onReactivateSubscription}
                    className='mt-2 bg-green-2010c hover:bg-green-2010c/90'
                    size='sm'
                  >
                    Reactivate Subscription
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {subscription.status === 'Suspended' && (
          <div className='bg-destructive/10 p-4 rounded-md'>
            <div className='flex items-start space-x-2'>
              <AlertCircle className='h-5 w-5 text-destructive shrink-0 mt-0.5' />
              <div>
                <h4 className='font-medium text-destructive'>
                  Account Suspended
                </h4>
                <p className='text-sm text-muted-foreground mt-1'>
                  Your account has been suspended due to payment issues. Please
                  update your payment method to restore access.
                </p>
                {onResolveDelinquency && (
                  <Button
                    onClick={onResolveDelinquency}
                    className='mt-2 bg-destructive hover:bg-destructive/90'
                    size='sm'
                  >
                    Restore Access
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className='flex flex-wrap gap-2'>
        {subscription.status === 'Active' && (
          <>
            {onUpdatePayment && (
              <Button
                variant='outline'
                onClick={onUpdatePayment}
                className='flex-1'
              >
                <CreditCard className='mr-2 h-4 w-4' />
                Update Payment
              </Button>
            )}

            {onCancelSubscription && (
              <Button
                variant='destructive'
                onClick={onCancelSubscription}
                className='flex-1'
              >
                <AlertCircle className='mr-2 h-4 w-4' />
                Cancel Subscription
              </Button>
            )}
          </>
        )}
      </CardFooter>
    </Card>
  );
}
