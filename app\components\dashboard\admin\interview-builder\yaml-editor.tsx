'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Code,
  Save,
  Eye,
  FileText,
  Download,
  Upload,
  Copy,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { YAMLInterviewParser } from '@/lib/yaml-interview-parser';
import { SAMPLE_INTERVIEWS } from '@/lib/sample-yaml-interviews';

interface YAMLEditorProps {
  yamlContent: string;
  onYAMLChange: (yaml: string) => void;
  onPreview?: () => void;
}

export function YAMLEditor({
  yamlContent,
  onYAMLChange,
  onPreview,
}: YAMLEditorProps) {
  const [isValid, setIsValid] = useState(true);
  const [parseError, setParseError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const validateYAML = (yaml: string) => {
    try {
      if (yaml.trim()) {
        YAMLInterviewParser.parseYAML(yaml);
      }
      setIsValid(true);
      setParseError(null);
    } catch (error) {
      setIsValid(false);
      setParseError(error instanceof Error ? error.message : 'Invalid YAML');
    }
  };

  const handleYAMLChange = (value: string) => {
    onYAMLChange(value);
    validateYAML(value);
  };

  const loadSampleInterview = (sampleKey: keyof typeof SAMPLE_INTERVIEWS) => {
    const sampleYAML = SAMPLE_INTERVIEWS[sampleKey];
    handleYAMLChange(sampleYAML);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(yamlContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const downloadYAML = () => {
    const blob = new Blob([yamlContent], { type: 'text/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'interview.yml';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target?.result as string;
        handleYAMLChange(content);
      };
      reader.readAsText(file);
    }
  };

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            <span className='flex items-center'>
              <Code className='mr-2 h-5 w-5' />
              YAML Interview Editor
            </span>
            <div className='flex items-center space-x-2'>
              {isValid ? (
                <Badge
                  variant='default'
                  className='bg-green-100 text-green-800'
                >
                  <CheckCircle className='mr-1 h-3 w-3' />
                  Valid
                </Badge>
              ) : (
                <Badge variant='destructive'>
                  <AlertCircle className='mr-1 h-3 w-3' />
                  Invalid
                </Badge>
              )}
              <Button onClick={onPreview} size='sm' variant='outline'>
                <Eye className='mr-2 h-4 w-4' />
                Preview
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Edit your interview in DocAssemble-compatible YAML format
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue='editor' className='w-full'>
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='editor'>Editor</TabsTrigger>
              <TabsTrigger value='samples'>Sample Interviews</TabsTrigger>
              <TabsTrigger value='actions'>Actions</TabsTrigger>
            </TabsList>

            <TabsContent value='editor' className='space-y-4'>
              <div className='space-y-2'>
                <div className='flex items-center justify-between'>
                  <label className='text-sm font-medium'>YAML Content</label>
                  <div className='flex items-center space-x-2'>
                    <Button onClick={copyToClipboard} size='sm' variant='ghost'>
                      <Copy className='mr-1 h-3 w-3' />
                      {copied ? 'Copied!' : 'Copy'}
                    </Button>
                  </div>
                </div>
                <Textarea
                  value={yamlContent}
                  onChange={e => handleYAMLChange(e.target.value)}
                  placeholder='Enter your YAML interview content here...'
                  className='min-h-[400px] font-mono text-sm'
                />
                {parseError && (
                  <div className='text-sm text-red-600 bg-red-50 p-3 rounded-md'>
                    <strong>Parse Error:</strong> {parseError}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value='samples' className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <Card
                  className='cursor-pointer hover:shadow-md transition-shadow'
                  onClick={() => loadSampleInterview('basic')}
                >
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm'>
                      Basic Estate Planning
                    </CardTitle>
                    <CardDescription className='text-xs'>
                      Simple linear interview with basic questions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <Badge variant='outline' className='text-xs'>
                      4 questions
                    </Badge>
                  </CardContent>
                </Card>

                <Card
                  className='cursor-pointer hover:shadow-md transition-shadow'
                  onClick={() => loadSampleInterview('complex')}
                >
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm'>
                      Complex Conditional
                    </CardTitle>
                    <CardDescription className='text-xs'>
                      Advanced interview with branching logic and conditions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <Badge variant='outline' className='text-xs'>
                      15+ questions
                    </Badge>
                  </CardContent>
                </Card>

                <Card
                  className='cursor-pointer hover:shadow-md transition-shadow'
                  onClick={() => loadSampleInterview('business')}
                >
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm'>
                      Business Succession
                    </CardTitle>
                    <CardDescription className='text-xs'>
                      Complex business planning with multiple decision points
                    </CardDescription>
                  </CardHeader>
                  <CardContent className='pt-0'>
                    <Badge variant='outline' className='text-xs'>
                      20+ questions
                    </Badge>
                  </CardContent>
                </Card>
              </div>

              <div className='text-sm text-[var(--custom-gray-medium)] bg-blue-50 p-3 rounded-md'>
                <strong>Tip:</strong> Click on any sample interview above to
                load it into the editor. You can then modify it to suit your
                needs.
              </div>
            </TabsContent>

            <TabsContent value='actions' className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div className='space-y-3'>
                  <h4 className='font-medium'>Import/Export</h4>
                  <div className='space-y-2'>
                    <div>
                      <input
                        type='file'
                        accept='.yml,.yaml'
                        onChange={handleFileUpload}
                        className='hidden'
                        id='yaml-upload'
                      />
                      <Button
                        onClick={() =>
                          document.getElementById('yaml-upload')?.click()
                        }
                        variant='outline'
                        className='w-full justify-start'
                      >
                        <Upload className='mr-2 h-4 w-4' />
                        Upload YAML File
                      </Button>
                    </div>
                    <Button
                      onClick={downloadYAML}
                      variant='outline'
                      className='w-full justify-start'
                      disabled={!yamlContent.trim()}
                    >
                      <Download className='mr-2 h-4 w-4' />
                      Download YAML
                    </Button>
                  </div>
                </div>

                <div className='space-y-3'>
                  <h4 className='font-medium'>Validation</h4>
                  <div className='space-y-2'>
                    <div
                      className={`p-3 rounded-md ${isValid ? 'bg-green-50' : 'bg-red-50'}`}
                    >
                      <div className='flex items-center'>
                        {isValid ? (
                          <CheckCircle className='h-4 w-4 text-green-600 mr-2' />
                        ) : (
                          <AlertCircle className='h-4 w-4 text-red-600 mr-2' />
                        )}
                        <span
                          className={`text-sm font-medium ${isValid ? 'text-green-800' : 'text-red-800'}`}
                        >
                          {isValid ? 'YAML is valid' : 'YAML has errors'}
                        </span>
                      </div>
                      {parseError && (
                        <p className='text-xs text-red-600 mt-1'>
                          {parseError}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className='text-sm text-[var(--custom-gray-medium)] bg-yellow-50 p-3 rounded-md'>
                <strong>YAML Tips:</strong>
                <ul className='mt-2 space-y-1 text-xs'>
                  <li>• Use proper indentation (2 spaces recommended)</li>
                  <li>• Separate documents with "---"</li>
                  <li>• Use "show if:" for conditional questions</li>
                  <li>• Set "required: True" for mandatory fields</li>
                  <li>• Use "datatype:" to specify field types</li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
