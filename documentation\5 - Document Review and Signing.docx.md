# 5 \- Document Review and Signing

# **Document 5 \- Functional Specification: Document Review and Signing**

## **Overview**

The Document Review and Signing process enables Members of the Childfree Legacy Web Application to review, finalize, and legally execute estate planning documents—such as wills, trusts, and powers of attorney (POAs)—in a secure, compliant, and user-friendly manner. Leveraging AWS Amplify, this specification outlines the steps for reviewing generated documents, optionally requesting attorney reviews, supporting both electronic and manual signing methods, integrating with online notarization services where required by state law, and uploading manually signed documents (handled by Welon Trust). Tailored for the 65+ demographic, it prioritizes accessibility, legal compliance, and trust. AWS Amplify’s serverless architecture, data storage, and encryption features ensure a scalable, secure, and intuitive experience.

---

## **1\. Document Review**

The document review process allows Members to verify the accuracy and completeness of their generated estate planning documents before proceeding to signing.

### **Key Requirements**

#### **Document Types**

- Wills, trusts, POAs, advance healthcare directives, and related estate planning documents.

#### **Review Features**

- Display documents in a readable PDF format with zoom, scrolling, and page navigation.
- Highlight critical sections (e.g., beneficiaries, asset distribution) for quick reference.

#### **Accessibility**

- Implement large text, high-contrast visuals, and screen reader compatibility for users aged 65+.

#### **Educational Content**

- Offer tooltips or short explainer videos for legal terms (e.g., "What is an executor?").

### **User Flow**

1. Post-generation, the Member navigates to /member/documents/review.
2. The system verifies payment via **AWS AppSync** and initiates the billing cycle upon first document generation.
3. Documents are retrieved from **Amazon S3** and displayed in an embedded PDF viewer with tools like page thumbnails and a search bar.
4. Members can zoom, highlight sections, or add notes within the viewer.
5. Educational content is accessible via clickable icons next to key terms or sections.
6. Members can opt for an attorney review by clicking "Request Attorney Review" or proceed to signing with "Proceed to Signing."

### **Edge Cases**

- **Incomplete Documents**: Prompt Members to fill mandatory fields before review.
- **Viewer Failure**: Provide a "Download PDF" fallback option if the embedded viewer fails.
- **Multiple Documents**: Enable batch review for related documents (e.g., a will and a trust).

### **Compliance Considerations**

- **Data Integrity**: Use **AWS S3 versioning** to ensure the displayed document matches the generated version.
- **Audit Trails**: Log access and review timestamps in **Amazon CloudWatch** for accountability.

### **UI Components**

| Element                   | Description                                |
| ------------------------- | ------------------------------------------ |
| PDF Viewer                | Embedded viewer with zoom and navigation   |
| Annotation Tools          | Options to highlight or add comments       |
| "Request Attorney Review" | Button to request optional attorney review |
| "Proceed to Signing"      | Button to advance to the signing phase     |
| Educational Icons         | Icons linking to legal term explanations   |

---

## **2\. Attorney Review**

Members may opt for an attorney to review their watermarked estate planning documents. The platform provides a state-specific list of preferred attorneys, but the review process is external, with no attorney access to the system.

### **Key Features**

#### **Attorney List**

- Displays state-licensed preferred attorneys (name, phone number, email) stored in **Amazon DynamoDB**.

#### **External Review**

- Members contact attorneys directly and handle the review process offline.

#### **Next Steps**

- Post-review (or if skipped), Members return to the platform and select "Proceed to Signing."

### **How It Works**

From /member/documents/review, Members choose one of three options:

1. **Request a Preferred Attorney Review (Included)**:
   - Navigate to /member/attorney-list to view state-licensed attorneys.
   - Select an attorney and contact them externally.
   - Receive instructions for review and platform return.
   - Post-review, return and click "Proceed to Signing." The request is logged via **AWS AppSync** and displayed on the dashboard.
2. **External Attorney Review**:
   - Arrange review with a non-listed attorney.
   - Follow provided instructions to return and click "Proceed to Signing."
3. **No Attorney Review**:
   - Skip review and click "Proceed to Signing" directly.

### **Special Cases**

- **No Attorneys Listed**: Display: "No attorneys available for your state. Proceed to signing or contact support."

### **Notes**

- Attorney licensing verification is the Member’s responsibility.
- The platform does not track external review progress or require attorney logins.

### **Service Level Agreements (SLAs) and Billing**

- **SLAs**: Preferred attorney reviews target completion within 5 business days, subject to availability.
- **Billing**: Included in membership for preferred attorneys; external reviews are Member-funded.

### **Interface Elements**

| Element                   | Purpose                                   |
| ------------------------- | ----------------------------------------- |
| "Request Attorney Review" | Button to access the attorney list        |
| Attorney List             | Displays names, phone numbers, and emails |
| "Proceed to Signing"      | Button to advance to signing              |

---

## **3\. Signing Workflow**

The signing workflow ensures secure, legal execution of estate planning documents, with two scenarios: Scenario 1 (MVP) for the current process and Scenario 2 (Future Phase) for planned enhancements.

### **Scenario 1 (MVP – Minimum Viable Product)**

Members print, notarize, and mail documents to Welon Trust using prepaid UPS labels, separating review and execution to prevent redundant label generation.

#### **Key Steps and Instructions**

- **Document Review and Preparation**:
  - Navigate to /member/documents/sign post-generation.
  - Options:
    - "Download to Review": Downloads the document (no label/instructions). _Note: Use this to preview your document._
    - "Ready to Print, Execute, Notarize & Ship": Generates the execution package. _Note: Use this when ready to finalize._
- **Generating the Execution Package**:
  - Selecting "Ready to Print, Execute, Notarize & Ship" triggers an **AWS Lambda** function to create a multi-page PDF in **S3**:
    - Page 1: Signing, notarization, and mailing instructions.
    - Page 2: Prepaid UPS shipping label.
    - Page 3+: Estate planning document(s).
- **Printing the Document**:
  - Print on letter-sized, single-sided paper.
- **Notarization**:
  - Take to a notary public, guided by instructions with a locator link and checklist.
- **Mailing the Notarized Document**:
  - Use the UPS label with step-by-step mailing guidance.
- **Receipt and Verification**:
  - Receive email updates via **AWS SES** for shipped, received, and verified ("Approved" or "Declined") statuses.
  - If declined, emails detail reasons and correction steps.

#### **Logistics and Considerations**

- **Printing Access**: Recommend libraries or print shops for printer-less Members.
- **Notarization Fees**: $5–$15, Member-paid.
- **Shipping**: Prepaid label covers standard shipping; upgrades are Member-funded.

#### **Edge Cases**

- **Incomplete Notarization**: Welon notifies Member to resend.
- **Lost Shipments**: Reprint and resend with a new label.
- **Witness Requirements**: Include templates and instructions for applicable states.

#### **Compliance Considerations**

- **State-Specific Rules**: Supports physical signatures and notarization per state laws.
- **Audit Trails**: Log downloads, package generation, and receipt in **CloudWatch**.

#### **UI Components**

| Element                                    | Description                                    |
| ------------------------------------------ | ---------------------------------------------- |
| "Download to Review"                       | Downloads document without label/instructions  |
| "Ready to Print, Execute, Notarize & Ship" | Generates full execution package               |
| Notary Guidance                            | Instructions with locator links and checklists |
| Mailing Instructions                       | Step-by-step mailing guidance                  |
| "Track Shipment"                           | UPS tracking link                              |
| Status Update                              | Confirmation of receipt and verification       |

---

## **4\. Document Upload (Manual Signing)**

Welon staff, under the Welon-Basic role, upload signed documents post-receipt, ensuring secure processing and storage.

### **Key Requirements**

- **File Formats**: PDF, JPEG, or PNG.
- **Validation**: Manual staff review.
- **Storage**: Encrypted in **Amazon S3**, linked to Member profiles in **DynamoDB**.

### **User Flow (Welon-Basic)**

1. Welon receives signed documents.
2. Staff review and approve externally.
3. Digitize into PDF, JPEG, or PNG with standardized naming.
4. Access /staff/documents/upload.
5. Upload via drag-and-drop or file selection.
6. **AWS Lambda** validates file type, size, name, and legibility.
7. On success:
   - Mark as "Approved."
   - Store in **S3**, link to **DynamoDB**.
   - Notify Member via **AWS SES** with a view link.
8. On failure (e.g., missing signatures):
   - Staff flag the issue.
   - Member is contacted by Welon Trust and emailed correction steps via **SES**.

### **Edge Cases**

- **Invalid File Type**: Display error to staff.
- **Missing Signatures/Notarization**: Flag and notify Member.

### **Handling Document Rejection by Welon**

- **MVP Requirements**:
  - Notify Member via email and in-app alert with rejection reason (e.g., "Missing notarization") and correction steps.
  - Allow resubmission at /member/documents/update.
  - Track rejections at /admin/users with status "Rejected \- \[Reason\]."
- **Future State**:
  - Automate issue detection (e.g., missing signatures) pre-submission.
  - Enable real-time Welon feedback integration.

### **Compliance Considerations**

- **Data Integrity**: Verify authenticity with checksums.
- **Retention**: Adhere to legal storage requirements.

### **UI Components (Staff Interface)**

| Element            | Description                        |
| ------------------ | ---------------------------------- |
| Drag-and-Drop Area | For staff file uploads             |
| File Preview       | Displays document for verification |
| "Validate" Button  | Initiates validation process       |
| Error Message      | Flags file or validation issues    |

---

## **5\. User Dashboard for Document Processing**

The dashboard offers Members a centralized view to track estate planning document statuses, designed for transparency and usability for the 65+ demographic.

### **Key Requirements**

#### **Document Status Tracking**

- **Package Shipped**: Shows a UPS tracking link.
- **Received by Welon**: Displays receipt date.
- **Approved**: Confirms approval and upload to Member’s record.

#### **Accessibility**

- Large text, high-contrast visuals, and screen reader support.

#### **Real-Time Updates**

- Reflect status changes via **AWS AppSync subscriptions**.

#### **Notifications**

- Integrate with the Notifications System for status alerts (e.g., "Package shipped").

### **User Flow**

1. Log in and visit /member/dashboard.
2. View a table of documents with columns: Document Name, Status, Details.
3. Status updates:
   - "Package Shipped": UPS tracking link in Details.
   - "Received by Welon": Receipt date in Details.
   - "Approved/Uploaded": View link in Details.
4. Receive in-app and email notifications via **SES** with dashboard links.

### **Edge Cases**

- **Delayed Updates**: Show last status with "Awaiting update from Welon Trust."
- **Lost Shipments**: Display "Shipment Issue" with support link.
- **Multiple Documents**: Support pagination/filtering for clarity.

### **Compliance Considerations**

- **HIPAA**: Encrypt health-related document statuses (e.g., Medical POA) with **AWS KMS**.
- **SOC2**: Log updates and interactions in **CloudWatch**.

### **UI Components**

| Element               | Description                               |
| --------------------- | ----------------------------------------- |
| Document Status Table | Lists documents: Name, Status, Details    |
| UPS Tracking Link     | Tracks shipment via UPS                   |
| Status Indicator      | Visual cues (e.g., "Shipped," "Approved") |
| View Document Link    | Access approved document                  |
| Support Link          | Contact support for issues                |

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **Amplify Hosting**.
- **Routes**:
  - /member/documents/review: Document review.
  - /member/attorney-list: Attorney list.
  - /member/documents/sign: Signing options.
  - /staff/documents/upload: Staff upload interface.
  - /member/dashboard: Document status dashboard.

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - getDocumentReview: Fetch document for review.
  - requestAttorneyReview: Log attorney review request.
  - generateSigningPackage: Create signing package PDF.
  - uploadSignedDocument: Upload and validate signed documents.
  - getDocumentStatus: Fetch document status for dashboard.
- **Encryption**: TLS/SSL for data in transit.

### **Database (DynamoDB)**

- **Table: documents**
  - Columns: id, user_id, type, status, version, file_url, signature_type, notarization_required, execution_date
- **Table: attorney_reviews**
  - Columns: id, document_id, attorney_id, comments, status
- **Table: document_status**
  - Columns: id, document_id, member_id, status, tracking_link, receipt_date, approval_date, updated_at

### **Logging**

- Track views, attorney interactions, signing events, uploads, and status updates in **CloudWatch**.

---

## **7\. Testing and Validation**

- **Unit Tests**: Validate signing and upload logic.
- **Integration Tests**: Test end-to-end workflows.
- **Compliance Testing**: Verify state-specific compliance.
- **UAT**: Ensure usability for 65+ users.

### **Test Cases**

| Scenario                | Expected Outcome                                  |
| ----------------------- | ------------------------------------------------- |
| Document Review         | Member views and annotates document               |
| Attorney Review Request | Attorney list displayed; Member contacts attorney |
| Manual Upload (Staff)   | Staff upload succeeds, system stores document     |
| Dashboard Status Update | Real-time status reflected on dashboard           |
| Lost Shipment Handling  | "Shipment Issue" shown with support link          |

---

## **8\. Compliance and Security**

- **HIPAA**: Encrypt health-related documents (e.g., advance healthcare directives) with **AWS KMS**.
- **SOC2**: Implement access controls via **Cognito** and audit logs in **CloudWatch**.
- **State Laws**: Ensure signing/notarization complies with local regulations.
- **Data Integrity**: Use checksums for authenticity verification.

---

## **Summary**

This specification provides a secure, accessible, and compliant process for Members to review, refine, and execute estate planning documents. The attorney review workflow offers transparency with defined SLAs and billing. Flexible signing options and notarization services ensure legal accuracy and trust for the 65+ demographic. The User Dashboard delivers real-time visibility into the document lifecycle, from shipment to approval, leveraging AWS Amplify’s robust features.
