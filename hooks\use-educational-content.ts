/**
 * useEducationalContent Hook
 *
 * Custom React hook for managing educational content.
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import {
  Content,
  ContentType,
  ContentFilters,
  ContentAnalytics,
  ContentFeedback,
  ContentIntegrationPoint,
  ContentStatus,
} from '../app/types/education';
import {
  mockEducationalContent,
  getContentById,
  getContentByType,
  getContentByTags,
  getContentAnalytics,
  getContentForIntegrationPoint,
} from '../app/mock/educational-content';

interface UseEducationalContentReturn {
  // Content retrieval
  allContent: Content[];
  contentById: (id: string) => Content | undefined;
  contentByType: (type: ContentType) => Content[];
  contentByTags: (tags: string[]) => Content[];
  contentForIntegrationPoint: (point: ContentIntegrationPoint) => Content[];
  filteredContent: Content[];

  // Content filtering
  filters: ContentFilters;
  setFilters: (filters: ContentFilters) => void;

  // Content analytics
  contentAnalytics: (contentId: string) => ContentAnalytics | undefined;
  trackContentView: (contentId: string) => void;
  submitContentFeedback: (
    contentId: string,
    rating: number,
    comment?: string
  ) => void;

  // Loading states
  isLoading: boolean;
  error: string | null;
}

export function useEducationalContent(): UseEducationalContentReturn {
  const [content, setContent] = useState<Content[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [filteredContent, setFilteredContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize content
  useEffect(() => {
    try {
      // Simulate API call
      setTimeout(() => {
        setContent(mockEducationalContent);
        setIsLoading(false);
      }, 500);
    } catch (err) {
      setError('Failed to load educational content');
      setIsLoading(false);
    }
  }, []);

  // Apply filters when content or filters change
  useEffect(() => {
    if (!content.length) return;

    let filtered = [...content];

    // Filter by type
    if (filters.type) {
      filtered = filtered.filter(item => item.type === filters.type);
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(item =>
        item.tags.some(tag => filters.tags?.includes(tag))
      );
    }

    // Filter by search term
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.title.toLowerCase().includes(searchLower) ||
          item.description.toLowerCase().includes(searchLower)
      );
    }

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(item => item.status === filters.status);
    }

    setFilteredContent(filtered);
  }, [content, filters]);

  // Get content by ID
  const contentById = useCallback((id: string): Content | undefined => {
    return getContentById(id);
  }, []);

  // Get content by type
  const contentByType = useCallback((type: ContentType): Content[] => {
    return getContentByType(type);
  }, []);

  // Get content by tags
  const contentByTags = useCallback((tags: string[]): Content[] => {
    return getContentByTags(tags);
  }, []);

  // Get content for integration point
  const contentForIntegrationPoint = useCallback(
    (point: ContentIntegrationPoint): Content[] => {
      return getContentForIntegrationPoint(point);
    },
    []
  );

  // Get content analytics
  const contentAnalytics = useCallback(
    (contentId: string): ContentAnalytics | undefined => {
      return getContentAnalytics(contentId);
    },
    []
  );

  // Track content view
  const trackContentView = useCallback((contentId: string): void => {
    console.log(`Tracked view for content: ${contentId}`);
    // In a real implementation, this would call an API to track the view
  }, []);

  // Submit content feedback
  const submitContentFeedback = useCallback(
    (contentId: string, rating: number, comment?: string): void => {
      console.log(
        `Feedback for ${contentId}: ${rating} stars, comment: ${comment || 'none'}`
      );
      // In a real implementation, this would call an API to submit feedback
    },
    []
  );

  return {
    allContent: content,
    contentById,
    contentByType,
    contentByTags,
    contentForIntegrationPoint,
    filteredContent,
    filters,
    setFilters,
    contentAnalytics,
    trackContentView,
    submitContentFeedback,
    isLoading,
    error,
  };
}
