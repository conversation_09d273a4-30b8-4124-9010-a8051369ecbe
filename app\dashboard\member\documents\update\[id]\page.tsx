'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DocumentUpdateForm,
  DocumentSection,
} from '@/components/documents/document-update-form';
import {
  Headline,
  Subhead,
} from '../../../../../../components/ui/brand/typography';
import { AlertCircle, ArrowLeft } from 'lucide-react';

// Mock data for document sections
const getMockDocumentSections = (documentType: string): DocumentSection[] => {
  if (documentType === 'Will') {
    return [
      {
        id: 'personal-info',
        title: 'Personal Information',
        description: 'Your basic personal details',
        fields: [
          {
            id: 'full-name',
            label: 'Full Legal Name',
            type: 'text',
            value: '<PERSON>',
            validation: {
              required: true,
              message: 'Full legal name is required',
            },
          },
          {
            id: 'address',
            label: 'Current Address',
            type: 'textarea',
            value: '123 Main St, Anytown, CA 90210',
            validation: {
              required: true,
              message: 'Current address is required',
            },
          },
          {
            id: 'phone',
            label: 'Phone Number',
            type: 'text',
            value: '(*************',
            validation: {
              required: true,
              pattern: /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/,
              message: 'Valid phone number is required',
            },
          },
        ],
      },
      {
        id: 'beneficiaries',
        title: 'Beneficiaries',
        description: 'People who will inherit your assets',
        fields: [
          {
            id: 'primary-beneficiary',
            label: 'Primary Beneficiary',
            type: 'text',
            value: 'John Doe',
            validation: {
              required: true,
              message: 'Primary beneficiary is required',
            },
          },
          {
            id: 'primary-relationship',
            label: 'Relationship to Primary',
            type: 'select',
            value: 'Friend',
            options: [
              { value: 'Spouse', label: 'Spouse' },
              { value: 'Partner', label: 'Partner' },
              { value: 'Sibling', label: 'Sibling' },
              { value: 'Parent', label: 'Parent' },
              { value: 'Friend', label: 'Friend' },
              { value: 'Other', label: 'Other' },
            ],
            validation: { required: true, message: 'Relationship is required' },
          },
          {
            id: 'secondary-beneficiary',
            label: 'Secondary Beneficiary',
            type: 'text',
            value: 'Sarah Johnson',
            validation: { required: false },
          },
          {
            id: 'secondary-relationship',
            label: 'Relationship to Secondary',
            type: 'select',
            value: 'Sibling',
            options: [
              { value: 'Spouse', label: 'Spouse' },
              { value: 'Partner', label: 'Partner' },
              { value: 'Sibling', label: 'Sibling' },
              { value: 'Parent', label: 'Parent' },
              { value: 'Friend', label: 'Friend' },
              { value: 'Other', label: 'Other' },
            ],
            validation: { required: false },
          },
        ],
      },
      {
        id: 'assets',
        title: 'Assets',
        description: 'Your major assets and how they should be distributed',
        fields: [
          {
            id: 'real-estate',
            label: 'Real Estate',
            type: 'textarea',
            value: 'House at 123 Main St, Anytown, CA 90210',
            validation: { required: false },
          },
          {
            id: 'financial-accounts',
            label: 'Financial Accounts',
            type: 'textarea',
            value:
              'Checking account #1234 at First Bank\nSavings account #5678 at First Bank',
            validation: { required: false },
          },
          {
            id: 'personal-property',
            label: 'Personal Property',
            type: 'textarea',
            value:
              'Art collection to be donated to Local Art Museum\nJewelry to be given to Sarah Johnson',
            validation: { required: false },
          },
        ],
      },
    ];
  } else if (documentType === 'Power of Attorney') {
    return [
      {
        id: 'personal-info',
        title: 'Personal Information',
        description: 'Your basic personal details',
        fields: [
          {
            id: 'full-name',
            label: 'Full Legal Name',
            type: 'text',
            value: 'Jane Smith',
            validation: {
              required: true,
              message: 'Full legal name is required',
            },
          },
          {
            id: 'address',
            label: 'Current Address',
            type: 'textarea',
            value: '123 Main St, Anytown, CA 90210',
            validation: {
              required: true,
              message: 'Current address is required',
            },
          },
        ],
      },
      {
        id: 'attorney-info',
        title: 'Attorney-in-Fact',
        description: 'Person who will make decisions on your behalf',
        fields: [
          {
            id: 'attorney-name',
            label: 'Attorney-in-Fact Name',
            type: 'text',
            value: 'Robert Brown',
            validation: {
              required: true,
              message: 'Attorney-in-Fact name is required',
            },
          },
          {
            id: 'attorney-relationship',
            label: 'Relationship',
            type: 'select',
            value: 'Friend',
            options: [
              { value: 'Spouse', label: 'Spouse' },
              { value: 'Partner', label: 'Partner' },
              { value: 'Sibling', label: 'Sibling' },
              { value: 'Parent', label: 'Parent' },
              { value: 'Friend', label: 'Friend' },
              { value: 'Other', label: 'Other' },
            ],
            validation: { required: true, message: 'Relationship is required' },
          },
          {
            id: 'attorney-address',
            label: 'Address',
            type: 'textarea',
            value: '456 Oak St, Anytown, CA 90210',
            validation: { required: true, message: 'Address is required' },
          },
          {
            id: 'attorney-phone',
            label: 'Phone Number',
            type: 'text',
            value: '(*************',
            validation: {
              required: true,
              pattern: /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/,
              message: 'Valid phone number is required',
            },
          },
        ],
      },
      {
        id: 'powers',
        title: 'Powers Granted',
        description:
          'Specific powers you are granting to your attorney-in-fact',
        fields: [
          {
            id: 'financial-powers',
            label: 'Financial Powers',
            type: 'textarea',
            value:
              'Authority to manage bank accounts\nAuthority to pay bills and taxes\nAuthority to manage investments',
            validation: {
              required: true,
              message: 'Financial powers description is required',
            },
          },
          {
            id: 'property-powers',
            label: 'Property Powers',
            type: 'textarea',
            value:
              'Authority to manage real estate\nAuthority to sell property if necessary',
            validation: { required: false },
          },
          {
            id: 'limitations',
            label: 'Limitations',
            type: 'textarea',
            value:
              'No authority to make gifts exceeding $1,000 per year\nNo authority to change beneficiary designations',
            validation: { required: false },
          },
        ],
      },
    ];
  } else {
    // Default sections for other document types
    return [
      {
        id: 'general-info',
        title: 'General Information',
        description: 'Basic information about this document',
        fields: [
          {
            id: 'title',
            label: 'Document Title',
            type: 'text',
            value: documentType,
            validation: {
              required: true,
              message: 'Document title is required',
            },
          },
          {
            id: 'description',
            label: 'Description',
            type: 'textarea',
            value:
              'This document contains important information that should be reviewed regularly.',
            validation: { required: true, message: 'Description is required' },
          },
        ],
      },
    ];
  }
};

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function DocumentUpdatePage({ params }: PageProps) {
  const router = useRouter();
  const { id } = await params;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [documentType, setDocumentType] = useState<string>('');
  const [sections, setSections] = useState<DocumentSection[]>([]);

  useEffect(() => {
    // Simulate API call to fetch document data
    const fetchDocument = async () => {
      try {
        // In a real implementation, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock document data based on ID
        const mockDocumentType = id.includes('will')
          ? 'Will'
          : id.includes('poa')
            ? 'Power of Attorney'
            : 'Living Trust';

        setDocumentType(mockDocumentType);
        setSections(getMockDocumentSections(mockDocumentType));
        setLoading(false);
      } catch (err) {
        setError('Failed to load document. Please try again.');
        setLoading(false);
      }
    };

    fetchDocument();
  }, [id]);

  const handleSave = (
    documentId: string,
    updatedSections: DocumentSection[]
  ) => {
    // In a real implementation, this would call an API to save the document
    console.log('Saving document:', documentId, updatedSections);
    // The form component already handles navigation after save
  };

  const handleBack = () => {
    router.push('/dashboard/member/documents/history');
  };

  if (loading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-8'>
            <Skeleton className='h-10 w-1/3 mb-2' />
            <Skeleton className='h-5 w-1/2' />
          </div>
          <Skeleton className='h-[600px] w-full rounded-md' />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-8'>
            <Headline className='mb-2'>Update Document</Headline>
            <Subhead className='text-muted-foreground'>
              Review and update your document information
            </Subhead>
          </div>

          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <Button variant='outline' onClick={handleBack}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Update {documentType}</Headline>
          <Subhead className='text-muted-foreground'>
            Review and update your document information to keep it current
          </Subhead>
        </div>

        <DocumentUpdateForm
          documentId={id}
          documentType={documentType}
          sections={sections}
          onSave={handleSave}
        />

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100 mt-8'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Document Updates
          </h3>
          <p className='text-blue-700 mb-4'>
            Regular updates help ensure your estate planning documents contain
            accurate and up-to-date information.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>Each update creates a new version of your document</li>
            <li>
              Previous versions are archived and can be accessed if needed
            </li>
            <li>The system tracks when each document was last updated</li>
            <li>
              You'll receive reminders when it's time to review your documents
              again
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
