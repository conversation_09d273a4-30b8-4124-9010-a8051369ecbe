import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

export interface NotificationData {
  id: string;
  message: string;
  recipient: string;
  createdAt: string;
  isRead: boolean;
}

export async function createNotification(message: string, recipient: string, author?: string): Promise<NotificationData> {
  const { getCurrentUser } = await import('aws-amplify/auth');
  const currentUser = await getCurrentUser();

  const { data, errors } = await client.models.Notification.create({
    message,
    recipient,
    author: author || currentUser.userId,
    createdAt: new Date().toISOString(),
    isRead: false,
  });

  if (errors) throw new Error(errors[0].message);
  return data as NotificationData;
}

export async function getNotifications(recipient: string): Promise<NotificationData[]> {
  console.log('API: Getting notifications for recipient:', recipient);
  // Authorization automatically filters by recipient, no need for manual filter
  const { data, errors } = await client.models.Notification.list();

  console.log('API: Notification query result:', { data, errors });
  if (errors) throw new Error(errors[0].message);

  // Sort by createdAt descending (newest first)
  const sortedData = (data as NotificationData[]).sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  return sortedData;
}

export async function markAsRead(id: string): Promise<void> {
  const { errors } = await client.models.Notification.update({
    id,
    isRead: true,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function deleteNotification(id: string): Promise<void> {
  console.log('API: Deleting notification:', id);
  const { errors } = await client.models.Notification.delete({
    id,
  });

  if (errors) throw new Error(errors[0].message);
}

export async function deleteAllNotifications(): Promise<void> {
  console.log('API: Deleting all notifications for current user');
  const notifications = await getNotifications('');

  for (const notification of notifications) {
    await deleteNotification(notification.id);
  }
}

export function subscribeToNotifications(
  recipient: string,
  onNotification: (notification: NotificationData) => void
) {
  console.log('Setting up subscription for recipient:', recipient);
  // Authorization automatically filters by recipient, no need for manual filter
  return client.models.Notification.onCreate().subscribe({
    next: (data) => {
      if (data) {
        console.log('Subscription: Received notification:', data);
        onNotification(data as NotificationData);
      }
    },
    error: (error) => console.error('Subscription error:', error),
  });
}