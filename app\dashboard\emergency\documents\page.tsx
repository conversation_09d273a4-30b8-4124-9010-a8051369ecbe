'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { DocumentAccess } from '@/components/emergency/document-access';
import { VerificationForm } from '@/components/emergency/verification-form';
import { Document, AccessTrigger } from '@/components/emergency/types';
import { AlertCircle, CheckCircle2, ShieldAlert, FileText } from 'lucide-react';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';

// Mock data for demonstration
const mockDocuments: Document[] = [
  {
    id: '1',
    userId: 'user123',
    title: 'Last Will and Testament',
    type: 'Legal Document',
    url: '/documents/will.pdf',
    createdAt: '2023-01-15T12:00:00Z',
    updatedAt: '2023-06-20T14:30:00Z',
  },
  {
    id: '2',
    userId: 'user123',
    title: 'Healthcare Directive',
    type: 'Medical Document',
    url: '/documents/healthcare.pdf',
    createdAt: '2023-02-10T09:15:00Z',
    updatedAt: '2023-05-05T11:45:00Z',
  },
  {
    id: '3',
    userId: 'user123',
    title: 'Power of Attorney',
    type: 'Legal Document',
    url: '/documents/poa.pdf',
    createdAt: '2023-03-22T16:20:00Z',
    updatedAt: '2023-03-22T16:20:00Z',
  },
];

export default function EmergencyDocumentsPage() {
  const [isVerified, setIsVerified] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [accessType, setAccessType] = useState<AccessTrigger>('Incapacitation');
  const [expiryDate, setExpiryDate] = useState<string | undefined>(
    new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString() // 180 days from now
  );
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const handleVerify = (code: string) => {
    // In a real implementation, this would call an API to verify the code
    if (code === '123456') {
      // Mock verification
      setIsVerified(true);
      setDocuments(mockDocuments);
      setAlert({
        type: 'success',
        message:
          'Verification successful. You now have access to the documents.',
      });
    } else {
      setAlert({
        type: 'error',
        message: 'Invalid verification code. Please try again.',
      });
    }

    // Clear alert after 5 seconds
    setTimeout(() => setAlert(null), 5000);
  };

  const handleResendCode = () => {
    // In a real implementation, this would call an API to resend the code
    setAlert({
      type: 'success',
      message: 'A new verification code has been sent.',
    });

    // Clear alert after 5 seconds
    setTimeout(() => setAlert(null), 5000);
  };

  const handleViewDocument = (documentId: string) => {
    // In a real implementation, this would open the document for viewing
    const document = documents.find(doc => doc.id === documentId);
    if (document) {
      window.alert(`Viewing document: ${document.title}`);
    }
  };

  const handleDownloadDocument = (documentId: string) => {
    // In a real implementation, this would download the document
    const document = documents.find(doc => doc.id === documentId);
    if (document) {
      window.alert(`Downloading document: ${document.title}`);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-3xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2 flex items-center'>
            <ShieldAlert className='mr-3 h-8 w-8 text-blue-2157c' />
            Emergency Document Access
          </Headline>
          <Subhead className='text-muted-foreground'>
            Access critical documents in an emergency situation.
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        {!isVerified ? (
          <>
            <Card className='mb-8'>
              <CardHeader>
                <CardTitle>Secure Access</CardTitle>
                <CardDescription>
                  This page provides emergency access to important documents.
                  Verification is required.
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-start gap-4'>
                  <div className='bg-blue-2157c/10 p-2 rounded-full'>
                    <FileText className='h-6 w-6 text-blue-2157c' />
                  </div>
                  <div>
                    <h3 className='font-semibold mb-1'>Document Access</h3>
                    <p className='text-sm text-muted-foreground'>
                      You've been granted temporary access to view important
                      documents. This access is valid for a limited time.
                    </p>
                  </div>
                </div>
                <div className='flex items-start gap-4'>
                  <div className='bg-blue-2157c/10 p-2 rounded-full'>
                    <ShieldAlert className='h-6 w-6 text-blue-2157c' />
                  </div>
                  <div>
                    <h3 className='font-semibold mb-1'>
                      Security Verification
                    </h3>
                    <p className='text-sm text-muted-foreground'>
                      To protect sensitive information, please verify your
                      identity using the code sent to your email or phone.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <VerificationForm
              onVerify={handleVerify}
              onResendCode={handleResendCode}
            />
          </>
        ) : (
          <>
            <Card className='mb-8'>
              <CardHeader>
                <CardTitle>Access Information</CardTitle>
                <CardDescription>
                  You have been granted{' '}
                  {accessType === 'Incapacitation' ? 'temporary' : 'permanent'}{' '}
                  access to these documents.
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {accessType === 'Incapacitation' && expiryDate && (
                  <div className='bg-blue-2157c/10 p-4 rounded-md'>
                    <p className='font-medium'>
                      Your access is temporary and will expire on{' '}
                      {new Date(expiryDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })}
                      .
                    </p>
                  </div>
                )}
                <p className='text-sm text-muted-foreground'>
                  These documents contain sensitive information. Please handle
                  them with care and respect the privacy of the individual.
                </p>
              </CardContent>
            </Card>

            <DocumentAccess
              documents={documents}
              accessType={accessType}
              expiryDate={expiryDate}
              onView={handleViewDocument}
              onDownload={handleDownloadDocument}
            />
          </>
        )}
      </div>
    </div>
  );
}
