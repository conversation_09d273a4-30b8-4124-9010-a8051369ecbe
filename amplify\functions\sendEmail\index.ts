// // amplify/backend/function/sendEmail/index.js
// const AWS = require('aws-sdk');
// const ses = new AWS.SES();
//
// exports.handler = async (event) => {
//     const args = event.arguments.input;
//     const params = {
//         Destination: { ToAddresses: [args.to] },
//         Message: {
//             Body: { Text: { Data: args.body } },
//             Subject: { Data: args.subject },
//         },
//         Source: '<EMAIL>',
//     };
//
//     try {
//         await ses.sendEmail(params).promise();
//         return { success: true };
//     } catch (err) {
//         console.error(err);
//         return { success: false, error: err.message };
//     }
// };
