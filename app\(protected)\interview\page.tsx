'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ArrowRight, ArrowLeft, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import routes from '@/utils/routes';
import {
  getInterviewQuestions,
  getUserInterviewProgress,
  saveInterviewAnswer,
  completeInterview,
} from '@/utils/userInterviewProgress';
import { InterviewQuestion } from './components/InterviewQuestion';
import type { Schema } from '@/amplify/data/resource';
import { AuthGuard } from '@/lib/auth/auth-guard';

function InterviewPageContent() {
  const { user, refreshUser } = useAuth();
  const router = useRouter();

  const [questions, setQuestions] = useState<
    Schema['InterviewQuestion']['type'][]
  >([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userProgress, setUserProgress] = useState<
    Schema['UserInterviewProgress']['type'] | null
  >(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(true);
  const [isComplete, setIsComplete] = useState(false);

  console.log('===> QUESTION', questions);
  console.log('===> USER PROGRESS', userProgress);

  // Load questions and user progress
  useEffect(() => {
    async function loadData() {
      try {
        setIsLoadingQuestions(true);

        // Fetch questions and user progress in parallel
        const [fetchedQuestions, progress] = await Promise.all([
          getInterviewQuestions(),
          getUserInterviewProgress(),
        ]);

        setQuestions(fetchedQuestions);
        setUserProgress(progress);

        if (progress.currentQuestionId && fetchedQuestions.length > 0) {
          const index = fetchedQuestions.findIndex(
            q => q.id === progress.currentQuestionId
          );
          if (index !== -1) {
            setCurrentQuestionIndex(index);
          }
        }

        setIsComplete(progress.isComplete || false);
      } catch (error) {
        console.error('Error loading interview data:', error);
      } finally {
        setIsLoadingQuestions(false);
      }
    }

    if (user) {
      loadData();
    }
  }, [user]);

  // Calculate progress percentage
  const progressPercentage =
    questions.length > 0 ? (currentQuestionIndex / questions.length) * 100 : 0;

  // Handle answer submission
  const handleSubmitAnswer = async (
    answer: string | string[] | boolean | number
  ) => {
    if (!questions[currentQuestionIndex]) return;

    setIsSubmitting(true);

    try {
      const questionId = questions[currentQuestionIndex].id;
      const isLastQuestion = currentQuestionIndex === questions.length - 1;

      // Save the answer
      await saveInterviewAnswer(questionId, answer, isLastQuestion);

      // If this is the last question, mark the interview as complete
      if (isLastQuestion) {
        await completeInterview();
        setIsComplete(true);
      } else {
        // Move to the next question
        setCurrentQuestionIndex(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error saving answer:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle going back to the previous question
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Handle finishing the interview
  const handleFinish = () => {
    router.push(routes.dashboard);
  };

  if (isLoadingQuestions) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Loading interview...</CardTitle>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  // If the interview is complete, show a completion message
  if (isComplete) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle className='text-2xl'>Interview Complete</CardTitle>
              <CardDescription>
                Thank you for completing the interview!
              </CardDescription>
            </CardHeader>
            <CardContent className='flex flex-col items-center justify-center py-8'>
              <CheckCircle2 className='h-16 w-16 text-primary mb-4' />
              <p className='text-center text-muted-foreground'>
                Your responses have been saved. You can now return to your
                profile.
              </p>
            </CardContent>
            <CardFooter className='flex justify-center'>
              <Button onClick={handleFinish}>Return to Profile</Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  // If there are no questions, show a message
  if (questions.length === 0) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>No Questions Available</CardTitle>
              <CardDescription>
                There are currently no interview questions available.
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button onClick={() => router.push(routes.profile)}>
                Return to Profile
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];

  return (
    <div className='container mx-auto py-12 px-4'>
      <div className='flex flex-col items-center justify-center'>
        <Card className='w-full max-w-3xl'>
          <CardHeader>
            <div className='flex justify-between items-center mb-2'>
              <CardTitle>Interview Questions</CardTitle>
              <span className='text-sm text-muted-foreground'>
                Question {currentQuestionIndex + 1} of {questions.length}
              </span>
            </div>
            <Progress value={progressPercentage} className='h-2' />
          </CardHeader>

          <CardContent>
            {currentQuestion && (
              <InterviewQuestion
                question={currentQuestion}
                onSubmit={handleSubmitAnswer}
                isSubmitting={isSubmitting}
                savedAnswer={
                  userProgress?.answers &&
                  typeof userProgress.answers === 'object'
                    ? (
                        userProgress.answers as Record<
                          string,
                          string | string[] | boolean | number
                        >
                      )[currentQuestion.id]
                    : undefined
                }
              />
            )}
          </CardContent>

          <CardFooter className='flex justify-between'>
            <Button
              variant='outline'
              onClick={handlePrevious}
              disabled={currentQuestionIndex === 0 || isSubmitting}
            >
              <ArrowLeft className='mr-2 h-4 w-4' /> Previous
            </Button>

            {currentQuestionIndex < questions.length - 1 ? (
              <Button
                onClick={() => {
                  if (
                    currentQuestion &&
                    userProgress?.answers &&
                    typeof userProgress.answers === 'object' &&
                    (
                      userProgress.answers as Record<
                        string,
                        string | string[] | boolean | number
                      >
                    )[currentQuestion.id]
                  ) {
                    setCurrentQuestionIndex(prev => prev + 1);
                  }
                }}
                disabled={
                  !(
                    userProgress?.answers &&
                    typeof userProgress.answers === 'object' &&
                    (
                      userProgress.answers as Record<
                        string,
                        string | string[] | boolean | number
                      >
                    )[currentQuestion.id]
                  ) || isSubmitting
                }
              >
                Next <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            ) : (
              <Button
                onClick={handleFinish}
                disabled={
                  !(
                    userProgress?.answers &&
                    typeof userProgress.answers === 'object' &&
                    (
                      userProgress.answers as Record<
                        string,
                        string | string[] | boolean | number
                      >
                    )[currentQuestion.id]
                  ) || isSubmitting
                }
              >
                Finish
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

export default function InterviewPage() {
  return (
    <AuthGuard fallbackMessage='You need to be logged in to access the interview.'>
      <InterviewPageContent />
    </AuthGuard>
  );
}
