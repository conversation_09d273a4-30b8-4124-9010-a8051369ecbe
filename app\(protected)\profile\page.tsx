'use client';

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';

import routes from '@/utils/routes';
import { useAuth } from '@/app/context/AuthContext';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { FileText, History, ArrowRight, Shield } from 'lucide-react';
import { LivingDocumentsDashboard } from '@/components/dashboard/LivingDocumentsDashboard';
import { AuthGuard } from '@/lib/auth/auth-guard';

function ProfilePageContent() {
  const { refreshUser } = useAuth();
  // In a real implementation, this would be fetched from an API
  const documentCount = 0;

  return (
    <div className='container mx-auto py-12 px-4'>
      <div className='flex flex-col items-center justify-center'>
        <Card className='w-full max-w-3xl mb-6'>
          <CardHeader>
            <CardTitle className='text-2xl'>
              Welcome to Your Dashboard
            </CardTitle>
          </CardHeader>
        </Card>

        <Card className='w-full max-w-3xl mb-6'>
          <CardHeader>
            <CardTitle>Your Documents</CardTitle>
            <CardDescription>
              Manage your estate planning documents
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <p>You have {documentCount} documents created.</p>

            <div className='flex gap-2 space-y-2'>
              <Button asChild>
                <Link href='#' className='flex items-center'>
                  <FileText className='mr-2 h-4 w-4' />
                  Create New Document
                </Link>
              </Button>

              <Button asChild>
                <Link href='#' className='flex items-center'>
                  <History className='mr-2 h-4 w-4' />
                  View Document History
                </Link>
              </Button>
            </div>
          </CardContent>
          <CardFooter className={'justify-end'}>
            <Button variant={'ghost'} className={'cursor-pointer'} asChild>
              <Link href={routes.interview}>
                Start Interview
                <ArrowRight className='ml-2 h-4 w-4' />
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <LivingDocumentsDashboard />

        {/* <ProfileSecurity/> */}
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <AuthGuard fallbackMessage='You need to be logged in to access your profile.'>
      <ProfilePageContent />
    </AuthGuard>
  );
}
