'use client';

import { Table } from '@tanstack/react-table';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTableViewOptions } from './data-table-view-options';
import { DataTableFacetedFilter } from './data-table-faceted-filter';
import { DataTableConfig } from './data-table';

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  config?: DataTableConfig;
}

export function DataTableToolbar<TData>({
  table,
  config = {},
}: DataTableToolbarProps<TData>) {
  const {
    searchColumn = 'name',
    searchPlaceholder = 'Search...',
    filters = [],
    enableColumnVisibility = true,
  } = config;

  const isFiltered = table.getState().columnFilters.length > 0;

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        {/* Search Input */}
        {searchColumn && (
          <Input
            placeholder={searchPlaceholder}
            value={
              (table.getColumn(searchColumn)?.getFilterValue() as string) ?? ''
            }
            onChange={event =>
              table.getColumn(searchColumn)?.setFilterValue(event.target.value)
            }
            className='h-8 w-[150px] lg:w-[250px]'
          />
        )}

        {/* Faceted Filters */}
        {filters.map(filter => {
          const column = table.getColumn(filter.id);
          if (!column) return null;

          return (
            <DataTableFacetedFilter
              key={filter.id}
              column={column}
              title={filter.title}
              options={filter.options}
            />
          );
        })}

        {/* Reset Filters Button */}
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>

      {/* Column Visibility Toggle */}
      {enableColumnVisibility && <DataTableViewOptions table={table} />}
    </div>
  );
}
