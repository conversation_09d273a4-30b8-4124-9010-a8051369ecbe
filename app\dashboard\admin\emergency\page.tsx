'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Download,
  Shield,
  Activity,
  Users,
  FileText,
  Search,
  Filter,
  MoreHorizontal,
  Power,
  PowerOff,
} from 'lucide-react';

interface DMSStatus {
  id: string;
  memberId: string;
  memberName: string;
  memberEmail: string;
  status: 'active' | 'paused' | 'triggered' | 'inactive';
  lastCheckIn: string;
  nextCheckIn: string;
  missedCheckIns: number;
  escalationLevel: number;
  emergencyContacts: number;
}

interface EvidenceSubmission {
  id: string;
  submitterName: string;
  submitterEmail: string;
  memberName: string;
  memberEmail: string;
  evidenceType: string;
  submittedAt: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: string;
  filesCount: number;
}

interface EmergencyAccess {
  id: string;
  memberId: string;
  memberName: string;
  requesterId: string;
  requesterName: string;
  accessType: 'temporary' | 'permanent';
  grantedAt: string;
  expiresAt?: string;
  documentsAccessed: number;
  status: 'active' | 'expired' | 'revoked';
}

// Mock data
const mockDMSStatuses: DMSStatus[] = [
  {
    id: 'dms-001',
    memberId: 'member-001',
    memberName: 'John Smith',
    memberEmail: '<EMAIL>',
    status: 'triggered',
    lastCheckIn: '2025-01-10T10:00:00Z',
    nextCheckIn: '2025-01-17T10:00:00Z',
    missedCheckIns: 2,
    escalationLevel: 2,
    emergencyContacts: 3,
  },
  {
    id: 'dms-002',
    memberId: 'member-002',
    memberName: 'Sarah Johnson',
    memberEmail: '<EMAIL>',
    status: 'active',
    lastCheckIn: '2025-01-19T14:30:00Z',
    nextCheckIn: '2025-01-26T14:30:00Z',
    missedCheckIns: 0,
    escalationLevel: 0,
    emergencyContacts: 2,
  },
  {
    id: 'dms-003',
    memberId: 'member-003',
    memberName: 'Michael Chen',
    memberEmail: '<EMAIL>',
    status: 'paused',
    lastCheckIn: '2025-01-15T09:15:00Z',
    nextCheckIn: '2025-01-30T09:15:00Z',
    missedCheckIns: 0,
    escalationLevel: 0,
    emergencyContacts: 4,
  },
];

const mockEvidenceSubmissions: EvidenceSubmission[] = [
  {
    id: 'ev-001',
    submitterName: 'Dr. Emily Rodriguez',
    submitterEmail: '<EMAIL>',
    memberName: 'John Smith',
    memberEmail: '<EMAIL>',
    evidenceType: 'Medical Certificate',
    submittedAt: '2025-01-20T16:45:00Z',
    status: 'pending',
    filesCount: 2,
  },
  {
    id: 'ev-002',
    submitterName: 'Lisa Thompson',
    submitterEmail: '<EMAIL>',
    memberName: 'Robert Davis',
    memberEmail: '<EMAIL>',
    evidenceType: 'Death Certificate',
    submittedAt: '2025-01-19T11:20:00Z',
    status: 'approved',
    reviewedBy: 'Admin User',
    reviewedAt: '2025-01-19T15:30:00Z',
    filesCount: 1,
  },
];

const mockEmergencyAccess: EmergencyAccess[] = [
  {
    id: 'access-001',
    memberId: 'member-001',
    memberName: 'John Smith',
    requesterId: 'contact-001',
    requesterName: 'Dr. Emily Rodriguez',
    accessType: 'temporary',
    grantedAt: '2025-01-20T17:00:00Z',
    expiresAt: '2025-01-27T17:00:00Z',
    documentsAccessed: 3,
    status: 'active',
  },
  {
    id: 'access-002',
    memberId: 'member-004',
    memberName: 'Robert Davis',
    requesterId: 'contact-002',
    requesterName: 'Lisa Thompson',
    accessType: 'permanent',
    grantedAt: '2025-01-19T16:00:00Z',
    documentsAccessed: 8,
    status: 'active',
  },
];

export default function AdminEmergencyPage() {
  const [activeTab, setActiveTab] = useState('dms');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const handleApproveEvidence = (evidenceId: string) => {
    console.log('Approving evidence:', evidenceId);
    // In real app, this would call API to approve evidence
  };

  const handleRejectEvidence = (evidenceId: string) => {
    console.log('Rejecting evidence:', evidenceId);
    // In real app, this would call API to reject evidence
  };

  const handleRevokeAccess = (accessId: string) => {
    console.log('Revoking access:', accessId);
    // In real app, this would call API to revoke access
  };

  const handleOverrideDMS = (
    dmsId: string,
    action: 'pause' | 'resume' | 'reset'
  ) => {
    console.log(`DMS Override - ${action}:`, dmsId);
    // In real app, this would call API to override DMS
  };

  const getStatusBadge = (
    status: string,
    type: 'dms' | 'evidence' | 'access'
  ) => {
    const baseClasses = 'text-xs';

    if (type === 'dms') {
      switch (status) {
        case 'active':
          return (
            <Badge className={`${baseClasses} bg-green-100 text-green-800`}>
              Active
            </Badge>
          );
        case 'paused':
          return (
            <Badge className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
              Paused
            </Badge>
          );
        case 'triggered':
          return (
            <Badge className={`${baseClasses} bg-red-100 text-red-800`}>
              Triggered
            </Badge>
          );
        case 'inactive':
          return (
            <Badge variant='outline' className={baseClasses}>
              Inactive
            </Badge>
          );
      }
    } else if (type === 'evidence') {
      switch (status) {
        case 'pending':
          return (
            <Badge className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
              Pending
            </Badge>
          );
        case 'under_review':
          return (
            <Badge className={`${baseClasses} bg-blue-100 text-blue-800`}>
              Under Review
            </Badge>
          );
        case 'approved':
          return (
            <Badge className={`${baseClasses} bg-green-100 text-green-800`}>
              Approved
            </Badge>
          );
        case 'rejected':
          return (
            <Badge className={`${baseClasses} bg-red-100 text-red-800`}>
              Rejected
            </Badge>
          );
      }
    } else if (type === 'access') {
      switch (status) {
        case 'active':
          return (
            <Badge className={`${baseClasses} bg-green-100 text-green-800`}>
              Active
            </Badge>
          );
        case 'expired':
          return (
            <Badge variant='outline' className={baseClasses}>
              Expired
            </Badge>
          );
        case 'revoked':
          return (
            <Badge className={`${baseClasses} bg-red-100 text-red-800`}>
              Revoked
            </Badge>
          );
      }
    }
    return null;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold mb-4 text-[var(--custom-gray-dark)]'>
          Emergency Management
        </h1>
        <p className='text-lg text-[var(--custom-gray-medium)] mb-6'>
          Monitor Dead Man's Switch status, review evidence submissions, and
          manage emergency access
        </p>

        {/* Overview Stats */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
          <Card>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between mb-2'>
                <Activity className='h-8 w-8 text-green-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                  Active DMS
                </p>
                <p className='text-2xl font-bold'>
                  {mockDMSStatuses.filter(d => d.status === 'active').length}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between mb-2'>
                <AlertTriangle className='h-8 w-8 text-red-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                  Triggered DMS
                </p>
                <p className='text-2xl font-bold text-red-600'>
                  {mockDMSStatuses.filter(d => d.status === 'triggered').length}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between mb-2'>
                <Clock className='h-8 w-8 text-yellow-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                  Pending Evidence
                </p>
                <p className='text-2xl font-bold text-yellow-600'>
                  {
                    mockEvidenceSubmissions.filter(e => e.status === 'pending')
                      .length
                  }
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between mb-2'>
                <Shield className='h-8 w-8 text-blue-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                  Active Access
                </p>
                <p className='text-2xl font-bold text-blue-600'>
                  {
                    mockEmergencyAccess.filter(a => a.status === 'active')
                      .length
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-3 mb-8'>
          <TabsTrigger value='dms' className='flex items-center gap-2'>
            <Activity className='h-4 w-4' />
            Dead Man's Switch
          </TabsTrigger>
          <TabsTrigger value='evidence' className='flex items-center gap-2'>
            <FileText className='h-4 w-4' />
            Evidence Review
          </TabsTrigger>
          <TabsTrigger value='access' className='flex items-center gap-2'>
            <Shield className='h-4 w-4' />
            Emergency Access
          </TabsTrigger>
        </TabsList>

        {/* Dead Man's Switch Tab */}
        <TabsContent value='dms'>
          <Card>
            <CardHeader>
              <CardTitle>Dead Man's Switch Monitoring</CardTitle>
              <CardDescription>
                Monitor and manage member Dead Man's Switch status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockDMSStatuses.map(dms => (
                  <div key={dms.id} className='p-4 border rounded-lg'>
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-3 mb-2'>
                          <h3 className='font-semibold'>{dms.memberName}</h3>
                          {getStatusBadge(dms.status, 'dms')}
                          {dms.status === 'triggered' && (
                            <Badge className='bg-red-100 text-red-800 text-xs'>
                              Level {dms.escalationLevel}
                            </Badge>
                          )}
                        </div>

                        <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                          {dms.memberEmail}
                        </p>

                        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Last Check-in
                            </p>
                            <p className='font-medium'>
                              {formatDate(dms.lastCheckIn)}
                            </p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Next Due
                            </p>
                            <p className='font-medium'>
                              {formatDate(dms.nextCheckIn)}
                            </p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Missed Check-ins
                            </p>
                            <p className='font-medium'>{dms.missedCheckIns}</p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Emergency Contacts
                            </p>
                            <p className='font-medium'>
                              {dms.emergencyContacts}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className='flex gap-2 ml-4'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleOverrideDMS(dms.id, 'pause')}
                        >
                          <PowerOff className='h-4 w-4' />
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleOverrideDMS(dms.id, 'reset')}
                        >
                          Reset
                        </Button>
                        <Button variant='outline' size='sm'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Evidence Review Tab */}
        <TabsContent value='evidence'>
          <Card>
            <CardHeader>
              <CardTitle>Evidence Submissions</CardTitle>
              <CardDescription>
                Review and approve evidence submissions for emergency access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockEvidenceSubmissions.map(evidence => (
                  <div key={evidence.id} className='p-4 border rounded-lg'>
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-3 mb-2'>
                          <h3 className='font-semibold'>
                            {evidence.submitterName}
                          </h3>
                          {getStatusBadge(evidence.status, 'evidence')}
                          <Badge variant='outline' className='text-xs'>
                            {evidence.evidenceType}
                          </Badge>
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm mb-3'>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Member
                            </p>
                            <p className='font-medium'>{evidence.memberName}</p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Submitted
                            </p>
                            <p className='font-medium'>
                              {formatDate(evidence.submittedAt)}
                            </p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Files
                            </p>
                            <p className='font-medium'>
                              {evidence.filesCount} files
                            </p>
                          </div>
                        </div>

                        {evidence.reviewedBy && (
                          <div className='text-sm text-[var(--custom-gray-medium)]'>
                            Reviewed by {evidence.reviewedBy} on{' '}
                            {formatDate(evidence.reviewedAt!)}
                          </div>
                        )}
                      </div>

                      <div className='flex gap-2 ml-4'>
                        <Button variant='outline' size='sm'>
                          <Eye className='h-4 w-4 mr-1' />
                          View
                        </Button>
                        {evidence.status === 'pending' && (
                          <>
                            <Button
                              variant='default'
                              size='sm'
                              onClick={() => handleApproveEvidence(evidence.id)}
                            >
                              <CheckCircle className='h-4 w-4 mr-1' />
                              Approve
                            </Button>
                            <Button
                              variant='destructive'
                              size='sm'
                              onClick={() => handleRejectEvidence(evidence.id)}
                            >
                              <XCircle className='h-4 w-4 mr-1' />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Emergency Access Tab */}
        <TabsContent value='access'>
          <Card>
            <CardHeader>
              <CardTitle>Emergency Access Management</CardTitle>
              <CardDescription>
                Monitor and manage active emergency document access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {mockEmergencyAccess.map(access => (
                  <div key={access.id} className='p-4 border rounded-lg'>
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-3 mb-2'>
                          <h3 className='font-semibold'>
                            {access.requesterName}
                          </h3>
                          {getStatusBadge(access.status, 'access')}
                          <Badge
                            variant='outline'
                            className='text-xs capitalize'
                          >
                            {access.accessType}
                          </Badge>
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Member
                            </p>
                            <p className='font-medium'>{access.memberName}</p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Granted
                            </p>
                            <p className='font-medium'>
                              {formatDate(access.grantedAt)}
                            </p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Expires
                            </p>
                            <p className='font-medium'>
                              {access.expiresAt
                                ? formatDate(access.expiresAt)
                                : 'Never'}
                            </p>
                          </div>
                          <div>
                            <p className='text-[var(--custom-gray-medium)]'>
                              Documents Accessed
                            </p>
                            <p className='font-medium'>
                              {access.documentsAccessed}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className='flex gap-2 ml-4'>
                        <Button variant='outline' size='sm'>
                          <Eye className='h-4 w-4 mr-1' />
                          View Logs
                        </Button>
                        {access.status === 'active' && (
                          <Button
                            variant='destructive'
                            size='sm'
                            onClick={() => handleRevokeAccess(access.id)}
                          >
                            <XCircle className='h-4 w-4 mr-1' />
                            Revoke
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
