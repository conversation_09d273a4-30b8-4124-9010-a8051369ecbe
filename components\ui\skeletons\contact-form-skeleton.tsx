import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

export function ContactFormSkeleton() {
  return (
    <div className='max-w-3xl mx-auto'>
      <Card>
        <CardContent className='p-6 space-y-6'>
          {/* Name and Relationship fields */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-10 w-full' />
            </div>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-28' />
              <Skeleton className='h-10 w-full' />
            </div>
          </div>

          {/* Contact Info fields */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-32' />
              <Skeleton className='h-10 w-full' />
            </div>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-28' />
              <Skeleton className='h-10 w-full' />
            </div>
          </div>

          {/* Contact Type and Primary fields */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-28' />
              <Skeleton className='h-10 w-full' />
            </div>
            <div className='space-y-4'>
              <Skeleton className='h-4 w-36' />
              <Skeleton className='h-5 w-5 rounded-full' />
            </div>
          </div>

          {/* Buttons */}
          <div className='flex justify-end space-x-4 pt-4'>
            <Skeleton className='h-10 w-24' />
            <Skeleton className='h-10 w-24' />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
