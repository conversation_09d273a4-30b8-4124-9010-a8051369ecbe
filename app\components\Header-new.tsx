'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { cn } from '../../lib/utils';
import { UserDropdown } from './user-dropdown';
// import { NotificationBell } from "./notifications/notification-bell";
// import { useRole } from "@/lib/roles/role-context";
// import { AccountSwitcherCompact } from "@/components/account-linking/account-switcher";
// import { useLinkedAccountContextOptional } from "@/lib/contexts/linked-account-context";

export function Header() {
  const pathname = usePathname();
  const router = useRouter();
  // const { userContext } = useRole();
  // const linkedAccountContext = useLinkedAccountContextOptional();

  const handleLogout = () => {
    // In a real app, this would call an API to log the user out
    console.log('Logging out...');
    // Redirect to login page
    router.push('/login');
  };

  // Check if we're on a dashboard page
  const isDashboardPage =
    pathname.startsWith('/dashboard') ||
    pathname.startsWith('/admin') ||
    pathname.startsWith('/emergency') ||
    pathname.startsWith('/professional') ||
    pathname.startsWith('/linked') ||
    pathname.startsWith('/staff') ||
    pathname.startsWith('/member');

  // Use role from context instead of URL-based detection
  const userRole = 'Member'; // userContext?.displayName || "Member";

  // Get background color based on page type
  const getBgColor = () => {
    return 'bg-background border-b border-gray-200 text-[var(--custom-gray-dark)]';
  };

  return (
    <header className='sticky top-0 z-50 w-full shadow-md'>
      <div className={cn(getBgColor())}>
        <div className='container mx-auto px-4'>
          <div className='flex h-16 items-center justify-between'>
            {/* Logo - text only */}
            <div className='flex items-center'>
              <Link
                href={isDashboardPage ? '/dashboard' : '/'}
                className='flex items-center'
              >
                <span className='text-xl font-bold'>ChildFree</span>
              </Link>
            </div>

            {/* Account Switcher, Notifications and User dropdown */}
            {isDashboardPage && (
              <div className='flex items-center space-x-3'>
                {/* {linkedAccountContext && <AccountSwitcherCompact />} */}
                {/* <NotificationBell /> */}
                <UserDropdown userRole={userRole} onLogout={handleLogout} />
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
