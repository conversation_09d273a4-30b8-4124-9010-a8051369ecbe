'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Headline, Subhead } from '../../../components/ui/brand/typography';
import {
  Scale,
  Phone,
  Mail,
  MapPin,
  ArrowLeft,
  Search,
  CheckCircle,
  AlertCircle,
  ExternalLink,
} from 'lucide-react';

// Types for attorney data
interface Attorney {
  id: string;
  name: string;
  firm: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  specialties: string[];
  barNumber: string;
  yearsExperience: number;
  rating?: number;
  isPreferred: boolean;
}

// Mock attorney data
const mockAttorneys: Attorney[] = [
  // California attorneys
  {
    id: 'att-1',
    name: 'Sarah <PERSON>',
    firm: 'Johnson Estate Law',
    phone: '(*************',
    email: '<EMAIL>',
    address: '123 Main Street',
    city: 'Los Angeles',
    state: 'CA',
    zipCode: '90210',
    specialties: ['Estate Planning', 'Wills & Trusts', 'Probate'],
    barNumber: 'CA123456',
    yearsExperience: 15,
    rating: 4.8,
    isPreferred: true,
  },
  {
    id: 'att-2',
    name: 'Michael Chen',
    firm: 'Chen & Associates',
    phone: '(*************',
    email: '<EMAIL>',
    address: '456 Oak Avenue',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94102',
    specialties: ['Estate Planning', 'Tax Law', 'Business Law'],
    barNumber: 'CA234567',
    yearsExperience: 12,
    rating: 4.9,
    isPreferred: true,
  },
  {
    id: 'att-3',
    name: 'Jennifer Martinez',
    firm: 'Martinez Legal Group',
    phone: '(*************',
    email: '<EMAIL>',
    address: '789 Pine Street',
    city: 'San Diego',
    state: 'CA',
    zipCode: '92101',
    specialties: ['Estate Planning', 'Elder Law', 'Guardianship'],
    barNumber: 'CA345678',
    yearsExperience: 18,
    rating: 4.7,
    isPreferred: true,
  },
  // New York attorneys
  {
    id: 'att-4',
    name: 'Robert Williams',
    firm: 'Williams Estate Planning',
    phone: '(*************',
    email: '<EMAIL>',
    address: '100 Broadway',
    city: 'New York',
    state: 'NY',
    zipCode: '10005',
    specialties: ['Estate Planning', 'Wills & Trusts', 'Tax Law'],
    barNumber: 'NY123456',
    yearsExperience: 20,
    rating: 4.9,
    isPreferred: true,
  },
  {
    id: 'att-5',
    name: 'Lisa Thompson',
    firm: 'Thompson Legal Services',
    phone: '(*************',
    email: '<EMAIL>',
    address: '250 Park Avenue',
    city: 'New York',
    state: 'NY',
    zipCode: '10017',
    specialties: ['Estate Planning', 'Probate', 'Elder Law'],
    barNumber: 'NY234567',
    yearsExperience: 14,
    rating: 4.6,
    isPreferred: true,
  },
  // Texas attorneys
  {
    id: 'att-6',
    name: 'David Rodriguez',
    firm: 'Rodriguez & Partners',
    phone: '(*************',
    email: '<EMAIL>',
    address: '1000 Main Street',
    city: 'Houston',
    state: 'TX',
    zipCode: '77002',
    specialties: ['Estate Planning', 'Business Law', 'Real Estate'],
    barNumber: 'TX123456',
    yearsExperience: 16,
    rating: 4.8,
    isPreferred: true,
  },
];

export default function AttorneyListPage() {
  const router = useRouter();
  const [attorneys, setAttorneys] = useState<Attorney[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCity, setSelectedCity] = useState<string>('all');
  const [selectedSpecialty, setSelectedSpecialty] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const [selectedAttorney, setSelectedAttorney] = useState<Attorney | null>(
    null
  );
  const [accessAuthorized, setAccessAuthorized] = useState(false);

  // Get user state from session storage (set during attorney review request)
  const [userState, setUserState] = useState<string>('');

  useEffect(() => {
    const storedUserState = sessionStorage.getItem('user-state');
    if (storedUserState) {
      setUserState(storedUserState);
    } else {
      // Fallback to mock data if no state found
      setUserState('CA');
    }
  }, []);

  // Check if user accessed this page from document review
  useEffect(() => {
    const checkAccess = () => {
      // Check if user came from document review page
      const sessionFlag = sessionStorage.getItem('attorney-review-requested');

      console.log('Access check:', {
        sessionFlag,
        hasFlag: sessionFlag === 'true',
      });

      // Simplified check - just use session flag for now
      // In production, you might want to add additional security checks
      if (sessionFlag !== 'true') {
        // Redirect to document review if not authorized
        alert(
          'Attorney list can only be accessed through the "Request Attorney Review" button during document review.'
        );
        router.push('/documents/review');
        return;
      }

      setAccessAuthorized(true);
      // Clear the session flag
      sessionStorage.removeItem('attorney-review-requested');
    };

    // Add a small delay to ensure session storage is set
    const timer = setTimeout(checkAccess, 100);
    return () => clearTimeout(timer);
  }, [router]);

  useEffect(() => {
    // Only load attorneys if access is authorized and user state is available
    if (!accessAuthorized || !userState) return;

    // Simulate loading attorneys
    const loadAttorneys = async () => {
      try {
        setLoading(true);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter attorneys by user's state
        const stateFilteredAttorneys = mockAttorneys.filter(
          attorney => attorney.state === userState
        );
        setAttorneys(stateFilteredAttorneys);
      } catch (err) {
        setError('Failed to load attorneys. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadAttorneys();
  }, [accessAuthorized, userState]);

  // Filter attorneys based on search and filters
  const filteredAttorneys = attorneys.filter(attorney => {
    const matchesSearch =
      attorney.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attorney.firm.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCity =
      selectedCity === 'all' || attorney.city === selectedCity;
    const matchesSpecialty =
      selectedSpecialty === 'all' ||
      attorney.specialties.some(s =>
        s.toLowerCase().includes(selectedSpecialty.toLowerCase())
      );

    return matchesSearch && matchesCity && matchesSpecialty;
  });

  // Get unique cities and specialties for filters
  const cities = Array.from(new Set(attorneys.map(a => a.city))).sort();
  const specialties = Array.from(
    new Set(attorneys.flatMap(a => a.specialties))
  ).sort();

  const handleSelectAttorney = (attorney: Attorney) => {
    setSelectedAttorney(attorney);
  };

  const handleContactAttorney = (attorney: Attorney) => {
    // In a real implementation, this would log the attorney review request
    alert(
      `Contacting ${attorney.name} at ${attorney.firm}. You will be redirected to proceed with signing.`
    );
    router.push('/documents/sign');
  };

  const handleSkipAttorneyReview = () => {
    router.push('/documents/sign');
  };

  const handleProceedToSigning = () => {
    router.push('/documents/sign');
  };

  const handleBackToReview = () => {
    router.push('/documents/review');
  };

  // Don't render anything if access is not authorized
  if (!accessAuthorized) {
    return null;
  }

  if (loading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-6xl mx-auto'>
          <div className='mb-8'>
            <Skeleton className='h-10 w-1/3 mb-2' />
            <Skeleton className='h-5 w-1/2' />
          </div>
          <div className='grid gap-4'>
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className='h-48 w-full' />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-6xl mx-auto'>
          <Alert variant='destructive'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <div className='flex items-center gap-4 mb-4'>
            <Button variant='outline' size='sm' onClick={handleBackToReview}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Review
            </Button>
          </div>
          <Headline className='mb-2'>
            Preferred Attorneys - {userState}
          </Headline>
          <Subhead className='text-muted-foreground'>
            Select a state-licensed attorney to review your estate planning
            documents. Attorney reviews are included in your membership.
          </Subhead>
        </div>

        {/* Information Card */}
        <Card className='mb-8 bg-blue-50 border-blue-200'>
          <CardHeader>
            <CardTitle className='text-blue-800 flex items-center gap-2'>
              <Scale className='h-5 w-5' />
              Attorney Review Process
            </CardTitle>
          </CardHeader>
          <CardContent className='text-blue-700'>
            <ul className='space-y-2'>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>1.</span>
                <span>Select an attorney from our preferred list below</span>
              </li>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>2.</span>
                <span>
                  Contact them directly using the provided information
                </span>
              </li>
              <li className='flex items-start gap-2'>
                <span className='font-medium'>3.</span>
                <span>
                  Return to the platform after review to proceed with signing
                </span>
              </li>
            </ul>
            <div className='mt-4 p-3 bg-blue-100 rounded-md'>
              <p className='text-sm font-medium'>
                Target completion: 5 business days • Included in membership
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Search and Filters */}
        <Card className='mb-6'>
          <CardContent className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div className='relative'>
                <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search attorneys or firms...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
              <Select value={selectedCity} onValueChange={setSelectedCity}>
                <SelectTrigger>
                  <SelectValue placeholder='All Cities' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Cities</SelectItem>
                  {cities.map(city => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={selectedSpecialty}
                onValueChange={setSelectedSpecialty}
              >
                <SelectTrigger>
                  <SelectValue placeholder='All Specialties' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Specialties</SelectItem>
                  {specialties.map(specialty => (
                    <SelectItem key={specialty} value={specialty}>
                      {specialty}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant='outline'
                onClick={handleSkipAttorneyReview}
                className='w-full'
              >
                Skip Attorney Review
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Attorney List */}
        {attorneys.length === 0 ? (
          // No attorneys available for user's state
          <Card>
            <CardContent className='p-8 text-center'>
              <AlertCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
              <p className='text-lg font-medium mb-2'>
                No attorneys available for your state
              </p>
              <p className='text-muted-foreground mb-4'>
                No attorneys available for your state. Proceed to signing or
                contact support.
              </p>
              <div className='flex gap-4 justify-center'>
                <Button onClick={handleProceedToSigning}>
                  Proceed to Signing
                </Button>
                <Button
                  variant='outline'
                  onClick={() => {
                    // In real implementation, this would open support contact form
                    alert(
                      'Contact support functionality would be implemented here'
                    );
                  }}
                >
                  Contact Support
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : filteredAttorneys.length === 0 ? (
          // No attorneys match search criteria
          <Card>
            <CardContent className='p-8 text-center'>
              <AlertCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
              <p className='text-lg font-medium mb-2'>No attorneys found</p>
              <p className='text-muted-foreground mb-4'>
                No attorneys match your current search criteria. Try adjusting
                your filters.
              </p>
              <Button
                variant='outline'
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCity('all');
                  setSelectedSpecialty('all');
                }}
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className='space-y-4'>
            {filteredAttorneys.map(attorney => (
              <Card
                key={attorney.id}
                className='hover:shadow-md transition-shadow'
              >
                <CardContent className='p-6'>
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-3 mb-2'>
                        <h3 className='text-xl font-semibold'>
                          {attorney.name}
                        </h3>
                        {attorney.isPreferred && (
                          <Badge
                            variant='secondary'
                            className='bg-green-100 text-green-800'
                          >
                            <CheckCircle className='h-3 w-3 mr-1' />
                            Preferred
                          </Badge>
                        )}
                        {attorney.rating && (
                          <Badge variant='outline'>★ {attorney.rating}</Badge>
                        )}
                      </div>
                      <p className='text-lg text-muted-foreground mb-3'>
                        {attorney.firm}
                      </p>

                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                        <div className='space-y-2'>
                          <div className='flex items-center gap-2 text-sm'>
                            <Phone className='h-4 w-4 text-muted-foreground' />
                            <span>{attorney.phone}</span>
                          </div>
                          <div className='flex items-center gap-2 text-sm'>
                            <Mail className='h-4 w-4 text-muted-foreground' />
                            <span>{attorney.email}</span>
                          </div>
                          <div className='flex items-center gap-2 text-sm'>
                            <MapPin className='h-4 w-4 text-muted-foreground' />
                            <span>
                              {attorney.address}, {attorney.city},{' '}
                              {attorney.state} {attorney.zipCode}
                            </span>
                          </div>
                        </div>
                        <div className='space-y-2'>
                          <div className='text-sm'>
                            <span className='font-medium'>Bar Number:</span>{' '}
                            {attorney.barNumber}
                          </div>
                          <div className='text-sm'>
                            <span className='font-medium'>Experience:</span>{' '}
                            {attorney.yearsExperience} years
                          </div>
                          <div className='text-sm'>
                            <span className='font-medium'>Specialties:</span>
                            <div className='flex flex-wrap gap-1 mt-1'>
                              {attorney.specialties.map((specialty, index) => (
                                <Badge
                                  key={index}
                                  variant='outline'
                                  className='text-xs'
                                >
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className='ml-6'>
                      <Button
                        onClick={() => handleContactAttorney(attorney)}
                        className='bg-blue-600 hover:bg-blue-700'
                      >
                        <ExternalLink className='h-4 w-4 mr-2' />
                        Contact Attorney
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Footer Actions */}
        <div className='mt-8 flex justify-between items-center'>
          <Button variant='outline' onClick={handleBackToReview}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Document Review
          </Button>
          <Button variant='outline' onClick={handleSkipAttorneyReview}>
            Skip Attorney Review & Proceed to Signing
          </Button>
        </div>
      </div>
    </div>
  );
}
