// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/exportDocumentUpdateLogs';
import * as ExcelJS from 'exceljs';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

export const handler: Schema['exportDocumentUpdateLogs']['functionHandler'] =
  async event => {
    const { startDate, endDate, userId, changeType } = event.arguments;

    console.log('===> START EXPORT DOCUMENT UPDATE LOGS:', {
      startDate,
      endDate,
      userId,
      changeType,
    });

    try {
      // Build filter conditions
      const filter: any = {};

      // Add date range filter if provided
      if (startDate) {
        filter.timestamp = { ge: startDate };
      }
      if (endDate) {
        if (filter.timestamp) {
          filter.timestamp.le = endDate;
        } else {
          filter.timestamp = { le: endDate };
        }
      }

      // Add user filter if provided
      if (userId) {
        filter.userId = { eq: userId };
      }

      // Add change type filter if provided
      if (changeType) {
        filter.changeType = { eq: changeType };
      }

      console.log('===> Fetching document update logs with filter:', filter);

      // Fetch document update logs from database
      const { data: logs, errors } = await client.models.DocumentUpdateLog.list(
        {
          filter: Object.keys(filter).length > 0 ? filter : undefined,
          limit: 10000, // Set a reasonable limit
        }
      );

      if (errors) {
        console.error('===> Error fetching document update logs:', errors);
        throw new Error('Failed to fetch document update logs');
      }

      console.log(`===> Found ${logs?.length || 0} document update logs`);

      if (!logs || logs.length === 0) {
        return {
          success: false,
          message: 'No document update logs found for the specified criteria',
          data: null,
        };
      }

      // Transform data for Excel export
      const excelData = logs.map(log => ({
        'Log ID': log.id,
        'Document ID': log.documentId,
        'User ID': log.userId,
        'Change Type': log.changeType,
        'Change Description': log.changeDescription || '',
        'Previous Version': log.previousVersion || '',
        'New Version': log.newVersion || '',
        Timestamp: log.timestamp
          ? new Date(log.timestamp).toLocaleString()
          : '',
        'Created At': log.createdAt
          ? new Date(log.createdAt).toLocaleString()
          : '',
        'Updated At': log.updatedAt
          ? new Date(log.updatedAt).toLocaleString()
          : '',
      }));

      console.log('===> Creating Excel workbook');

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Document Update Logs');

      // Define columns with headers and widths
      worksheet.columns = [
        { header: 'Log ID', key: 'logId', width: 20 },
        { header: 'Document ID', key: 'documentId', width: 20 },
        { header: 'User ID', key: 'userId', width: 20 },
        { header: 'Change Type', key: 'changeType', width: 15 },
        { header: 'Change Description', key: 'changeDescription', width: 40 },
        { header: 'Previous Version', key: 'previousVersion', width: 15 },
        { header: 'New Version', key: 'newVersion', width: 15 },
        { header: 'Timestamp', key: 'timestamp', width: 20 },
        { header: 'Created At', key: 'createdAt', width: 20 },
        { header: 'Updated At', key: 'updatedAt', width: 20 },
      ];

      // Add data rows
      excelData.forEach(log => {
        worksheet.addRow({
          logId: log['Log ID'],
          documentId: log['Document ID'],
          userId: log['User ID'],
          changeType: log['Change Type'],
          changeDescription: log['Change Description'],
          previousVersion: log['Previous Version'],
          newVersion: log['New Version'],
          timestamp: log['Timestamp'],
          createdAt: log['Created At'],
          updatedAt: log['Updated At'],
        });
      });

      // Generate Excel file buffer
      const excelBuffer = await workbook.xlsx.writeBuffer();

      // Convert buffer to base64 for return
      const base64Excel = Buffer.from(excelBuffer).toString('base64');

      console.log('===> Excel file generated successfully');

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `document-update-logs-${timestamp}.xlsx`;

      return {
        success: true,
        message: `Successfully exported ${logs.length} document update logs`,
        data: {
          filename,
          fileContent: base64Excel,
          mimeType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          recordCount: logs.length,
        },
      };
    } catch (error) {
      console.error('===> Error exporting document update logs:', error);
      return {
        success: false,
        message: 'Failed to export document update logs',
        error: error.message || 'Unknown error occurred',
        data: null,
      };
    }
  };
