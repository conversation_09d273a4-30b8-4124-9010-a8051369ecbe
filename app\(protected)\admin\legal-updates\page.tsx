'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  ArrowLeft,
  Plus,
  FileEdit,
  AlertTriangle,
  Calendar,
} from 'lucide-react';

// Mock data for legal updates
const legalUpdates = [
  {
    id: 'LU-2023-05',
    state: 'California',
    documentType: 'Will',
    description:
      'Updated witness requirements for California wills. Now requires two witnesses who are both present at the same time when the testator signs the will.',
    effectiveDate: '2023-06-01',
    createdAt: '2023-05-15',
    affectedTemplates: 1,
    status: 'Pending Review',
  },
  {
    id: 'LU-2023-04',
    state: 'New York',
    documentType: 'Healthcare POA',
    description:
      'New language requirements for healthcare power of attorney documents in New York. Additional disclosures about HIPAA compliance are now required.',
    effectiveDate: '2023-05-15',
    createdAt: '2023-04-20',
    affectedTemplates: 1,
    status: 'Implemented',
  },
  {
    id: 'LU-2023-03',
    state: 'Texas',
    documentType: 'Advance Directive',
    description:
      'Texas has updated the required language for advance healthcare directives to include more specific instructions about end-of-life care options.',
    effectiveDate: '2023-04-01',
    createdAt: '2023-03-10',
    affectedTemplates: 1,
    status: 'Implemented',
  },
  {
    id: 'LU-2023-02',
    state: 'Florida',
    documentType: 'Trust',
    description:
      'Florida has modified trust requirements to include additional language about digital assets and cryptocurrency.',
    effectiveDate: '2023-03-15',
    createdAt: '2023-02-20',
    affectedTemplates: 1,
    status: 'Implemented',
  },
  {
    id: 'LU-2023-01',
    state: 'California',
    documentType: 'Financial POA',
    description:
      'California has updated the statutory form for financial power of attorney documents with new agent responsibility disclosures.',
    effectiveDate: '2023-02-01',
    createdAt: '2023-01-15',
    affectedTemplates: 1,
    status: 'Implemented',
  },
];

// Define the type for template
type Template = {
  id: string;
  state: string;
  type: string;
  version: string;
  status: string;
};

// Define the type for affected templates
type AffectedTemplatesMap = {
  [key: string]: Template[];
};

// Mock data for affected templates
const affectedTemplates: AffectedTemplatesMap = {
  'LU-2023-05': [
    {
      id: '1',
      state: 'California',
      type: 'Will',
      version: '1.0',
      status: 'Pending Review',
    },
  ],
  'LU-2023-04': [
    {
      id: '3',
      state: 'New York',
      type: 'Healthcare POA',
      version: '1.2',
      status: 'Updated',
    },
  ],
  'LU-2023-03': [
    {
      id: '4',
      state: 'Texas',
      type: 'Advance Directive',
      version: '1.0',
      status: 'Updated',
    },
  ],
  'LU-2023-02': [
    {
      id: '2',
      state: 'Florida',
      type: 'Trust',
      version: '1.1',
      status: 'Updated',
    },
  ],
  'LU-2023-01': [
    {
      id: '5',
      state: 'California',
      type: 'Financial POA',
      version: '1.0',
      status: 'Updated',
    },
  ],
};

// Mock data for states and document types
const states = [
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
];

const documentTypes = [
  'Will',
  'Trust',
  'Healthcare POA',
  'Financial POA',
  'Advance Directive',
  'All Document Types',
];

export default function LegalUpdatesPage() {
  const router = useRouter();
  const [selectedUpdate, setSelectedUpdate] = useState<string | null>(null);
  const [isAddUpdateOpen, setIsAddUpdateOpen] = useState(false);

  // Form state for new legal update
  const [newUpdate, setNewUpdate] = useState({
    state: '',
    documentType: '',
    description: '',
    effectiveDate: '',
    legalReference: '',
  });

  const handleSelectUpdate = (updateId: string) => {
    setSelectedUpdate(updateId);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewUpdate({ ...newUpdate, [name]: value });
  };

  const handleStateChange = (value: string) => {
    setNewUpdate({ ...newUpdate, state: value });
  };

  const handleDocumentTypeChange = (value: string) => {
    setNewUpdate({ ...newUpdate, documentType: value });
  };

  const handleAddUpdate = () => {
    // In a real implementation, this would add a new legal update
    setIsAddUpdateOpen(false);

    // Reset form
    setNewUpdate({
      state: '',
      documentType: '',
      description: '',
      effectiveDate: '',
      legalReference: '',
    });
  };

  const handleUpdateTemplate = (templateId: string) => {
    router.push(`/admin/templates/edit/${templateId}`);
  };

  // Get selected update details
  const selectedUpdateDetails = selectedUpdate
    ? legalUpdates.find(update => update.id === selectedUpdate)
    : null;

  // Get affected templates for selected update
  const selectedUpdateTemplates = selectedUpdate
    ? affectedTemplates[selectedUpdate] || []
    : [];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex justify-between items-center mb-6'>
        <div className='flex items-center'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => router.push('/admin/templates')}
            className='mr-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Templates
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
              Legal Updates Dashboard
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Track legal changes by state and manage template updates
            </p>
          </div>
        </div>

        <Dialog open={isAddUpdateOpen} onOpenChange={setIsAddUpdateOpen}>
          <DialogTrigger asChild>
            <Button variant='default' size='sm'>
              <Plus className='mr-2 h-4 w-4' />
              Add Legal Update
            </Button>
          </DialogTrigger>
          <DialogContent className='sm:max-w-[600px]'>
            <DialogHeader>
              <DialogTitle>Add New Legal Update</DialogTitle>
              <DialogDescription>
                Record a new legal change that affects document templates
              </DialogDescription>
            </DialogHeader>

            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium mb-2 block'>
                    State
                  </label>
                  <Select
                    value={newUpdate.state}
                    onValueChange={handleStateChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select state' />
                    </SelectTrigger>
                    <SelectContent>
                      {states.map(state => (
                        <SelectItem key={state} value={state}>
                          {state}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className='text-sm font-medium mb-2 block'>
                    Document Type
                  </label>
                  <Select
                    value={newUpdate.documentType}
                    onValueChange={handleDocumentTypeChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select document type' />
                    </SelectTrigger>
                    <SelectContent>
                      {documentTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Description
                </label>
                <Textarea
                  name='description'
                  value={newUpdate.description}
                  onChange={handleInputChange}
                  placeholder='Describe the legal change and its impact on templates'
                  className='min-h-[100px]'
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Legal Reference
                </label>
                <Input
                  name='legalReference'
                  value={newUpdate.legalReference}
                  onChange={handleInputChange}
                  placeholder='e.g., California Probate Code Section 6110'
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Effective Date
                </label>
                <Input
                  type='date'
                  name='effectiveDate'
                  value={newUpdate.effectiveDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setIsAddUpdateOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant='default'
                size='sm'
                onClick={handleAddUpdate}
                disabled={
                  !newUpdate.state ||
                  !newUpdate.documentType ||
                  !newUpdate.description ||
                  !newUpdate.effectiveDate
                }
              >
                Add Update
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <div className='md:col-span-1'>
          <Card>
            <CardHeader>
              <CardTitle>Recent Legal Updates</CardTitle>
              <CardDescription>
                Select an update to view affected templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {legalUpdates.map(update => (
                  <div
                    key={update.id}
                    className={`p-4 border rounded-md cursor-pointer transition-colors ${
                      selectedUpdate === update.id
                        ? 'border-green-2010c bg-green-2010c/10'
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => handleSelectUpdate(update.id)}
                  >
                    <div className='flex justify-between items-start mb-2'>
                      <h3 className='font-medium'>
                        {update.state} {update.documentType}
                      </h3>
                      <Badge
                        variant={
                          update.status === 'Implemented'
                            ? 'default'
                            : 'outline'
                        }
                        className={
                          update.status === 'Implemented'
                            ? 'bg-green-2010c'
                            : ''
                        }
                      >
                        {update.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      ID: {update.id}
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      <Calendar className='inline-block h-3 w-3 mr-1' />
                      Effective: {formatDate(update.effectiveDate)}
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)] line-clamp-2 mt-2'>
                      {update.description}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='md:col-span-2'>
          {selectedUpdate ? (
            <>
              <Card className='mb-6'>
                <CardHeader>
                  <CardTitle>Update Details</CardTitle>
                  <CardDescription>
                    Information about the selected legal update
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mb-4'>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        ID
                      </p>
                      <p>{selectedUpdateDetails?.id}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        State
                      </p>
                      <p>{selectedUpdateDetails?.state}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Document Type
                      </p>
                      <p>{selectedUpdateDetails?.documentType}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Status
                      </p>
                      <p>{selectedUpdateDetails?.status}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Effective Date
                      </p>
                      <p>
                        {formatDate(selectedUpdateDetails?.effectiveDate || '')}
                      </p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Created
                      </p>
                      <p>
                        {formatDate(selectedUpdateDetails?.createdAt || '')}
                      </p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Affected Templates
                      </p>
                      <p>{selectedUpdateDetails?.affectedTemplates}</p>
                    </div>
                  </div>

                  <div>
                    <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-2'>
                      Description
                    </p>
                    <div className='p-4 bg-gray-50 rounded-md'>
                      <p>{selectedUpdateDetails?.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Affected Templates</CardTitle>
                  <CardDescription>
                    Templates that need to be updated
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {selectedUpdateTemplates.length === 0 ? (
                    <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                      No templates affected by this legal update.
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>State</TableHead>
                          <TableHead>Document Type</TableHead>
                          <TableHead>Version</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedUpdateTemplates.map(template => (
                          <TableRow key={template.id}>
                            <TableCell>{template.state}</TableCell>
                            <TableCell>{template.type}</TableCell>
                            <TableCell>v{template.version}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  template.status === 'Updated'
                                    ? 'default'
                                    : 'outline'
                                }
                                className={
                                  template.status === 'Updated'
                                    ? 'bg-green-2010c'
                                    : ''
                                }
                              >
                                {template.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant='outline'
                                size='sm'
                                onClick={() =>
                                  handleUpdateTemplate(template.id)
                                }
                                className='h-8'
                              >
                                <FileEdit className='mr-2 h-4 w-4' />
                                Update Template
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <div className='flex items-center justify-center h-full min-h-[300px] border border-dashed rounded-lg'>
              <div className='text-center p-6'>
                <AlertTriangle className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
                <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-1'>
                  No Update Selected
                </h3>
                <p className='text-[var(--custom-gray-medium)]'>
                  Select a legal update from the list to view details and
                  affected templates.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
