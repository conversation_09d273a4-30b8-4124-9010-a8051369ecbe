import { a } from '@aws-amplify/backend';

/**
 * Authentication and user onboarding related models
 */

export const UserOnboarding = a
  .model({
    userId: a.string().required(),
    currentStep: a.string().required(),
    metadata: a.json(),
    lastUpdated: a.datetime(),
    isComplete: a.boolean().default(false),
  })
  .identifier(['userId'])
  .authorization(allow => [allow.owner()]);

export const LoginAttempt = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    lockoutUntil: a.datetime(),
    failedAttempts: a.integer().default(0),
  })
  .identifier(['email'])
  .authorization(allow => [allow.guest(), allow.authenticated()]);

export const LoginHistory = a
  .model({
    email: a.string().required(),
    attemptTime: a.datetime().required(),
    success: a.boolean().required(),
    ipAddress: a.string(),
    userAgent: a.string(),
    sessionId: a.string(),
  })
  .authorization(allow => [allow.guest(), allow.authenticated()]);

export const VerificationTokens = a
  .model({
    id: a.id(),
    email: a.string().required(),
    token: a.string().required(),
    expiresAt: a.datetime().required(),
    isUsed: a.boolean().default(false),
    createdAt: a.datetime().required(),
    usedAt: a.datetime(),
    verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
  })
  .authorization(allow => [allow.guest(), allow.authenticated()]);
