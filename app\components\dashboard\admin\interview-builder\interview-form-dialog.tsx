'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Save, AlertTriangle, MessageSquare } from 'lucide-react';
import {
  Interview,
  CreateInterviewRequest,
  UpdateInterviewRequest,
  InterviewFormData,
} from '@/types/interview-builder';
import {
  createInterview,
  updateInterview,
  validateInterviewData,
} from '@/lib/api/interview-builder';

interface InterviewFormDialogProps {
  interview: Interview | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  loading?: boolean;
}

const TEMPLATE_OPTIONS = [
  { value: 'basic-estate-plan', label: 'Basic Estate Plan' },
  { value: 'advanced-trust', label: 'Advanced Trust Planning' },
  { value: 'healthcare-directives', label: 'Healthcare Directives' },
  { value: 'business-succession', label: 'Business Succession' },
  { value: 'charitable-planning', label: 'Charitable Planning' },
  { value: 'special-needs', label: 'Special Needs Planning' },
];

export function InterviewFormDialog({
  interview,
  isOpen,
  onClose,
  onSave,
  loading = false,
}: InterviewFormDialogProps) {
  const [formData, setFormData] = useState<InterviewFormData>({
    name: '',
    description: '',
    targetTemplate: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  const isEditing = !!interview;

  useEffect(() => {
    if (interview) {
      // Get the current version
      const currentVersion = interview.versions.find(
        v => v.version === interview.currentVersion
      );
      if (currentVersion) {
        setFormData({
          name: currentVersion.name,
          description: currentVersion.description,
          targetTemplate: currentVersion.targetTemplate || '',
        });
      }
    } else {
      setFormData({
        name: '',
        description: '',
        targetTemplate: '',
      });
    }
    setErrors({});
  }, [interview, isOpen]);

  const handleFieldChange = (field: keyof InterviewFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    const validationErrors = validateInterviewData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      if (isEditing && interview) {
        const updateData: UpdateInterviewRequest = {
          name: formData.name,
          description: formData.description,
          targetTemplate: formData.targetTemplate || undefined,
        };
        await updateInterview(updateData);
      } else {
        const createData: CreateInterviewRequest = {
          name: formData.name,
          description: formData.description,
          targetTemplate: formData.targetTemplate || undefined,
        };
        await createInterview(createData);
      }

      onSave();
    } catch (err) {
      setErrors({ submit: 'Failed to save interview. Please try again.' });
      console.error('Error saving interview:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (!saving) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <MessageSquare className='h-5 w-5' />
            <span>{isEditing ? 'Edit Interview' : 'Create New Interview'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update interview information and settings.'
              : 'Create a new interview template for member data collection.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className='space-y-6'>
          {errors.submit && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Basic Information</CardTitle>
              <CardDescription>Interview details and metadata</CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>
                  Interview Name <span className='text-red-500'>*</span>
                </Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={e => handleFieldChange('name', e.target.value)}
                  placeholder='e.g., Basic Estate Planning Interview'
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className='text-sm text-red-500'>{errors.name}</p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>
                  Description <span className='text-red-500'>*</span>
                </Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={e =>
                    handleFieldChange('description', e.target.value)
                  }
                  placeholder='Describe the purpose and scope of this interview...'
                  rows={3}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && (
                  <p className='text-sm text-red-500'>{errors.description}</p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='targetTemplate'>Target Template</Label>
                <Select
                  value={formData.targetTemplate}
                  onValueChange={value =>
                    handleFieldChange('targetTemplate', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select a document template (optional)' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=''>No specific template</SelectItem>
                    {TEMPLATE_OPTIONS.map(template => (
                      <SelectItem key={template.value} value={template.value}>
                        {template.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className='text-sm text-[var(--custom-gray-medium)]'>
                  Link this interview to a specific document template for
                  automatic data mapping.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Interview Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Interview Guidelines</CardTitle>
              <CardDescription>
                Best practices for creating effective interviews
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3 text-sm text-[var(--custom-gray-medium)]'>
                <div className='flex items-start space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                  <p>
                    Use clear, jargon-free language that's easy for seniors to
                    understand
                  </p>
                </div>
                <div className='flex items-start space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                  <p>
                    Group related questions by category (personal, financial,
                    estate, emergency)
                  </p>
                </div>
                <div className='flex items-start space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                  <p>
                    Include helpful tooltips and examples for complex questions
                  </p>
                </div>
                <div className='flex items-start space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                  <p>
                    Use conditional logic to create personalized question flows
                  </p>
                </div>
                <div className='flex items-start space-x-2'>
                  <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                  <p>
                    Map questions to template variables for automatic document
                    generation
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              <Save className='mr-2 h-4 w-4' />
              {saving
                ? 'Saving...'
                : isEditing
                  ? 'Update Interview'
                  : 'Create Interview'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
