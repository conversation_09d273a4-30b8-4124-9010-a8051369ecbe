'use client';

import React, { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Plus, Download } from 'lucide-react';
import { User } from '@/types/account';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { useUsers } from '@/hooks/useUsers';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

const client = generateClient<Schema>();

interface UserManagementTableProps {
  onEdit?: (user: User) => void;
  onCreateNew?: () => void;
  compact?: boolean;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Filter users...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'pending', label: 'Pending' },
        { value: 'deceased', label: 'Deceased' },
      ],
    },
    {
      id: 'role',
      title: 'Role',
      options: [
        { value: 'Member', label: 'Member' },
        { value: 'Administrator', label: 'Administrator' },
        { value: 'Welon Trust', label: 'Welon Trust' },
        { value: 'Professional', label: 'Professional' },
      ],
    },
    {
      id: 'subrole',
      title: 'Subrole',
      options: [
        { value: 'Basic Member', label: 'Basic Member' },
        { value: 'Advanced', label: 'Advanced' },
        { value: 'Emergency Service', label: 'Emergency Service' },
        { value: 'Medical', label: 'Medical' },
        { value: 'Finance', label: 'Finance' },
        { value: 'Reporting', label: 'Reporting' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

export function UserManagementTable({
  onEdit,
  onCreateNew,
  compact = false,
  className = '',
}: UserManagementTableProps) {
  // Use the custom hook to fetch and manage user data
  const { users, loading, error, updateUserStatus, removeUser } = useUsers();

  // State for export functionality
  const [isExporting, setIsExporting] = useState(false);

  // Transform users data to treat null/undefined status as 'pending' for filtering and facets
  const transformedUsers = useMemo(
    () =>
      users.map(user => ({
        ...user,
        status: user.status || 'pending',
      })),
    [users]
  );

  // Handle user actions
  const handleActivate = async (user: User) => {
    try {
      await updateUserStatus(user.id, 'active');
      toast.success(`User ${user.name} has been activated successfully`, {
        description: 'The user can now log in and access the application.',
      });
    } catch (err) {
      console.error('Failed to activate user:', err);
      toast.error(`Failed to activate user ${user.name}`, {
        description:
          err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };

  const handleDeactivate = async (user: User) => {
    try {
      await updateUserStatus(user.id, 'inactive');
      toast.success(`User ${user.name} has been deactivated successfully`, {
        description: 'The user can no longer log in to the application.',
      });
    } catch (err) {
      console.error('Failed to deactivate user:', err);
      toast.error(`Failed to deactivate user ${user.name}`, {
        description:
          err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };

  const handleDelete = async (user: User) => {
    try {
      await removeUser(user.id);
      toast.success(`User ${user.name} has been deleted successfully`, {
        description: 'The user has been permanently removed from the system.',
      });
    } catch (err) {
      console.error('Failed to delete user:', err);
      toast.error(`Failed to delete user ${user.name}`, {
        description:
          err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };

  // Handle export document update logs (all users)
  const handleExportDocumentLogs = async () => {
    setIsExporting(true);

    try {
      const result = await client.queries.exportDocumentUpdateLogs({
        // Optional parameters - can be extended later for filtering
        // startDate: undefined,
        // endDate: undefined,
        // userId: undefined,
        // changeType: undefined,
      });

      console.log('Export result:', result);

      if (result.errors) {
        console.error('Export errors:', result.errors);
        throw new Error('Failed to export document update logs');
      }

      const responseData = result.data as any;
      let parsedData = responseData;

      // Handle potential JSON string response
      if (typeof responseData === 'string') {
        try {
          parsedData = JSON.parse(responseData);
        } catch (e) {
          console.error('Failed to parse response as JSON:', e);
          throw new Error('Invalid response format from export function');
        }
      }

      if (!parsedData.success) {
        throw new Error(parsedData.message || 'Export failed');
      }

      const { filename, fileContent, mimeType, recordCount } = parsedData.data;

      if (!fileContent) {
        throw new Error('No file content received from export');
      }

      // Convert base64 to blob and trigger download
      const binaryString = atob(fileContent);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const blob = new Blob([bytes], { type: mimeType });
      const url = URL.createObjectURL(blob);

      // Create download link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Document update logs exported successfully', {
        description: `Downloaded ${recordCount} records to ${filename}`,
      });
    } catch (err) {
      console.error('Failed to export document update logs:', err);
      toast.error('Failed to export document update logs', {
        description:
          err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Handle export document update logs for specific user
  const handleExportUserDocumentLogs = async (user: User) => {
    try {
      console.log(
        `Starting document update logs export for user: ${user.name} (${user.id})`
      );

      // Call the exportDocumentUpdateLogs lambda function with user filter
      const result = await client.queries.exportDocumentUpdateLogs({
        userId: user.id,
        // Optional parameters for future enhancement
        // startDate: undefined,
        // endDate: undefined,
        // changeType: undefined,
      });

      console.log('Export result:', result);

      if (result.errors) {
        console.error('Export errors:', result.errors);
        throw new Error('Failed to export document update logs');
      }

      const responseData = result.data as any;
      let parsedData = responseData;

      // Handle potential JSON string response
      if (typeof responseData === 'string') {
        try {
          parsedData = JSON.parse(responseData);
        } catch (e) {
          console.error('Failed to parse response as JSON:', e);
          throw new Error('Invalid response format from export function');
        }
      }

      if (!parsedData.success) {
        throw new Error(parsedData.message || 'Export failed');
      }

      const { filename, fileContent, mimeType, recordCount } = parsedData.data;

      if (!fileContent) {
        throw new Error('No file content received from export');
      }

      // Convert base64 to blob and trigger download
      const binaryString = atob(fileContent);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const blob = new Blob([bytes], { type: mimeType });
      const url = URL.createObjectURL(blob);

      // Create download link and trigger download
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Document logs for ${user.name} exported successfully`, {
        description: `Downloaded ${recordCount} records to ${filename}`,
      });
    } catch (err) {
      console.error('Failed to export user document update logs:', err);
      toast.error(`Failed to export logs for ${user.name}`, {
        description:
          err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };
  // Define columns for the data table
  const columns: ColumnDef<User>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Name' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>{row.getValue('name')}</span>
      ),
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Email' />
      ),
      cell: ({ row }) => <span>{row.getValue('email')}</span>,
    },
    {
      accessorKey: 'role',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Role' />
      ),
      cell: ({ row }) => <span>{row.getValue('role')}</span>,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'subrole',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Subrole' />
      ),
      cell: ({ row }) => (
        <span className='text-sm text-muted-foreground'>
          {row.getValue('subrole')}
        </span>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        if (status === 'active') {
          return (
            <Badge className='bg-green-600 text-white hover:bg-green-700'>
              Active
            </Badge>
          );
        } else if (status === 'inactive') {
          return (
            <Badge
              variant='outline'
              className='border-gray-300 text-[var(--custom-gray-medium)]'
            >
              Inactive
            </Badge>
          );
        } else if (status === 'deceased') {
          return (
            <Badge
              variant='outline'
              className='border-red-500 text-red-600 bg-red-50'
            >
              Deceased
            </Badge>
          );
        } else {
          return (
            <Badge
              variant='outline'
              className='border-amber-500 text-amber-600'
            >
              Pending
            </Badge>
          );
        }
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'assignedWelonTrust',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Assigned Welon Trust' />
      ),
      cell: ({ row }) => {
        const user = row.original;
        if (user.role !== 'Member') {
          return <span className='text-sm text-muted-foreground'>N/A</span>;
        }

        if (!user.assignedWelonTrust) {
          return (
            <span className='text-sm text-muted-foreground'>Not assigned</span>
          );
        }

        const statusColor =
          user.assignedWelonTrust.status === 'active'
            ? 'text-green-600'
            : user.assignedWelonTrust.status === 'pending'
              ? 'text-amber-600'
              : 'text-red-600';

        return (
          <div className='flex flex-col gap-1'>
            <span className='text-sm font-medium'>
              {user.assignedWelonTrust.welonTrustName}
            </span>
            <span className={`text-xs capitalize ${statusColor}`}>
              {user.assignedWelonTrust.status}
            </span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(user)}>
                  Edit
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => handleExportUserDocumentLogs(user)}
              >
                Export Document Logs
              </DropdownMenuItem>
              {user.status === 'active' && (
                <DropdownMenuItem
                  onClick={() => handleDeactivate(user)}
                  className='text-red-600'
                >
                  Deactivate
                </DropdownMenuItem>
              )}
              {user.status === 'inactive' && (
                <DropdownMenuItem
                  onClick={() => handleActivate(user)}
                  className='text-green-600'
                >
                  Activate
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => handleDelete(user)}
                className='text-red-600'
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>User Management</h2>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            onClick={handleExportDocumentLogs}
            disabled={isExporting}
            size={compact ? 'sm' : 'default'}
            variant='outline'
          >
            <Download className='mr-2 h-4 w-4' />
            {isExporting ? 'Exporting...' : 'Export Document Logs'}
          </Button>
          {/* {onCreateNew && (
            <Button onClick={onCreateNew} size={compact ? 'sm' : 'default'}>
              <Plus className='mr-2 h-4 w-4' />
              Create User
            </Button>
          )} */}
        </div>
      </div>

      {/* Universal Data Table */}
      <DataTable
        columns={columns}
        data={transformedUsers}
        config={tableConfig}
        loading={loading}
        error={error}
      />
    </div>
  );
}
