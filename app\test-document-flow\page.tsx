'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Headline, Subhead } from '../../components/ui/brand/typography';
import {
  FileText,
  Scale,
  Package,
  Upload,
  ArrowRight,
  CheckCircle,
} from 'lucide-react';

export default function TestDocumentFlowPage() {
  const router = useRouter();

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>
            Document Review & Signing Flow Test
          </Headline>
          <Subhead className='text-muted-foreground'>
            Test the complete document review and signing workflow
            implementation with role switching
          </Subhead>
        </div>

        {/* Role Switching Instructions */}
        <Card className='mb-8 bg-green-50 border-green-200'>
          <CardHeader>
            <CardTitle className='text-green-800'>
              🔄 Role Switching Feature
            </CardTitle>
          </CardHeader>
          <CardContent className='text-green-700'>
            <div className='space-y-2'>
              <p className='font-medium'>How to test different user roles:</p>
              <ol className='list-decimal list-inside space-y-1 ml-4'>
                <li>Click on your user name in the top-right header</li>
                <li>Select "Switch Role (Testing)" from the dropdown</li>
                <li>
                  Choose from: Member, Administrator, Welon Trust, or Linked
                  Account
                </li>
                <li>
                  You'll be automatically redirected to the appropriate
                  dashboard for that role
                </li>
              </ol>
              <div className='mt-3 p-3 bg-green-100 rounded-md'>
                <p className='text-sm font-medium'>
                  Available Test Roles & Redirect Paths:
                </p>
                <ul className='text-sm mt-1 space-y-1'>
                  <li>
                    • <strong>Member:</strong> Redirects to{' '}
                    <code>/dashboard</code> - Full document workflow
                  </li>
                  <li>
                    • <strong>Administrator:</strong> Redirects to{' '}
                    <code>/admin</code> - System administration
                  </li>
                  <li>
                    • <strong>Welon Trust:</strong> Redirects to{' '}
                    <code>/emergency</code> - Emergency dashboard
                  </li>
                  <li>
                    • <strong>Linked Account:</strong> Redirects to{' '}
                    <code>/linked</code> - Limited access interface
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Flow Overview */}
        <Card className='mb-8 bg-blue-50 border-blue-200'>
          <CardHeader>
            <CardTitle className='text-blue-800'>
              Implementation Status
            </CardTitle>
          </CardHeader>
          <CardContent className='text-blue-700'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Member document review page</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>
                    Attorney list with state filtering
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Document signing workflow</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Welon staff upload interface</span>
                </div>
              </div>
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>PDF viewer modal</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Document validation checks</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Execution package generation</span>
                </div>
                <div className='flex items-center gap-2'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <span className='text-sm'>Dashboard integration</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Navigation */}
        <div className='grid gap-6'>
          {/* Member Flow */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-3'>
                <div className='bg-blue-100 p-2 rounded-lg'>
                  <FileText className='h-5 w-5 text-blue-600' />
                </div>
                Member Document Flow
              </CardTitle>
              <CardDescription>
                Test the complete member experience from document review to
                signing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>1. Document Review</p>
                    <p className='text-sm text-muted-foreground'>
                      Review generated documents with PDF viewer and annotations
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push('/documents/review')}
                  >
                    Test Review
                  </Button>
                </div>

                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>2. Attorney Review</p>
                    <p className='text-sm text-muted-foreground'>
                      Request attorney review from within document review
                      process
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => {
                      router.push('/documents/review');
                      setTimeout(() => {
                        alert(
                          "Click 'Request Attorney Review' button to access attorney list"
                        );
                      }, 1000);
                    }}
                  >
                    Test Attorney Flow
                  </Button>
                </div>

                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>3. Document Signing</p>
                    <p className='text-sm text-muted-foreground'>
                      Choose between review download or execution package
                      generation
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push('/member/documents/sign')}
                  >
                    Test Signing
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Welon Staff Flow */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-3'>
                <div className='bg-green-100 p-2 rounded-lg'>
                  <Upload className='h-5 w-5 text-green-600' />
                </div>
                Welon Staff Flow
              </CardTitle>
              <CardDescription>
                Test the Welon Trust staff document upload and validation
                interface
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Staff Document Upload</p>
                    <p className='text-sm text-muted-foreground'>
                      Upload signed documents with validation checklist and
                      member notification
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push('/staff/documents/upload')}
                  >
                    Test Upload Interface
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dashboard Integration */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-3'>
                <div className='bg-purple-100 p-2 rounded-lg'>
                  <Package className='h-5 w-5 text-purple-600' />
                </div>
                Dashboard Integration
              </CardTitle>
              <CardDescription>
                Test the updated dashboard with document review links
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Updated Dashboard</p>
                    <p className='text-sm text-muted-foreground'>
                      Dashboard now includes document review and signing buttons
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push('/dashboard')}
                  >
                    View Dashboard
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Linked Account Flow */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-3'>
                <div className='bg-orange-100 p-2 rounded-lg'>
                  <ArrowRight className='h-5 w-5 text-orange-600' />
                </div>
                Linked Account Flow
              </CardTitle>
              <CardDescription>
                Test the limited access interface for linked accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Linked Account Dashboard</p>
                    <p className='text-sm text-muted-foreground'>
                      Limited access interface with shared documents and
                      emergency features
                    </p>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push('/linked')}
                  >
                    Test Linked Account
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Implementation Notes */}
        <Card className='mt-8 bg-yellow-50 border-yellow-200'>
          <CardHeader>
            <CardTitle className='text-yellow-800'>
              Implementation Notes
            </CardTitle>
          </CardHeader>
          <CardContent className='text-yellow-700'>
            <div className='space-y-2 text-sm'>
              <p>
                <strong>Missing from Current Implementation:</strong>
              </p>
              <ul className='list-disc list-inside space-y-1 ml-4'>
                <li>
                  Real PDF viewer integration (currently shows placeholder)
                </li>
                <li>Actual AWS S3 file upload and storage</li>
                <li>Real-time document status tracking</li>
                <li>Email notifications via AWS SES</li>
                <li>UPS shipping label generation</li>
                <li>Attorney database integration</li>
                <li>Document validation with AWS Lambda</li>
              </ul>

              <p className='mt-4'>
                <strong>What's Implemented:</strong>
              </p>
              <ul className='list-disc list-inside space-y-1 ml-4'>
                <li>Complete UI/UX for document review workflow</li>
                <li>Attorney list with filtering and search</li>
                <li>Document signing options with validation</li>
                <li>Welon staff upload interface with validation checklist</li>
                <li>Proper routing and navigation between pages</li>
                <li>
                  Role-based access control structure with JSON configuration
                </li>
                <li>Role switching system for testing different user types</li>
                <li>Dynamic sidebar navigation based on user role</li>
                <li>Linked account interface with limited permissions</li>
                <li>Responsive design for all components</li>
              </ul>

              <p className='mt-4'>
                <strong>Role System Features:</strong>
              </p>
              <ul className='list-disc list-inside space-y-1 ml-4'>
                <li>
                  JSON-based role configuration with permissions and routes
                </li>
                <li>Role context provider with localStorage persistence</li>
                <li>Dynamic header with role switching dropdown</li>
                <li>Role-specific sidebar navigation and layouts</li>
                <li>
                  Support for subroles (e.g., Welon Basic, Admin Advanced)
                </li>
                <li>Permission-based access control for routes and features</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Quick Navigation */}
        <div className='mt-8 flex justify-center'>
          <Button
            onClick={() => router.push('/dashboard')}
            className='bg-blue-600 hover:bg-blue-700'
          >
            <ArrowRight className='h-4 w-4 mr-2' />
            Return to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}
