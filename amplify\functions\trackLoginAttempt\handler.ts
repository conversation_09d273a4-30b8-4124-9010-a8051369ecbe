// @ts-nocheck

import type { <PERSON><PERSON> } from 'aws-lambda';
import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/trackLoginAttempt';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

interface LoginAttemptEvent {
  email: string;
  success?: boolean;
  ipAddress?: string;
  userAgent?: string;
  action?: 'track' | 'check';
}

export const handler: Handler = async event => {
  try {
    // Handle both GraphQL and direct invocation
    let email: string;
    let success: boolean | undefined;
    let ipAddress: string | undefined;
    let userAgent: string | undefined;
    let action: string = 'track';

    if (event.arguments) {
      // GraphQL request
      email = event.arguments.email;
      success = event.arguments.success;
      ipAddress = event.arguments.ipAddress;
      userAgent = event.arguments.userAgent;
      action = event.arguments.action || 'track'; // Get action from arguments
    } else {
      // Direct invocation
      const body: LoginAttemptEvent =
        typeof event.body === 'string' ? JSON.parse(event.body) : event.body;

      email = body.email;
      success = body.success;
      ipAddress = body.ipAddress;
      userAgent = body.userAgent;
      action = body.action || 'track';
    }

    if (!email) {
      if (event.arguments) {
        throw new Error('Email is required');
      }
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Email is required' }),
      };
    }

    // Validate action parameter
    if (action !== 'check' && action !== 'track') {
      const error = `Invalid action: ${action}. Must be 'check' or 'track'`;

      if (event.arguments) {
        throw new Error(error);
      }
      return {
        statusCode: 400,
        body: JSON.stringify({ error }),
      };
    }

    // PRIORITY: If action is 'check', ALWAYS check lockout status first
    if (action === 'check') {
      try {
        const dbResult = await client.models.LoginAttempt.get({ email });
        const existingRecord = dbResult.data;

        if (existingRecord?.lockoutUntil) {
          const lockoutTime = new Date(existingRecord.lockoutUntil);
          const now = new Date();

          console.log('⏰ Lockout time check:', {
            lockoutTimeRaw: existingRecord.lockoutUntil,
            lockoutTime: lockoutTime.toISOString(),
            lockoutTimeValid: !isNaN(lockoutTime.getTime()),
            now: now.toISOString(),
            isStillLocked: now < lockoutTime,
            minutesRemaining: Math.ceil(
              (lockoutTime.getTime() - now.getTime()) / (1000 * 60)
            ),
          });

          // Check if lockoutTime is valid and still in the future
          if (!isNaN(lockoutTime.getTime()) && now < lockoutTime) {
            const result = {
              isLocked: true,
              lockoutUntil: existingRecord.lockoutUntil,
              failedAttempts: existingRecord.failedAttempts,
            };

            return event.arguments
              ? result
              : {
                  statusCode: 200,
                  body: JSON.stringify(result),
                };
          } else {
            // Clear the invalid lockout time
            if (existingRecord && isNaN(lockoutTime.getTime())) {
              await client.models.LoginAttempt.update({
                email,
                lockoutUntil: null,
              });
            }
          }
        }

        const result = {
          isLocked: false,
          failedAttempts: existingRecord?.failedAttempts || 0,
        };
        console.log('✅ Account is NOT locked, returning:', result);
        return event.arguments
          ? result
          : {
              statusCode: 200,
              body: JSON.stringify(result),
            };
      } catch (error) {
        console.log(
          '❌ Error checking lockout (no record found):',
          error.message
        );
        const result = {
          isLocked: false,
          failedAttempts: 0,
        };
        console.log('✅ No record found, returning default:', result);
        return event.arguments
          ? result
          : {
              statusCode: 200,
              body: JSON.stringify(result),
            };
      }
    }

    // Only validate success parameter for tracking actions
    if (success === undefined) {
      if (event.arguments) {
        throw new Error('Success status is required for tracking');
      }
      return {
        statusCode: 400,
        body: JSON.stringify({
          error: 'Success status is required for tracking',
        }),
      };
    }

    console.log('📝 Starting login attempt tracking for:', { email, success });
    const now = new Date().toISOString();

    // Get existing login attempt record
    let existingRecord;
    try {
      const result = await client.models.LoginAttempt.get({ email });
      existingRecord = result.data;
      console.log('📊 Found existing record:', {
        hasRecord: !!existingRecord,
        currentFailedAttempts: existingRecord?.failedAttempts,
      });
    } catch (error) {
      console.log('📝 No existing record found, will create new one');
    }

    if (success) {
      console.log('✅ Processing SUCCESSFUL login attempt');

      // Create a login history record for successful login
      try {
        await client.models.LoginHistory.create({
          email,
          attemptTime: now,
          success: true,
          ipAddress,
          userAgent,
          sessionId: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        });
        console.log('📝 Created login history record');
      } catch (historyError) {
        console.error('❌ Error creating login history record:', historyError);
        // Don't fail the entire operation if history creation fails
      }

      // Successful login - reset failed attempts in LoginAttempt table
      if (existingRecord) {
        console.log('🔄 Updating existing record - resetting failed attempts');
        await client.models.LoginAttempt.update({
          email,
          attemptTime: now,
          success: true,
          ipAddress,
          userAgent,
          failedAttempts: 0,
          lockoutUntil: null,
        });
      } else {
        console.log('➕ Creating new record for successful login');
        await client.models.LoginAttempt.create({
          email,
          attemptTime: now,
          success: true,
          ipAddress,
          userAgent,
          failedAttempts: 0,
        });
      }

      const result = {
        message: 'Login attempt recorded successfully',
        isLocked: false,
      };
      console.log('✅ Successful login result:', result);
      return event.arguments
        ? result
        : {
            statusCode: 200,
            body: JSON.stringify(result),
          };
    } else {
      console.log('❌ Processing FAILED login attempt');
      // Failed login attempt
      const currentFailedAttempts = existingRecord?.failedAttempts || 0;
      const newFailedAttempts = currentFailedAttempts + 1;

      console.log('📊 Failed attempt count:', {
        currentFailedAttempts,
        newFailedAttempts,
        willLockAccount: newFailedAttempts >= 5,
      });

      // Check if account should be locked (5 failed attempts)
      let lockoutUntil = null;
      if (newFailedAttempts >= 5) {
        // Lock for 15 minutes
        const lockoutTime = new Date();
        lockoutTime.setTime(lockoutTime.getTime() + 15 * 60 * 1000); // Add 15 minutes in milliseconds
        lockoutUntil = lockoutTime.toISOString();
        console.log('🔒 LOCKING ACCOUNT until:', {
          lockoutUntil,
          lockoutTimeValid: !isNaN(lockoutTime.getTime()),
          minutesFromNow: 15,
        });
      }

      if (existingRecord) {
        console.log('🔄 Updating existing record with failed attempt');
        await client.models.LoginAttempt.update({
          email,
          attemptTime: now,
          success: false,
          ipAddress,
          userAgent,
          failedAttempts: newFailedAttempts,
          lockoutUntil,
        });
      } else {
        console.log('➕ Creating new record for failed attempt');
        await client.models.LoginAttempt.create({
          email,
          attemptTime: now,
          success: false,
          ipAddress,
          userAgent,
          failedAttempts: newFailedAttempts,
          lockoutUntil,
        });
      }

      const result = {
        message: 'Failed login attempt recorded',
        isLocked: newFailedAttempts >= 5,
        failedAttempts: newFailedAttempts,
        lockoutUntil,
      };
      console.log('❌ Failed login result:', result);
      return event.arguments
        ? result
        : {
            statusCode: 200,
            body: JSON.stringify(result),
          };
    }
  } catch (error) {
    console.error('Error tracking login attempt:', error);
    if (event.arguments) {
      throw error;
    }
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' }),
    };
  }
};
