'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Plus,
  Pencil,
  Trash,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useLivingDocuments } from '@/hooks/useLivingDocuments';
import { LivingDocumentModal } from '@/components/dashboard/LivingDocumentModal';
import useModal from '@/hooks/useModal';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface LivingDocument {
  id: string;
  title: string;
  documentType:
    | 'EmergencyContacts'
    | 'PetCare'
    | 'DigitalAssets'
    | 'EndOfLifeWishes'
    | 'MedicalDirectives'
    | 'Other';
  content: string;
  version: number;
  status: 'Draft' | 'Active' | 'Archived';
  lastReviewDate?: string;
  nextReviewDate?: string;
  reminderFrequency: 'Monthly' | 'Quarterly' | 'SemiAnnually' | 'Annually';
  isTemplate: boolean;
  createdAt?: string;
  updatedAt?: string;
}

const documentTypeLabels = {
  EmergencyContacts: 'Emergency Contacts',
  PetCare: 'Pet Care Instructions',
  DigitalAssets: 'Digital Assets',
  EndOfLifeWishes: 'End of Life Wishes',
  MedicalDirectives: 'Medical Directives',
  Other: 'Other',
};

export default function LivingDocumentsPage() {
  const {
    documents,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument,
    getDocumentStatus,
  } = useLivingDocuments();
  const documentModal = useModal();
  const [selectedDocument, setSelectedDocument] = useState<
    LivingDocument | undefined
  >(undefined);
  const deleteConfirmModal = useModal();
  const [documentToDelete, setDocumentToDelete] =
    useState<LivingDocument | null>(null);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const handleAddDocument = () => {
    setSelectedDocument(undefined);
    documentModal.showModal();
  };

  const handleEditDocument = (document: LivingDocument) => {
    setSelectedDocument(document);
    documentModal.showModal();
  };

  const handleDeleteClick = (document: LivingDocument) => {
    setDocumentToDelete(document);
    deleteConfirmModal.showModal();
  };

  const handleConfirmDelete = async () => {
    if (documentToDelete) {
      try {
        await deleteDocument(documentToDelete.id);
        deleteConfirmModal.hideModal();
        setDocumentToDelete(null);
      } catch (error) {
        console.error('Error deleting document:', error);
      }
    }
  };

  const handleSaveDocument = async (
    documentData: Omit<
      LivingDocument,
      'id' | 'version' | 'createdAt' | 'updatedAt'
    >
  ) => {
    try {
      if (selectedDocument) {
        await updateDocument(selectedDocument.id, documentData);
      } else {
        await addDocument(documentData);
      }
      documentModal.hideModal();
    } catch (error) {
      console.error('Error saving document:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'due-soon':
        return <Clock className='h-4 w-4 text-yellow-500' />;
      case 'overdue':
        return <AlertCircle className='h-4 w-4 text-red-500' />;
      default:
        return <CheckCircle className='h-4 w-4 text-green-500' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'text-green-600';
      case 'due-soon':
        return 'text-yellow-600';
      case 'overdue':
        return 'text-red-600';
      default:
        return 'text-green-600';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  // Filter documents
  const filteredDocuments = documents.filter(document => {
    const typeMatch =
      filterType === 'all' || document.documentType === filterType;
    const statusMatch =
      filterStatus === 'all' || document.status === filterStatus;
    return typeMatch && statusMatch;
  });

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='max-w-4xl mx-auto'>
          <h1 className='text-3xl font-bold mb-8'>Living Documents</h1>
          <p>Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        <div className='flex justify-between items-center mb-8'>
          <div>
            <h1 className='text-3xl font-bold'>Living Documents</h1>
            <p className='text-[var(--custom-gray-medium)] mt-2'>
              Manage documents that require regular updates and reviews.
            </p>
          </div>
          <Button
            className='border-[var(--yellow-green)] bg-[var(--yellow-green)] text-white hover:bg-[var(--yellow-green)] hover:text-white cursor-pointer'
            variant='outline'
            onClick={handleAddDocument}
          >
            <Plus className='mr-2 h-4 w-4' />
            Create Document
          </Button>
        </div>

        {/* Info Card */}
        <Card className='mb-6'>
          <CardContent className='pt-6'>
            <div className='flex items-start space-x-3'>
              <FileText className='h-5 w-5 text-blue-500 mt-0.5' />
              <div>
                <h3 className='font-medium'>About Living Documents</h3>
                <p className='text-sm text-[var(--custom-gray-medium)] mt-1'>
                  Living documents are dynamic documents that need regular
                  updates to stay current. Set reminder frequencies to ensure
                  your important information stays up-to-date.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <div className='flex gap-4 mb-6'>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Filter by type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Types</SelectItem>
              {Object.entries(documentTypeLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Statuses</SelectItem>
              <SelectItem value='Active'>Active</SelectItem>
              <SelectItem value='Draft'>Draft</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Error State */}
        {error && (
          <Card className='mb-6'>
            <CardContent className='pt-6'>
              <div className='text-red-500'>
                <p>Error loading documents: {error}</p>
                <p className='text-sm mt-1'>
                  Please try again later or contact support.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Documents List */}
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='pt-6'>
              <div className='text-center py-8'>
                <FileText className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
                <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                  No documents found
                </h3>
                <p className='text-[var(--custom-gray-medium)] mb-6'>
                  {documents.length === 0
                    ? 'Create your first living document to get started.'
                    : 'No documents match your current filters.'}
                </p>
                <Button onClick={handleAddDocument}>
                  <Plus className='mr-2 h-4 w-4' />
                  Create Your First Document
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className='space-y-4'>
            {filteredDocuments.map(document => {
              const status = getDocumentStatus(document);
              return (
                <Card key={document.id} className='bg-background'>
                  <CardContent className='p-6'>
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-2 mb-2'>
                          <h3 className='text-lg font-semibold'>
                            {document.title}
                          </h3>
                          <Badge variant='outline' className='text-xs'>
                            {documentTypeLabels[document.documentType]}
                          </Badge>
                          {document.status === 'Draft' && (
                            <Badge
                              variant='outline'
                              className='text-xs border-gray-500'
                            >
                              Draft
                            </Badge>
                          )}
                          {document.isTemplate && (
                            <Badge
                              variant='outline'
                              className='text-xs border-blue-500 text-blue-600'
                            >
                              Template
                            </Badge>
                          )}
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-[var(--custom-gray-medium)] mb-4'>
                          <div>
                            <span className='font-medium'>Version:</span>{' '}
                            {document.version}
                          </div>
                          <div>
                            <span className='font-medium'>Last Updated:</span>{' '}
                            {formatDate(document.updatedAt)}
                          </div>
                          <div>
                            <span className='font-medium'>Next Review:</span>{' '}
                            {formatDate(document.nextReviewDate)}
                          </div>
                          <div
                            className={`flex items-center gap-1 ${getStatusColor(status)}`}
                          >
                            {getStatusIcon(status)}
                            <span className='capitalize'>
                              {status.replace('-', ' ')}
                            </span>
                          </div>
                        </div>

                        <p className='text-sm text-[var(--custom-gray-medium)] line-clamp-2'>
                          {document.content.length > 150
                            ? `${document.content.substring(0, 150)}...`
                            : document.content}
                        </p>
                      </div>

                      <div className='flex gap-2 ml-4'>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditDocument(document)}
                        >
                          <Pencil className='h-4 w-4' />
                        </Button>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleDeleteClick(document)}
                          className='text-red-600 hover:text-red-700'
                        >
                          <Trash className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Modals */}
        <LivingDocumentModal
          open={documentModal.isVisible}
          onOpenChange={documentModal.hideModal}
          onSave={handleSaveDocument}
          document={selectedDocument}
        />

        <ConfirmationDialog
          open={deleteConfirmModal.isVisible}
          onOpenChange={deleteConfirmModal.hideModal}
          onConfirm={handleConfirmDelete}
          title='Delete Document'
          description={`Are you sure you want to delete "${documentToDelete?.title}"? This action cannot be undone.`}
          confirmLabel='Delete'
          cancelLabel='Cancel'
          confirmVariant='destructive'
        />
      </div>
    </div>
  );
}
