'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Upload,
  Video,
  FileText,
  Image,
  BookOpen,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  BarChart3,
} from 'lucide-react';

// Mock content data for admin
const mockAdminContent = {
  pending: [
    {
      id: 'p1',
      title: 'Advanced Trust Strategies',
      type: 'video',
      category: 'Trusts',
      author: '<PERSON>',
      uploadDate: '2025-01-20',
      status: 'pending',
      size: '45 MB',
    },
    {
      id: 'p2',
      title: 'Digital Estate Planning Guide',
      type: 'article',
      category: 'Wills',
      author: '<PERSON>',
      uploadDate: '2025-01-19',
      status: 'pending',
      size: '2 MB',
    },
  ],
  approved: [
    {
      id: 'a1',
      title: 'Estate Planning Basics',
      type: 'video',
      category: 'Wills',
      author: 'Mike <PERSON>',
      uploadDate: '2025-01-15',
      status: 'approved',
      views: 1250,
      rating: 4.8,
    },
    {
      id: 'a2',
      title: 'Power of Attorney Guide',
      type: 'infographic',
      category: 'Power of Attorney',
      author: 'Emma Davis',
      uploadDate: '2025-01-10',
      status: 'approved',
      views: 890,
      rating: 4.6,
    },
  ],
};

export default function AdminContentPage() {
  const [activeTab, setActiveTab] = useState('upload');
  const [uploadForm, setUploadForm] = useState<{
    title: string;
    description: string;
    category: string;
    type: string;
    tags: string;
    file: File | null;
  }>({
    title: '',
    description: '',
    category: 'Wills',
    type: 'video',
    tags: '',
    file: null,
  });

  const handleUpload = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Uploading content:', uploadForm);
    // In a real app, this would upload to S3 and create DynamoDB record
  };

  const handleApprove = (contentId: string) => {
    console.log('Approving content:', contentId);
    // In a real app, this would update status in DynamoDB
  };

  const handleReject = (contentId: string) => {
    console.log('Rejecting content:', contentId);
    // In a real app, this would update status in DynamoDB
  };

  const handleDelete = (contentId: string) => {
    console.log('Deleting content:', contentId);
    // In a real app, this would remove from S3 and DynamoDB
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold mb-4 text-[var(--custom-gray-dark)]'>
          Content Management
        </h1>
        <p className='text-lg text-[var(--custom-gray-medium)] mb-6'>
          Upload, manage, and approve educational content for the platform
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-4 mb-8'>
          <TabsTrigger value='upload' className='flex items-center gap-2'>
            <Upload className='h-4 w-4' />
            Upload Content
          </TabsTrigger>
          <TabsTrigger value='pending' className='flex items-center gap-2'>
            <Clock className='h-4 w-4' />
            Pending Approval
          </TabsTrigger>
          <TabsTrigger value='approved' className='flex items-center gap-2'>
            <CheckCircle className='h-4 w-4' />
            Approved Content
          </TabsTrigger>
          <TabsTrigger value='analytics' className='flex items-center gap-2'>
            <BarChart3 className='h-4 w-4' />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Upload Content Tab */}
        <TabsContent value='upload'>
          <Card>
            <CardHeader>
              <CardTitle>Upload New Content</CardTitle>
              <CardDescription>
                Add new educational content to the platform. All content will be
                reviewed before publication.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpload} className='space-y-6'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                  <div>
                    <label className='block text-sm font-medium mb-2'>
                      Content Type
                    </label>
                    <select
                      value={uploadForm.type}
                      onChange={e =>
                        setUploadForm({ ...uploadForm, type: e.target.value })
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--eggplant)]'
                    >
                      <option value='video'>Video</option>
                      <option value='article'>Article</option>
                      <option value='infographic'>Infographic</option>
                      <option value='guide'>Guide/Template</option>
                    </select>
                  </div>
                  <div>
                    <label className='block text-sm font-medium mb-2'>
                      Category
                    </label>
                    <select
                      value={uploadForm.category}
                      onChange={e =>
                        setUploadForm({
                          ...uploadForm,
                          category: e.target.value,
                        })
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--eggplant)]'
                    >
                      <option value='Wills'>Wills</option>
                      <option value='Trusts'>Trusts</option>
                      <option value='Power of Attorney'>
                        Power of Attorney
                      </option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className='block text-sm font-medium mb-2'>
                    Title
                  </label>
                  <Input
                    type='text'
                    value={uploadForm.title}
                    onChange={e =>
                      setUploadForm({ ...uploadForm, title: e.target.value })
                    }
                    placeholder='Enter content title'
                    required
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium mb-2'>
                    Description
                  </label>
                  <Textarea
                    value={uploadForm.description}
                    onChange={e =>
                      setUploadForm({
                        ...uploadForm,
                        description: e.target.value,
                      })
                    }
                    placeholder='Enter content description'
                    rows={4}
                    required
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium mb-2'>
                    Tags (comma-separated)
                  </label>
                  <Input
                    type='text'
                    value={uploadForm.tags}
                    onChange={e =>
                      setUploadForm({ ...uploadForm, tags: e.target.value })
                    }
                    placeholder='e.g., onboarding, basics, advanced'
                  />
                </div>

                <div>
                  <label className='block text-sm font-medium mb-2'>
                    File Upload
                  </label>
                  <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
                    <Upload className='h-12 w-12 text-[var(--custom-gray-medium)] mx-auto mb-4' />
                    <p className='text-[var(--custom-gray-medium)] mb-2'>
                      Drag and drop your file here, or click to browse
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Supports: MP4, PDF, PNG, JPG (Max 100MB)
                    </p>
                    <input
                      type='file'
                      className='hidden'
                      accept='.mp4,.pdf,.png,.jpg,.jpeg'
                      onChange={e =>
                        setUploadForm({
                          ...uploadForm,
                          file: e.target.files?.[0] ?? null,
                        })
                      }
                    />
                    <Button type='button' variant='outline' className='mt-4'>
                      Choose File
                    </Button>
                  </div>
                </div>

                <div className='flex justify-end'>
                  <Button type='submit' variant='default'>
                    Upload Content
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pending Approval Tab */}
        <TabsContent value='pending'>
          <div className='space-y-4'>
            <div className='flex justify-between items-center'>
              <h2 className='text-xl font-semibold'>
                Pending Approval ({mockAdminContent.pending.length})
              </h2>
            </div>

            {mockAdminContent.pending.map(content => (
              <Card key={content.id}>
                <CardContent className='p-6'>
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-2'>
                        {content.type === 'video' && (
                          <Video className='h-4 w-4' />
                        )}
                        {content.type === 'article' && (
                          <FileText className='h-4 w-4' />
                        )}
                        {content.type === 'infographic' && (
                          <Image className='h-4 w-4' />
                        )}
                        <h3 className='font-semibold'>{content.title}</h3>
                        <Badge variant='outline'>{content.category}</Badge>
                      </div>
                      <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                        Uploaded by {content.author} on{' '}
                        {new Date(content.uploadDate).toLocaleDateString()}
                      </p>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Size: {content.size}
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Button variant='outline' size='sm'>
                        <Eye className='h-4 w-4 mr-1' />
                        Preview
                      </Button>
                      <Button
                        variant='default'
                        size='sm'
                        onClick={() => handleApprove(content.id)}
                      >
                        <CheckCircle className='h-4 w-4 mr-1' />
                        Approve
                      </Button>
                      <Button
                        variant='destructive'
                        size='sm'
                        onClick={() => handleReject(content.id)}
                      >
                        <XCircle className='h-4 w-4 mr-1' />
                        Reject
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Approved Content Tab */}
        <TabsContent value='approved'>
          <div className='space-y-4'>
            <div className='flex justify-between items-center'>
              <h2 className='text-xl font-semibold'>
                Approved Content ({mockAdminContent.approved.length})
              </h2>
            </div>

            {mockAdminContent.approved.map(content => (
              <Card key={content.id}>
                <CardContent className='p-6'>
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-2'>
                        {content.type === 'video' && (
                          <Video className='h-4 w-4' />
                        )}
                        {content.type === 'article' && (
                          <FileText className='h-4 w-4' />
                        )}
                        {content.type === 'infographic' && (
                          <Image className='h-4 w-4' />
                        )}
                        <h3 className='font-semibold'>{content.title}</h3>
                        <Badge variant='outline'>{content.category}</Badge>
                        <Badge className='bg-green-100 text-green-800'>
                          Live
                        </Badge>
                      </div>
                      <p className='text-sm text-[var(--custom-gray-medium)] mb-2'>
                        By {content.author} • {content.views} views • Rating:{' '}
                        {content.rating}/5
                      </p>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Published:{' '}
                        {new Date(content.uploadDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Button variant='outline' size='sm'>
                        <Eye className='h-4 w-4 mr-1' />
                        View
                      </Button>
                      <Button variant='outline' size='sm'>
                        <Edit className='h-4 w-4 mr-1' />
                        Edit
                      </Button>
                      <Button
                        variant='destructive'
                        size='sm'
                        onClick={() => handleDelete(content.id)}
                      >
                        <Trash2 className='h-4 w-4 mr-1' />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value='analytics'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between mb-2'>
                  <BookOpen className='h-8 w-8 text-blue-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                    Total Content
                  </p>
                  <p className='text-2xl font-bold'>24</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between mb-2'>
                  <Eye className='h-8 w-8 text-green-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                    Total Views
                  </p>
                  <p className='text-2xl font-bold'>12,450</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between mb-2'>
                  <BarChart3 className='h-8 w-8 text-yellow-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                    Avg. Rating
                  </p>
                  <p className='text-2xl font-bold'>4.7</p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between mb-2'>
                  <Users className='h-8 w-8 text-purple-600' />
                </div>
                <div>
                  <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                    Active Users
                  </p>
                  <p className='text-2xl font-bold'>1,234</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Content Performance</CardTitle>
              <CardDescription>
                Detailed analytics for each piece of content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className='text-[var(--custom-gray-medium)]'>
                Analytics dashboard would be implemented here with charts and
                detailed metrics.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
