# 4 \- Document Generation

# **Document 4 \- Functional Specification: Document Generation**

## **Overview**

The Document Generation process enables Members of the Childfree Legacy Web Application to create personalized, legally compliant estate planning documents—including wills, trusts, and powers of attorney (POAs)—tailored to their state-specific legal requirements and individual inputs. Leveraging AWS Amplify, this specification details how the system selects state-appropriate templates, maps and populates user data, and manages document versions over time. It prioritizes robust security (e.g., HIPAA and SOC2 compliance), legal accuracy, and accessibility, simplifying estate planning for the 65+ demographic. AWS Amplify’s serverless architecture, data storage, and encryption features ensure a scalable, secure, and intuitive experience.

---

## **1\. Template Selection**

The system retrieves state-specific legal templates based on the Member’s primary residence, ensuring compliance with local estate laws.

### **Key Requirements**

#### **Template Types**

- Wills
- Trusts (e.g., revocable living trusts)
- Powers of Attorney (POA) for healthcare and finances
- Advance Healthcare Directives

#### **State-Specific Variants**

- Templates vary by state to comply with local regulations (e.g., witness or notarization requirements).

#### **Template Storage**

- Stored in **Amazon DynamoDB** with versioning enabled.
- Administrators update templates to reflect legal changes.

#### **Selection Logic**

- Automatically selects templates based on the Member’s state, retrieved from their profile.
- Supports Administrator overrides for cases like multi-state residency.

#### **Document Preview**

- Members preview documents with a "Draft" watermark before finalization.

#### **Storage Policy**

- Signed documents are stored permanently in **Amazon S3**; unsigned drafts are generated on-demand and not retained.

### **User Flow**

1. Member initiates document creation at /Member/documents/create.
2. System fetches the Member’s state from **DynamoDB** via **AWS AppSync**.
3. Queries the template database for the requested document type and state.
4. Prepares the selected template for data population.

### **Edge Cases**

- **Missing State Data**: Prompts Member to update their profile address.
- **Unsupported State**: Displays: "Service unavailable in \[state\]. Contact support."
- **Template Updates**: Notifies Members via **AWS SES** if a template updates post-generation, recommending regeneration and re-signing.

### **Compliance Considerations**

- **Legal Accuracy**: Templates are vetted by state-specific legal experts.
- **Auditability**: Template selections are logged in **CloudWatch** for compliance tracking.
- **Multi-Jurisdiction Compliance**: Maintains a DynamoDB library of state-specific templates, adhering to local laws (e.g., witness requirements, notarization rules).

### **UI Components**

| Element                  | Description                           |
| ------------------------ | ------------------------------------- |
| Document Type Dropdown   | Selects document (e.g., Will, Trust)  |
| State Display            | Shows detected state from profile     |
| "Create Document" Button | Triggers template selection           |
| Error Message            | Displays issues (e.g., missing state) |

---

## **2\. Data Mapping and Population**

This section outlines how user inputs and profile data are mapped to template fields and populated into documents.

### **Key Requirements**

#### **Data Sources**

- Member responses from estate planning interviews, stored in **DynamoDB**.
- Profile data (e.g., name, address) retrieved via **AWS AppSync**.

#### **Mapping Rules**

- Links template fields (e.g., "beneficiary_name") to specific inputs.
- Uses conditional logic for optional sections (e.g., pet care clause if pets are specified).

#### **Data Validation**

- Ensures all required fields are completed.
- Flags missing or invalid data for correction.

#### **Personalization**

- Incorporates user-specific details (e.g., asset descriptions, beneficiary allocations).

### **User Flow**

1. Loads the selected template from **DynamoDB**.
2. Maps user inputs and profile data to fields using **AWS Lambda**.
3. Applies conditional logic to include/exclude sections.
4. Generates a draft document, temporarily stored in **S3**, for preview.

### **Edge Cases**

- **Incomplete Data**: Highlights missing fields and prompts Member to complete them.
- **Complex Inputs**: Supports attachments (e.g., property lists) stored in **S3**.
- **Data Errors**: Validates formats (e.g., dates, percentages summing to 100%).

### **Compliance Considerations**

- **Data Integrity**: Verifies population accuracy with checksums.
- **Privacy**: Encrypts sensitive data (e.g., Social Security numbers) using **AWS KMS**.

### **UI Components**

| Element              | Description                                     |
| -------------------- | ----------------------------------------------- |
| Input Review Table   | Displays mapped data for verification           |
| "Add Details" Button | Allows additional entries (e.g., beneficiaries) |
| Warning Message      | Flags missing/invalid data                      |

---

## **3\. Jurisdiction Management**

This section addresses multi-jurisdiction compliance, keeping the system aligned with state-specific legal requirements.

### **Key Requirements**

#### **Jurisdiction Library**

- Centralized repository of state-specific templates and rules in **DynamoDB**.
- Updated quarterly or as laws change by legal experts.

#### **Template Updates**

- Sends automated alerts to Administrators via **AWS SNS** when state laws change.
- Implements version control for templates.

#### **Exception Handling**

- Prompts users with specific instructions for states with unique requirements (e.g., additional witness forms).

#### **Future Expansion**

- Designed to support additional jurisdictions (e.g., international) by expanding the template library.

### **Compliance Management Process**

1. **Legal Monitoring**: Legal team tracks state law changes and updates the library.
2. **Template Revision**: Administrators update templates in **DynamoDB**, incrementing versions.
3. **User Notification**: Notifies affected Members via **AWS SES** to review and regenerate documents.
4. **Audit Trails**: Logs updates and notifications in **CloudWatch**.

### **Technical Implementation**

- **Database Structure**:
  - jurisdiction_templates: Columns include state, document_type, version, content, last_updated.
  - compliance_rules: Stores rules (e.g., witness requirements) linked to templates.
- **GraphQL APIs (via AppSync)**:
  - getJurisdictionTemplates: Retrieves templates by state and type.
  - updateJurisdiction: Enables Administrator updates.

### **User Flow (Administrator)**

1. Receives legal update alert via **SNS**.
2. Updates the relevant template in **DynamoDB**.
3. System logs the update and notifies affected Members via **SES**.

### **Edge Cases**

- **Multi-State Members**: Allows jurisdiction selection or guidance for multi-state planning.

---

## **4\. Document Preview and Confirmation**

Members review and confirm generated documents to ensure accuracy before finalization.

### **Key Requirements**

#### **Preview Functionality**

- Renders documents in a readable format (e.g., embedded PDF viewer).
- Highlights missing or editable fields.
- Requires payment completion.

#### **Editing**

- Allows direct edits from the preview page, re-populating via **Lambda**.

#### **Confirmation**

- Requires explicit approval (e.g., "I approve this document" checkbox).
- Logs confirmation in **CloudWatch**.

### **User Flow**

1. Member navigates to /Member/documents/preview.
2. System displays the document from **S3** with editable sections highlighted.
3. Member edits data, triggering re-population via **Lambda**.
4. Confirms via "Approve" button.
5. Saves the final document in **S3** and redirects to /Member/documents.

### **Edge Cases**

- **Unconfirmed Documents**: Blocks download/signing until approved.
- **Generation Failure**: Displays error with support contact via **SES**.

### **UI Components**

| Element            | Description                 |
| ------------------ | --------------------------- |
| PDF Viewer         | Displays populated document |
| "Edit" Button      | Links to input fields       |
| "Approve" Checkbox | Confirms document accuracy  |
| "Save" Button      | Finalizes document          |
| Error Message      | Shows generation issues     |

---

## **5\. Version Management**

The system tracks document versions, enabling Members to access and manage updates.

### **Key Requirements**

#### **Version History**

- Each generation creates a new version (e.g., v1.0, v1.1) in **S3**.
- Stores version details (number, timestamp, changes) in **DynamoDB**.

#### **Access**

- Members can view past versions but not edit them.

#### **Retention**

- Retains generated and executed (signed) versions indefinitely.

### **User Flow**

1. Member visits /Member/documents/history.
2. System lists versions with timestamps and summaries from **DynamoDB**.
3. Member selects a version to view or regenerate.

### **Edge Cases**

- **Concurrent Updates**: Preserves the latest version in conflicts.
- **Deletion**: Soft-deletes documents, retaining history for compliance.

### **Compliance Considerations**

- **Audit Trails**: Logs version creation and access in **CloudWatch**.
- **Retention Laws**: Adheres to state-specific rules.

### **UI Components**

| Element             | Description                       |
| ------------------- | --------------------------------- |
| Version List        | Table of versions with dates      |
| "View" Button       | Displays selected version         |
| "Regenerate" Button | Creates new version from old data |

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via **Amplify Hosting**.
- **Routes**:
  - /Member/documents/create: Initiates document creation.
  - /Member/documents/preview: Shows document preview.
  - /Member/documents/history: Displays version history.

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - generateDocument: Generates document and returns preview URL.
  - getDocumentHistory: Retrieves version history.
  - confirmDocument: Finalizes document.
- **Template Database (DynamoDB)**:
  - templates: Columns: id, type, state, version, content.
- **Document Storage (S3)**:
  - Stores finalized documents with versioning enabled.
- **Encryption**: TLS/SSL for all traffic.

### **Logging**

- Tracks template selections, generations, and confirmations in **CloudWatch**.

---

## **7\. Testing and Validation**

- **Unit Tests**: Validate template selection and data mapping.
- **Integration Tests**: Confirm end-to-end generation.
- **Compliance Testing**: Ensure state-specific legal adherence.
- **UAT**: Verify usability for 65+ users.

### **Test Cases**

| Scenario           | Expected Outcome                       |
| ------------------ | -------------------------------------- |
| Template Selection | Correct state-specific template loaded |
| Data Mapping       | Inputs accurately populate fields      |
| Missing Data       | Prompts Member to complete fields      |
| Version Access     | Prior versions viewable                |
| Compliance         | Documents meet state legal standards   |

---

## **8\. Compliance and Security**

- **HIPAA**: Encrypts data with **AWS KMS** and TLS.
- **SOC2**: Implements access controls and logging via **Cognito** and **CloudWatch**.
- **State Laws**: Ensures template compliance with local regulations.
- **Data Integrity**: Uses cryptographic signatures for verification.

---

## **Summary**

The Document Generation process enables Members to create tailored, legally compliant estate planning documents effortlessly. By automating template selection, data population, and version management—while ensuring multi-jurisdiction compliance through a robust library and Jurisdiction Management system—it delivers a secure, reliable, and user-friendly experience. This specification provides developers with a clear blueprint to build a system balancing legal precision and accessibility, strengthening trust in the Childfree Legacy platform.
