'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  UserPlus,
  Pencil,
  Trash,
  Mail,
  Phone,
  Star,
  AlertCircle,
} from 'lucide-react';
import { useEmergencyContacts } from '@/hooks/useEmergencyContacts';
import useModal from '@/hooks/useModal';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';

interface EmergencyContact {
  id: string;
  fullName: string;
  relationship: string;
  phoneNumber: string;
  emailAddress: string;
  contactType: 'Medical' | 'Other';
  isPrimaryForType: boolean;
  isVerified: boolean;
}

export default function EmergencyContactsPage() {
  const { contacts, loading, error, addContact, updateContact, deleteContact } =
    useEmergencyContacts();
  const contactModal = useModal();
  const [selectedContact, setSelectedContact] = useState<
    EmergencyContact | undefined
  >(undefined);
  const deleteConfirmModal = useModal();
  const [contactToDelete, setContactToDelete] =
    useState<EmergencyContact | null>(null);

  const handleAddContact = () => {
    setSelectedContact(undefined);
    contactModal.showModal();
  };

  const handleEditContact = (contact: EmergencyContact) => {
    setSelectedContact(contact);
    contactModal.showModal();
  };

  const handleDeleteClick = (contact: EmergencyContact) => {
    setContactToDelete(contact);
    deleteConfirmModal.showModal();
  };

  const handleConfirmDelete = async () => {
    if (!contactToDelete) return;

    try {
      await deleteContact(contactToDelete.id);
      deleteConfirmModal.hideModal();
      setContactToDelete(null);
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  const handleSaveContact = async (
    contactData: Omit<EmergencyContact, 'id'>
  ): Promise<void> => {
    try {
      if (selectedContact) {
        await updateContact(selectedContact.id, contactData);
      } else {
        await addContact(contactData);
      }
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className='container mx-auto py-8 px-4 bg-gray-100 min-h-screen'>
      <h1 className='text-3xl font-bold mb-2'>Emergency Contacts</h1>
      <p className='text-[var(--custom-gray-medium)] mb-8'>
        Designate trusted individuals to receive specific information in
        emergencies.
      </p>

      {/* Information Card */}
      <Card className='bg-card text-card-foreground mb-8'>
        <CardContent className='p-6 space-y-6'>
          <div>
            <h2 className='text-xl font-bold mb-2'>About Emergency Contacts</h2>
            <p className='text-muted-foreground'>
              Emergency contacts can receive specific information if you're
              unable to communicate.
            </p>
          </div>

          <div>
            <h3 className='text-lg font-semibold mb-1'>Medical Contacts</h3>
            <p className='text-muted-foreground'>
              Receive medical details such as allergies and medical power of
              attorney.
            </p>
          </div>

          <div>
            <h3 className='text-lg font-semibold mb-1'>Other Contacts</h3>
            <p className='text-muted-foreground'>
              Receive non-medical details such as pet information and access
              instructions.
            </p>
          </div>

          <div className='pt-2'>
            <p className='text-sm'>
              <strong>Note:</strong> All emergency information requests are
              routed through a call center for verification.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Add Contact Button - Top */}
      <div className='flex justify-end mb-6'>
        <Button
          className='border-[var(--yellow-green)] bg-[var(--yellow-green)] text-white hover:bg-[var(--yellow-green)] hover:text-white cursor-pointer'
          variant='outline'
          onClick={handleAddContact}
        >
          <UserPlus className='mr-2 h-4 w-4' />
          Add Emergency Contact
        </Button>
      </div>

      {/* Contacts List */}
      {loading ? (
        <p>Loading contacts...</p>
      ) : error ? (
        <div className='text-red-500'>
          <p>Error loading contacts: {error}</p>
          <p className='text-sm mt-1'>
            Please try again later or contact support.
          </p>
        </div>
      ) : contacts.length === 0 ? (
        <p>You have no emergency contacts added.</p>
      ) : (
        <div className='space-y-6'>
          <h2 className='text-xl font-semibold'>Your Contacts</h2>

          {contacts.map(contact => (
            <Card key={contact.id} className='bg-card text-card-foreground'>
              <CardContent className='p-6'>
                <div className='flex justify-between items-start'>
                  <div className='space-y-4'>
                    <div className='flex items-center'>
                      <h3 className='text-lg font-semibold'>
                        {contact.fullName}
                      </h3>
                      {contact.isPrimaryForType && (
                        <Star
                          className='ml-2 h-4 w-4 text-yellow-500'
                          fill='currentColor'
                        />
                      )}
                    </div>

                    <p>{contact.relationship}</p>

                    <div className='space-y-1'>
                      <div className='flex items-center'>
                        <Mail className='h-4 w-4 mr-2' />
                        <span>{contact.emailAddress}</span>
                      </div>
                      <div className='flex items-center'>
                        <Phone className='h-4 w-4 mr-2' />
                        <span>{contact.phoneNumber}</span>
                      </div>
                    </div>
                  </div>

                  <div className='flex space-x-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      className='border-[var(--yellow-green)] text-[var(--yellow-green)] hover:bg-[var(--yellow-green)] hover:text-white'
                      onClick={() =>
                        handleEditContact({
                          ...contact,
                          isPrimaryForType: contact.isPrimaryForType ?? false,
                          isVerified: false,
                        })
                      }
                    >
                      <Pencil className='h-4 w-4' />
                    </Button>

                    <Button
                      variant='outline'
                      size='sm'
                      className=' cursor-pointer border-red-500 text-red-500 hover:bg-red-500 hover:text-white'
                      onClick={() =>
                        handleDeleteClick({
                          ...contact,
                          isVerified: false,
                          isPrimaryForType: contact.isPrimaryForType ?? false,
                        })
                      }
                    >
                      <Trash className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteConfirmModal.isVisible}
        onOpenChange={deleteConfirmModal.setIsVisible}
        title='Delete Emergency Contact'
        description='Are you sure you want to delete this emergency contact? This action cannot be undone.'
        confirmLabel='Delete Contact'
        confirmVariant='destructive'
        onConfirm={handleConfirmDelete}
        className='sm:max-w-md bg-black text-white border-gray-800'
        icon={<AlertCircle className='h-6 w-6 text-red-500' />}
        cancelButtonClassName='bg-background text-black hover:bg-gray-100'
      >
        <p className='text-sm'>
          This will permanently remove {contactToDelete?.fullName} from your
          emergency contacts.
        </p>
      </ConfirmationDialog>
    </div>
  );
}
