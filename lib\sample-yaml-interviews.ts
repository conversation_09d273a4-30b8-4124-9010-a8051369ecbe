export const BASIC_ESTATE_PLANNING_YAML = String.raw`---
# Interview: Basic Estate Planning Interview
# Description: Comprehensive interview for basic estate planning documents including will, trust, and healthcare directives.
# Generated by Interview Builder

metadata:
  title: Basic Estate Planning Interview
  short title: Basic Estate Planning Interview
  description: Comprehensive interview for basic estate planning documents including will, trust, and healthcare directives.
  version: 1
  
---
# Interview Questions

question: |
  What is your full legal name?
subquestion: |
  Enter your name exactly as it appears on legal documents
field: client_full_name
required: True
validation code: |
  if len(str(client_full_name)) < 2:
    validation_error("Name must be at least 2 characters")
---

question: |
  What is your date of birth?
subquestion: |
  This information is required for legal document preparation
field: client_date_of_birth
datatype: date
required: True
---

question: |
  Do you own a home?
subquestion: |
  This helps us determine how to handle real estate in your estate plan
field: home_ownership_status
choices:
  - Yes, I own my primary residence: yes_primary
  - Yes, I own multiple properties: yes_multiple
  - No, I rent or live with family: no
required: True
---

# Final screen
mandatory: True
question: |
  Thank you for completing the interview!
subquestion: |
  Your information has been collected and will be used to generate your documents.
buttons:
  - Exit: exit`;

export const COMPLEX_CONDITIONAL_YAML = String.raw`---
# Interview: Complex Estate Planning with Conditional Logic
# Description: Advanced estate planning interview with branching logic based on user responses
# Generated by Interview Builder

metadata:
  title: Complex Estate Planning Interview
  short title: Complex Estate Planning
  description: Advanced estate planning interview with branching logic based on user responses
  version: 2
  
---
# Personal Information

question: |
  What is your full legal name?
subquestion: |
  Enter your name exactly as it appears on legal documents
field: client_full_name
required: True
---

question: |
  What is your marital status?
field: marital_status
choices:
  - Single: single
  - Married: married
  - Divorced: divorced
  - Widowed: widowed
required: True
---

question: |
  What is your spouse's full legal name?
subquestion: |
  This is required for joint estate planning documents
field: spouse_name
show if: marital_status == "married"
required: True
---

question: |
  Do you have children?
field: has_children
datatype: yesno
required: True
---

question: |
  How many children do you have?
field: number_of_children
datatype: number
show if: has_children
required: True
validation code: |
  if number_of_children < 1:
    validation_error("Please enter a valid number of children")
---

question: |
  Please provide details for each child
subquestion: |
  We need this information for inheritance planning
field: children_details
datatype: object_list
object type: DAObject
show if: has_children
required: True
list collect: True
---

question: |
  Child's full name
field: children_details[i].name
---

question: |
  Child's date of birth
field: children_details[i].birth_date
datatype: date
---

question: |
  Is this child a minor (under 18)?
field: children_details[i].is_minor
datatype: yesno
---

question: |
  Who should serve as guardian for {{ children_details[i].name }} if both parents are unable?
field: children_details[i].guardian_name
show if: children_details[i].is_minor
required: True
---

# Asset Information

question: |
  What is the approximate total value of your assets?
subquestion: |
  Include real estate, investments, bank accounts, and personal property
field: total_asset_value
datatype: currency
required: True
---

question: |
  Do you own real estate?
field: owns_real_estate
datatype: yesno
required: True
---

question: |
  Please describe your real estate holdings
field: real_estate_details
datatype: area
show if: owns_real_estate
required: True
---

question: |
  Do you own a business?
field: owns_business
datatype: yesno
required: True
---

question: |
  What type of business do you own?
field: business_type
choices:
  - Sole Proprietorship: sole_prop
  - Partnership: partnership
  - Corporation: corporation
  - LLC: llc
  - Other: other
show if: owns_business
required: True
---

question: |
  What is the approximate value of your business?
field: business_value
datatype: currency
show if: owns_business
required: True
---

# Estate Planning Preferences

question: |
  Based on your asset value of {{ currency(total_asset_value) }}, you may benefit from advanced estate planning strategies.
subquestion: |
  Would you like to explore trust options?
field: wants_trust
datatype: yesno
show if: total_asset_value > 500000
required: True
---

question: |
  What type of trust are you interested in?
field: trust_type
choices:
  - Revocable Living Trust: revocable
  - Irrevocable Trust: irrevocable
  - Special Needs Trust: special_needs
  - Charitable Trust: charitable
  - I'm not sure: unsure
show if: wants_trust
required: True
---

question: |
  Who would you like to serve as your successor trustee?
subquestion: |
  This person will manage the trust if you become unable to do so
field: successor_trustee
show if: wants_trust
required: True
---

# Healthcare Directives

question: |
  Who would you like to serve as your healthcare agent?
subquestion: |
  This person will make medical decisions for you if you're unable to do so
field: healthcare_agent
required: True
---

question: |
  Do you want your healthcare agent to have the authority to make end-of-life decisions?
field: end_of_life_authority
datatype: yesno
required: True
---

question: |
  Please specify your preferences for end-of-life care
field: end_of_life_preferences
datatype: area
show if: end_of_life_authority
required: True
---

# Final Review

question: |
  Review Your Information
subquestion: |
  Please review the information you've provided:
  
  **Personal Information:**
  - Name: {{ client_full_name }}
  - Marital Status: {{ marital_status }}
  % if marital_status == "married":
  - Spouse: {{ spouse_name }}
  % endif
  % if has_children:
  - Children: {{ number_of_children }}
  % endif

  **Assets:**
  - Total Value: {{ currency(total_asset_value) }}
  % if owns_real_estate:
  - Owns Real Estate: Yes
  % endif
  % if owns_business:
  - Business Type: {{ business_type }}
  - Business Value: {{ currency(business_value) }}
  % endif

  **Estate Planning:**
  % if wants_trust:
  - Trust Type: {{ trust_type }}
  - Successor Trustee: {{ successor_trustee }}
  % endif
  - Healthcare Agent: {{ healthcare_agent }}
  
  Is this information correct?
field: information_confirmed
datatype: yesno
required: True
---

question: |
  Please go back and correct any errors in your information.
subquestion: |
  Click "Back" to review and update your responses.
field: go_back_to_correct
show if: not information_confirmed
buttons:
  - Back: restart
---

# Document Generation

mandatory: True
question: |
  Your Estate Planning Documents Are Ready!
subquestion: |
  Based on your responses, we will prepare the following documents:
  
  **Standard Documents:**
  - Last Will and Testament
  - Healthcare Power of Attorney
  - HIPAA Authorization
  
  % if wants_trust:
  **Trust Documents:**
  - {{ trust_type.title() }} Trust Agreement
  - Pour-over Will
  % endif
  
  % if has_children and any(child.is_minor for child in children_details):
  **Guardian Nominations:**
  - Nomination of Guardian for Minor Children
  % endif
  
  % if owns_business:
  **Business Succession:**
  - Business Succession Planning Documents
  % endif
  
  **Next Steps:**
  1. Review draft documents
  2. Schedule consultation with attorney
  3. Execute documents with proper witnesses
  
  Thank you for completing the estate planning interview!
buttons:
  - Generate Documents: exit
  - Start Over: restart`;

export const BUSINESS_SUCCESSION_YAML = String.raw`---
# Interview: Business Succession Planning
# Description: Comprehensive interview for business succession planning with complex branching logic
# Generated by Interview Builder

metadata:
  title: Business Succession Planning Interview
  short title: Business Succession
  description: Comprehensive interview for business succession planning with complex branching logic
  version: 1
  
---
# Business Information

question: |
  What is the name of your business?
field: business_name
required: True
---

question: |
  What type of business entity is it?
field: entity_type
choices:
  - Sole Proprietorship: sole_prop
  - Partnership: partnership
  - Limited Partnership: limited_partnership
  - Corporation (C-Corp): c_corp
  - S-Corporation: s_corp
  - Limited Liability Company (LLC): llc
  - Professional Corporation: prof_corp
required: True
---

question: |
  How many owners/partners does the business have?
field: number_of_owners
datatype: number
required: True
validation code: |
  if number_of_owners < 1:
    validation_error("Must have at least one owner")
---

question: |
  What percentage of the business do you own?
field: ownership_percentage
datatype: number
required: True
validation code: |
  if ownership_percentage <= 0 or ownership_percentage > 100:
    validation_error("Ownership percentage must be between 1 and 100")
---

question: |
  Please provide information about each business owner
field: business_owners
datatype: object_list
object type: DAObject
show if: number_of_owners > 1
list collect: True
---

question: |
  Owner's full name
field: business_owners[i].name
---

question: |
  Owner's ownership percentage
field: business_owners[i].percentage
datatype: number
---

question: |
  Owner's role in the business
field: business_owners[i].role
choices:
  - Active Owner/Manager: active
  - Silent Partner: silent
  - Investor Only: investor
---

# Succession Planning Goals

question: |
  What is your primary succession planning goal?
field: succession_goal
choices:
  - Transfer to family members: family_transfer
  - Sell to employees (ESOP): employee_sale
  - Sell to co-owners: co_owner_sale
  - Sell to third party: third_party_sale
  - Liquidate the business: liquidation
  - Not sure yet: unsure
required: True
---

question: |
  Which family members are you considering for succession?
field: family_successors
datatype: object_list
object type: DAObject
show if: succession_goal == "family_transfer"
list collect: True
---

question: |
  Family member's name
field: family_successors[i].name
---

question: |
  Relationship to you
field: family_successors[i].relationship
choices:
  - Child: child
  - Spouse: spouse
  - Sibling: sibling
  - Other relative: other
---

question: |
  Are they currently involved in the business?
field: family_successors[i].currently_involved
datatype: yesno
---

question: |
  What role would they take in succession?
field: family_successors[i].succession_role
choices:
  - Full ownership and management: full_control
  - Ownership with hired management: ownership_only
  - Management with gradual ownership: gradual_ownership
  - Advisory role only: advisory
---

# Buy-Sell Agreements

question: |
  Do you currently have a buy-sell agreement with your co-owners?
field: has_buy_sell_agreement
datatype: yesno
show if: number_of_owners > 1
required: True
---

question: |
  What valuation method is used in your current buy-sell agreement?
field: current_valuation_method
choices:
  - Fixed price (updated annually): fixed_price
  - Formula based on financials: formula
  - Professional appraisal: appraisal
  - Multiple appraisers: multiple_appraisers
  - No specific method: none
show if: has_buy_sell_agreement
required: True
---

question: |
  What triggers are included in your buy-sell agreement?
field: buy_sell_triggers
datatype: checkboxes
choices:
  - Death: death
  - Disability: disability
  - Retirement: retirement
  - Involuntary termination: termination
  - Voluntary departure: voluntary
  - Divorce: divorce
  - Bankruptcy: bankruptcy
show if: has_buy_sell_agreement
required: True
---

# Valuation and Financing

question: |
  What is the approximate current value of the entire business?
field: business_value
datatype: currency
required: True
---

question: |
  How should the business be valued for succession purposes?
field: valuation_preference
choices:
  - Professional business appraisal: professional_appraisal
  - Multiple of earnings (EBITDA): earnings_multiple
  - Book value: book_value
  - Fair market value: fair_market
  - Discounted for minority interest: discounted
required: True
---

question: |
  How would you prefer the succession to be financed?
field: financing_preference
choices:
  - Installment payments over time: installments
  - Life insurance proceeds: life_insurance
  - Business loan: business_loan
  - Seller financing: seller_financing
  - Combination of methods: combination
required: True
---

# Tax Planning

question: |
  Are you interested in minimizing estate taxes through succession planning?
field: minimize_estate_taxes
datatype: yesno
required: True
---

question: |
  Which tax-advantaged strategies interest you?
field: tax_strategies
datatype: checkboxes
choices:
  - Grantor Retained Annuity Trust (GRAT): grat
  - Charitable Remainder Trust: crt
  - Family Limited Partnership: flp
  - Installment sale: installment_sale
  - Gifting strategies: gifting
  - Employee Stock Ownership Plan (ESOP): esop
show if: minimize_estate_taxes
---

# Final Planning

mandatory: True
question: |
  Your Business Succession Plan Summary
subquestion: |
  Based on your responses, here's your succession planning profile:
  
  **Business Details:**
  - Business: {{ business_name }}
  - Entity Type: {{ entity_type }}
  - Your Ownership: {{ ownership_percentage }}%
  - Business Value: {{ currency(business_value) }}

  **Succession Strategy:**
  - Primary Goal: {{ succession_goal }}
  % if succession_goal == "family_transfer" and defined('family_successors'):
  - Family Successors: {{ comma_and_list([successor.name for successor in family_successors]) }}
  % endif

  **Current Agreements:**
  % if number_of_owners > 1:
  - Buy-Sell Agreement: {{ "Yes" if has_buy_sell_agreement else "No" }}
  % endif
  
  **Recommended Next Steps:**
  1. Business valuation analysis
  2. Tax planning consultation
  3. Legal document preparation
  4. Implementation timeline
  
  **Documents to Prepare:**
  % if not has_buy_sell_agreement and number_of_owners > 1:
  - Buy-Sell Agreement
  % endif
  % if succession_goal == "family_transfer":
  - Family succession plan
  - Gift and estate tax analysis
  % endif
  % if minimize_estate_taxes:
  - Tax minimization strategies
  % endif
  - Succession timeline and implementation plan
  
buttons:
  - Generate Succession Plan: exit
  - Modify Responses: restart`;

// Complex YAML with advanced conditional logic and branching
export const SIMPLE_BASIC_YAML = `---
metadata:
  title: Advanced Estate Planning Interview
  description: Comprehensive estate planning with complex conditional logic and branching
  version: 2.0
  author: Estate Planning System
  tags: [estate-planning, conditional-logic, advanced]

---
# Initial Client Information
question: |
  What is your full legal name?
subquestion: |
  Please enter your name exactly as it appears on legal documents
field: client_full_name
required: True
validation code: |
  if len(str(client_full_name).strip()) < 2:
    validation_error("Name must be at least 2 characters")
  if not re.search(r'^[a-zA-Z\s\-\.\']+$', str(client_full_name)):
    validation_error("Name can only contain letters, spaces, hyphens, periods, and apostrophes")
---

question: |
  What is your date of birth?
subquestion: |
  This helps us determine age-related estate planning strategies
field: client_date_of_birth
datatype: date
required: True
validation code: |
  from datetime import date, timedelta
  today = date.today()
  min_age = today - timedelta(days=18*365.25)
  max_age = today - timedelta(days=120*365.25)
  if client_date_of_birth > min_age:
    validation_error("You must be at least 18 years old")
  if client_date_of_birth < max_age:
    validation_error("Please enter a valid birth date")
---

code: |
  from datetime import date
  client_age = (date.today() - client_date_of_birth).days // 365
---

question: |
  What is your current marital status?
field: marital_status
choices:
  - Single (never married): single
  - Married: married
  - Divorced: divorced
  - Widowed: widowed
  - Domestic Partnership: domestic_partner
  - Separated: separated
required: True
---

# Spouse Information Branch
question: |
  What is your spouse's full legal name?
field: spouse_name
show if: marital_status == "married"
required: True
---

question: |
  What is your spouse's date of birth?
field: spouse_date_of_birth
datatype: date
show if: marital_status == "married"
required: True
---

question: |
  How long have you been married?
field: marriage_duration
datatype: number
show if: marital_status == "married"
required: True
validation code: |
  if marriage_duration < 0:
    validation_error("Marriage duration cannot be negative")
  if marriage_duration > 80:
    validation_error("Please enter a reasonable marriage duration")
---

question: |
  Is this a first marriage for both of you?
field: first_marriage_both
datatype: yesno
show if: marital_status == "married"
required: True
---

# Previous Marriage Information
question: |
  Do you have any obligations from previous marriages?
subquestion: |
  This includes alimony, child support, or other financial commitments
field: previous_marriage_obligations
datatype: yesno
show if: marital_status in ["married", "divorced", "widowed"] and not first_marriage_both
required: True
---

question: |
  Please describe your previous marriage obligations
field: previous_obligations_details
datatype: area
show if: previous_marriage_obligations
required: True
---

# Children Information with Complex Branching
question: |
  Do you have any children?
subquestion: |
  Include biological, adopted, and stepchildren
field: has_children
datatype: yesno
required: True
---

question: |
  How many children do you have in total?
field: total_children
datatype: number
show if: has_children
required: True
validation code: |
  if total_children < 1:
    validation_error("Please enter a valid number of children")
  if total_children > 20:
    validation_error("Please enter a reasonable number of children")
---

question: |
  Please provide detailed information for each child
field: children_details
datatype: object_list
object type: DAObject
show if: has_children
list collect: True
ask number: True
---

question: |
  Child's full legal name
field: children_details[i].name
required: True
---

question: |
  Child's date of birth
field: children_details[i].birth_date
datatype: date
required: True
---

question: |
  What is your relationship to {{ children_details[i].name }}?
field: children_details[i].relationship_type
choices:
  - Biological child: biological
  - Adopted child: adopted
  - Stepchild: stepchild
  - Foster child: foster
required: True
---

code: |
  from datetime import date
  children_details[i].age = (date.today() - children_details[i].birth_date).days // 365
  children_details[i].is_minor = children_details[i].age < 18
  children_details[i].is_young_adult = 18 <= children_details[i].age < 25
---

question: |
  Does {{ children_details[i].name }} have any special needs or disabilities?
field: children_details[i].has_special_needs
datatype: yesno
required: True
---

question: |
  Please describe {{ children_details[i].name }}'s special needs
subquestion: |
  This helps us plan for ongoing care and financial support
field: children_details[i].special_needs_description
datatype: area
show if: children_details[i].has_special_needs
required: True
---

# Guardian Nomination for Minors
question: |
  Who should serve as guardian for {{ children_details[i].name }} if you are unable to care for them?
subquestion: |
  This person would have legal custody and make decisions for your child
field: children_details[i].primary_guardian
show if: children_details[i].is_minor
required: True
---

question: |
  Who should serve as alternate guardian for {{ children_details[i].name }}?
subquestion: |
  This person would serve if the primary guardian cannot
field: children_details[i].alternate_guardian
show if: children_details[i].is_minor
required: True
---

question: |
  Should {{ children_details[i].name }}'s inheritance be held in trust until they reach a certain age?
field: children_details[i].needs_trust
datatype: yesno
show if: children_details[i].age < 30
required: True
---

question: |
  At what age should {{ children_details[i].name }} receive their full inheritance?
field: children_details[i].distribution_age
datatype: number
choices:
  - Age 18: 18
  - Age 21: 21
  - Age 25: 25
  - Age 30: 30
  - Age 35: 35
  - Custom age: custom
show if: children_details[i].needs_trust
required: True
---

question: |
  What custom age should {{ children_details[i].name }} receive their inheritance?
field: children_details[i].custom_distribution_age
datatype: number
show if: children_details[i].needs_trust and children_details[i].distribution_age == "custom"
required: True
validation code: |
  if children_details[i].custom_distribution_age < 18:
    validation_error("Distribution age must be at least 18")
  if children_details[i].custom_distribution_age > 65:
    validation_error("Distribution age seems unreasonably high")
---

# Asset Information with Complex Calculations
question: |
  What is the approximate total value of all your assets?
subquestion: |
  Include real estate, investments, bank accounts, retirement accounts, and personal property
field: total_asset_value
datatype: currency
required: True
validation code: |
  if total_asset_value < 0:
    validation_error("Asset value cannot be negative")
---

code: |
  # Calculate estate tax implications
  federal_exemption_2024 = ********  # Current federal exemption
  needs_estate_tax_planning = total_asset_value > federal_exemption_2024 * 0.8
  high_net_worth = total_asset_value > 5000000
  moderate_estate = 1000000 <= total_asset_value <= 5000000
  simple_estate = total_asset_value < 1000000
---

# Real Estate Holdings
question: |
  Do you own any real estate?
field: owns_real_estate
datatype: yesno
required: True
---

question: |
  How many properties do you own?
field: number_of_properties
datatype: number
show if: owns_real_estate
required: True
validation code: |
  if number_of_properties < 1:
    validation_error("Please enter a valid number of properties")
---

question: |
  Please provide details for each property
field: property_details
datatype: object_list
object type: DAObject
show if: owns_real_estate
list collect: True
ask number: True
---

question: |
  What type of property is this?
field: property_details[i].property_type
choices:
  - Primary residence: primary_home
  - Vacation home: vacation_home
  - Rental property: rental
  - Commercial property: commercial
  - Raw land: land
  - Other: other
required: True
---

question: |
  What is the approximate value of this property?
field: property_details[i].property_value
datatype: currency
required: True
---

question: |
  Do you have a mortgage on this property?
field: property_details[i].has_mortgage
datatype: yesno
required: True
---

question: |
  What is the remaining mortgage balance?
field: property_details[i].mortgage_balance
datatype: currency
show if: property_details[i].has_mortgage
required: True
---

# Business Ownership
question: |
  Do you own any business interests?
field: owns_business
datatype: yesno
required: True
---

question: |
  What type of business entity do you own?
field: business_entity_type
choices:
  - Sole Proprietorship: sole_prop
  - Partnership: partnership
  - Limited Partnership: limited_partnership
  - Corporation (C-Corp): c_corp
  - S-Corporation: s_corp
  - Limited Liability Company (LLC): llc
  - Professional Corporation: prof_corp
  - Multiple businesses: multiple
show if: owns_business
required: True
---

question: |
  What percentage of the business do you own?
field: business_ownership_percentage
datatype: number
show if: owns_business
required: True
validation code: |
  if business_ownership_percentage <= 0 or business_ownership_percentage > 100:
    validation_error("Ownership percentage must be between 1 and 100")
---

question: |
  What is the approximate value of your business interest?
field: business_value
datatype: currency
show if: owns_business
required: True
---

question: |
  Do you have a succession plan for your business?
field: has_business_succession_plan
datatype: yesno
show if: owns_business
required: True
---

# Retirement Accounts
question: |
  Do you have retirement accounts (401k, IRA, etc.)?
field: has_retirement_accounts
datatype: yesno
required: True
---

question: |
  What is the total value of all your retirement accounts?
field: retirement_account_value
datatype: currency
show if: has_retirement_accounts
required: True
---

question: |
  Have you named beneficiaries for all your retirement accounts?
field: retirement_beneficiaries_named
datatype: yesno
show if: has_retirement_accounts
required: True
---

# Life Insurance
question: |
  Do you have life insurance?
field: has_life_insurance
datatype: yesno
required: True
---

question: |
  What is the total death benefit of all your life insurance policies?
field: life_insurance_benefit
datatype: currency
show if: has_life_insurance
required: True
---

question: |
  What type of life insurance do you have?
field: life_insurance_type
choices:
  - Term life insurance: term
  - Whole life insurance: whole
  - Universal life insurance: universal
  - Variable life insurance: variable
  - Multiple policies: multiple
show if: has_life_insurance
required: True
---

# Healthcare Directives
question: |
  Who would you like to serve as your healthcare agent/proxy?
subquestion: |
  This person will make medical decisions for you if you're unable to do so
field: healthcare_agent_name
required: True
---

question: |
  What is your healthcare agent's relationship to you?
field: healthcare_agent_relationship
choices:
  - Spouse: spouse
  - Adult child: child
  - Parent: parent
  - Sibling: sibling
  - Other family member: family
  - Friend: friend
  - Other: other
required: True
---

question: |
  Who should serve as your alternate healthcare agent?
field: alternate_healthcare_agent
required: True
---

question: |
  Do you want your healthcare agent to have authority to make end-of-life decisions?
field: end_of_life_authority
datatype: yesno
required: True
---

question: |
  Please specify your preferences for end-of-life care
subquestion: |
  Include any specific wishes about life support, resuscitation, etc.
field: end_of_life_preferences
datatype: area
show if: end_of_life_authority
required: True
---

# Financial Power of Attorney
question: |
  Who would you like to serve as your financial power of attorney?
subquestion: |
  This person will manage your finances if you become unable to do so
field: financial_poa_name
required: True
---

question: |
  Should your financial power of attorney be effective immediately or only upon incapacity?
field: financial_poa_effective
choices:
  - Immediately (durable): immediate
  - Only upon incapacity (springing): springing
required: True
---

# Estate Planning Strategy Based on Asset Value
question: |
  Based on your total assets of {{ currency(total_asset_value) }}, you may benefit from advanced estate planning strategies.
subquestion: |
  Would you like to explore trust options?
field: wants_trust_planning
datatype: yesno
show if: total_asset_value > 500000
required: True
---

question: |
  What are your primary estate planning goals?
field: estate_planning_goals
datatype: checkboxes
choices:
  - Minimize estate taxes: minimize_taxes
  - Provide for spouse: provide_spouse
  - Provide for children: provide_children
  - Charitable giving: charitable
  - Business succession: business_succession
  - Asset protection: asset_protection
  - Privacy: privacy
  - Avoid probate: avoid_probate
required: True
---

# Trust Planning Branch
question: |
  What type of trust structure interests you most?
field: preferred_trust_type
choices:
  - Revocable Living Trust: revocable_living
  - Irrevocable Life Insurance Trust: ilit
  - Charitable Remainder Trust: crt
  - Grantor Retained Annuity Trust: grat
  - Family Limited Partnership: flp
  - Special Needs Trust: special_needs
  - I'm not sure: unsure
show if: wants_trust_planning
required: True
---

question: |
  Who would you like to serve as successor trustee?
field: successor_trustee_name
show if: wants_trust_planning
required: True
---

# Charitable Planning
question: |
  Are you interested in charitable giving as part of your estate plan?
field: interested_in_charity
datatype: yesno
required: True
---

question: |
  What percentage of your estate would you like to leave to charity?
field: charitable_percentage
datatype: number
choices:
  - Less than 5%: 5
  - 5-10%: 10
  - 10-25%: 25
  - 25-50%: 50
  - More than 50%: 75
show if: interested_in_charity
required: True
---

question: |
  Which charitable organizations are you interested in supporting?
field: preferred_charities
datatype: area
show if: interested_in_charity
required: True
---

# Final Review and Recommendations
mandatory: True
question: |
  Estate Planning Summary and Recommendations
subquestion: |
  Based on your responses, here's your personalized estate planning profile:

  **Personal Information:**
  - Name: {{ client_full_name }}
  - Age: {{ client_age }}
  - Marital Status: {{ marital_status }}
  % if marital_status == "married":
  - Spouse: {{ spouse_name }}
  % endif

  **Family Structure:**
  % if has_children:
  - Children: {{ total_children }}
  % for child in children_details:
  - {{ child.name }} (Age {{ child.age }}, {{ child.relationship_type }})
  % endfor
  % else:
  - No children
  % endif

  **Asset Summary:**
  - Total Assets: {{ currency(total_asset_value) }}
  % if owns_real_estate:
  - Real Estate: {{ number_of_properties }} properties
  % endif
  % if owns_business:
  - Business Interest: {{ business_ownership_percentage }}% ownership
  - Business Value: {{ currency(business_value) }}
  % endif
  % if has_retirement_accounts:
  - Retirement Accounts: {{ currency(retirement_account_value) }}
  % endif
  % if has_life_insurance:
  - Life Insurance: {{ currency(life_insurance_benefit) }} death benefit
  % endif

  **Estate Planning Classification:**
  % if simple_estate:
  - **Simple Estate** - Basic will and healthcare directives recommended
  % elif moderate_estate:
  - **Moderate Estate** - Will, trust planning, and tax strategies recommended
  % elif high_net_worth:
  - **High Net Worth** - Comprehensive estate and tax planning required
  % endif

  % if needs_estate_tax_planning:
  - **Estate Tax Planning Required** - Your estate may be subject to federal estate taxes
  % endif

  **Recommended Documents:**
  - Last Will and Testament
  - Healthcare Power of Attorney
  - Financial Power of Attorney
  - HIPAA Authorization

  % if has_children and any(child.is_minor for child in children_details):
  - Nomination of Guardian for Minor Children
  % endif

  % if wants_trust_planning or total_asset_value > 1000000:
  - {{ preferred_trust_type if wants_trust_planning else "Revocable Living Trust" }}
  % endif

  % if owns_business and not has_business_succession_plan:
  - Business Succession Planning Documents
  % endif

  % if interested_in_charity:
  - Charitable Planning Documents
  % endif

  **Priority Action Items:**
  1. Schedule consultation with estate planning attorney
  % if not retirement_beneficiaries_named:
  2. Update beneficiaries on retirement accounts
  % endif
  % if owns_business and not has_business_succession_plan:
  3. Develop business succession plan
  % endif
  % if needs_estate_tax_planning:
  4. Implement estate tax reduction strategies
  % endif
  5. Review and update estate plan annually

  **Estimated Timeline:**
  - Initial consultation: 1-2 weeks
  - Document preparation: 2-4 weeks
  - Review and execution: 1-2 weeks
  - Total process: 4-8 weeks

  Would you like to proceed with document preparation?
field: proceed_with_planning
datatype: yesno
required: True
buttons:
  - Generate Estate Plan: exit
  - Schedule Consultation: exit
  - Modify Responses: restart`;

export const SIMPLE_CONDITIONAL_YAML = `---
metadata:
  title: Conditional Estate Planning Interview
  description: Estate planning with conditional logic
  version: 1

---
question: |
  What is your marital status?
field: marital_status
choices:
  - Single: single
  - Married: married
  - Divorced: divorced
required: True
---

question: |
  What is your spouse's name?
field: spouse_name
show if: marital_status == "married"
required: True
---

question: |
  Do you have children?
field: has_children
datatype: yesno
required: True
---

question: |
  How many children do you have?
field: number_of_children
datatype: number
show if: has_children
required: True
---

mandatory: True
question: |
  Interview Complete
subquestion: |
  Thank you for providing your information.
buttons:
  - Finish: exit`;

// Simple test YAML for debugging flow connections
export const SIMPLE_TEST_YAML = `---
metadata:
  title: Simple Test Interview
  description: Basic test with 4 questions
  version: 1.0

---
question: |
  What is your name?
field: user_name
datatype: text
required: true

---
question: |
  What is your age?
field: user_age
datatype: number
required: true

---
question: |
  Are you married?
field: is_married
datatype: yesno

---
question: |
  Thank you for completing the interview.
field: complete
datatype: text
default: "Done"
`;

export const SAMPLE_INTERVIEWS = {
  basic: SIMPLE_BASIC_YAML, // Restore complex YAML for proper testing
  complex: SIMPLE_CONDITIONAL_YAML,
  business: SIMPLE_BASIC_YAML,
};
