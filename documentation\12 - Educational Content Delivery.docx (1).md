# 12 \- Educational Content Delivery

# **Document 12 \- Functional Specification: Educational Content Delivery**

## **Overview**

The Educational Content Delivery feature integrates a variety of educational materials—such as videos, text articles, infographics, and interactive avatars—into the user journey of the Childfree Legacy Web Application. Leveraging AWS Amplify’s serverless architecture, this system delivers clear, accessible, and timely explanations to guide users, particularly those aged 65+, through complex estate planning concepts. By embedding content strategically within the user experience, it enhances understanding, reduces confusion, and builds confidence in managing estate plans. The feature prioritizes usability, accessibility, and compliance with data protection standards (e.g., HIPAA, SOC2), ensuring a secure and supportive environment. Future enhancements will allow administrators to incorporate white-labeled content for cross-promotion, expanding the platform’s value through strategic partnerships.

---

## **1\. Content Types and Formats**

The system supports diverse content types to accommodate various learning preferences and accessibility requirements.

### **Key Requirements**

#### **Content Types**

- **Videos**: Short (2–5 minutes), captioned, and audio-described videos explaining estate planning topics (e.g., "What is a Will?").
- **Text Articles**: Written guides, FAQs, and step-by-step instructions featuring clear headings and simple language.
- **Infographics**: Visual aids (e.g., flowcharts) to simplify complex concepts.
- **Interactive Avatars**: Virtual assistants offering conversational guidance and responses to common questions.
- **Tooltips and Popups**: Contextual explanations triggered by hover or click (e.g., defining "beneficiary").

#### **Compliance Considerations**

- **Accessibility**: Adheres to WCAG 2.1 Level AA standards, including captions and alt text.
- **Content Security**: Scans uploaded content for malware using **Amazon GuardDuty**.

---

## **2\. Delivery Mechanisms**

Educational content is delivered through multiple channels to ensure relevance and accessibility at optimal moments.

### **Key Requirements**

#### **Delivery Methods**

- **Contextual Integration**: Automatically displays content at relevant workflow points (e.g., a popup explaining "trustee" during trustee assignment).
- **On-Demand Access**: A searchable library at /education, categorized by topics (e.g., "Wills," "Trusts").
- **Search Functionality**: Keyword-based search powered by **Amazon CloudSearch** (e.g., "power of attorney").
- **Progressive Disclosure**: Introduces content gradually, offering links to advanced resources.

#### **User Flow**

1. User initiates a task (e.g., creating a will).
2. System presents contextual content (e.g., a video on "What is a Will?").
3. User opts to engage or skip; engaging displays the content, skipping advances the task.
4. Full library access is available at /education.

#### **Edge Cases**

- **Content Overload**: Caps contextual popups to avoid overwhelming users.

#### **Compliance Considerations**

- **User Consent**: Requires active user choice to engage with contextual content, ensuring informed interaction and supporting liability coverage.
- **Data Privacy**: Ensures no personal data is collected via content delivery.

---

## **3\. User Interaction**

Users can engage with content intuitively, fostering a seamless and effective learning experience.

### **Key Requirements**

#### **Interaction Features**

- **Videos**: Include play, pause, rewind, and speed controls; captions and audio descriptions are toggleable.
- **Text Articles**: Offer a table of contents, back-to-top buttons, and bookmarking.
- **Interactive Avatars**: Provide predefined responses to frequent queries.
- **Tooltips and Popups**: Dismissible with revisit options.
- **Feedback Mechanisms**: Allow users to rate content (thumbs up/down) or submit admin-only comments.

#### **User Flow**

1. User accesses content (e.g., plays a video).
2. Interacts using available controls (e.g., adjusts playback speed).
3. Post-interaction, rates content or explores related resources.

#### **Edge Cases**

- **Interrupted Sessions**: Saves progress (e.g., video timestamps) via **AWS AppSync**.
- **Low Bandwidth**: Provides lower-quality video or text options using **Amazon S3** and **CloudFront**.

#### **Compliance Considerations**

- **Accessibility**: Ensures keyboard navigation and screen reader support with ARIA labels.
- **User Feedback**: Stores feedback anonymously in **DynamoDB**.

---

## **4\. Accessibility Features**

Accessibility is tailored for users aged 65+, addressing diverse physical and cognitive needs.

### **Key Requirements**

#### **Accessibility Options**

- **Adjustable Text Size and Contrast**: Frontend settings allow text size increases and high-contrast mode.
- **Audio Descriptions**: Videos include narration for visually impaired users.
- **Keyboard Navigation**: All elements are keyboard-accessible.
- **Screen Reader Compatibility**: Uses semantic HTML and ARIA labels.
- **Simplified Language**: Avoids jargon, with definitions for technical terms.

#### **Compliance Considerations**

- **WCAG 2.1**: Meets Level AA standards.
- **ADA Compliance**: Ensures usability for users with disabilities.

---

## **5\. Integration with User Journey**

Content is embedded at strategic points to provide timely guidance without disrupting workflows.

### **Key Requirements**

#### **Integration Points**

- **Onboarding**: Introductory videos and articles during setup.
- **Document Creation**: Tooltips and popups clarify document sections.
- **Review and Signing**: Videos and articles emphasize review importance.
- **Emergency Features**: Interactive avatars assist with emergency contact setup.
- **Notifications**: Include links to relevant content.
- **Live Documents**: Offer maintenance-focused videos and articles.

#### **User Flow**

1. User starts a task (e.g., creating a will).
2. System displays a tooltip or popup with a content link.
3. User chooses to engage or proceed.

#### **Edge Cases**

- **Frequent Users**: Option to disable contextual tips after proficiency.
- **Content Relevance**: Adapts content to user progress (e.g., advanced tips for experienced users).

---

## **Future Phase: White-Labeled Content for Cross-Promotion**

A planned update will enable administrators to add customizable or branded content from partners for cross-promotion of related services.

### **Key Requirements**

#### **Content Management**

- Administrators upload or link content via the admin interface, defining type (e.g., promotional) and audience.

#### **Strategic Placement**

- Appears during onboarding, post-setup, or within emergency features and live documents.

#### **Delivery**

- Non-intrusive formats (e.g., modals, banners) with dismissible options.
- Users can opt out of promotional content.

#### **Analytics**

- Tracks engagement (views, clicks) using **Amazon CloudWatch**.

#### **Accessibility**

- Complies with WCAG 2.1 Level AA standards.

#### **Approval**

- Senior administrators approve content to align with platform goals.

#### **User Flow Example (Onboarding)**

1. User completes onboarding.
2. A modal presents partner content (e.g., "Explore estate planning tools").
3. User engages or dismisses it.

#### **Compliance Considerations**

- **Advertising Regulations**: Adheres to laws protecting the 65+ demographic.
- **User Consent**: Offers clear opt-out mechanisms.

#### **Technical Implementation**

- **CMS Module**: Manages content in **DynamoDB**.
- **APIs**: **AWS AppSync** delivers content at specified triggers.
- **Configuration**: Maps content to journey stages.

---

## **6\. Content Management for Administrators**

Administrators manage content through a secure, intuitive interface.

### **Key Requirements**

#### **Content Upload**

- Uploads occur at /admin/content, with type, title, and tags specified.

#### **Categorization**

- Tags link content to journey stages (e.g., "trusts").

#### **Versioning**

- Tracks edits via **AWS S3 versioning**.

#### **Analytics Dashboard**

- Displays metrics at /admin/content/analytics via **AWS AppSync**.

#### **Feedback**

- Views user feedback from **DynamoDB**.

#### **User Flow**

1. Administrator uploads content (e.g., "Understanding Trusts" video).
2. Tags and assigns it to trust setup.
3. Senior administrator approves via **AWS Cognito**.
4. Content integrates into the user journey.

#### **Compliance Considerations**

- **Content Security**: Scans uploads with **Amazon GuardDuty**.
- **Audit Trails**: Logs actions in **CloudWatch**.

---

## **7\. Analytics and Tracking**

Engagement tracking measures content effectiveness and highlights improvement opportunities.

### **Key Requirements**

#### **Metrics**

- **View Counts**: Monitored via **AWS AppSync**.
- **Completion Rates**: Tracks video watch time and article scroll depth.
- **Feedback Scores**: Collects ratings and comments.
- **Search Queries**: Logs terms to identify gaps.

#### **Compliance Considerations**

- **Data Privacy**: Anonymizes interaction data.

---

## **8\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted on **AWS Amplify Hosting**.
- **Routes**:
  - /education: User library.
  - /admin/content: Admin management.
  - /admin/content/analytics: Analytics dashboard.

### **Backend**

- **APIs (GraphQL via AWS AppSync)**:
  - getContent: { filters: { type, tags } } → { content: \[{ id, type, title, description, url }\] }
  - uploadContent: { type, title, description, file, tags } → { success, contentId }
  - getContentAnalytics: { contentId } → { views, completionRate, feedback }
- **Database (Amazon DynamoDB)**:
  - educational_content: id, type, title, description, url, tags, version, status, created_at, updated_at.
  - content_analytics: id, content_id, user_id, interaction_type, timestamp, details.
- **Storage**: **Amazon S3**, encrypted with **AWS KMS**.

#### **Third-Party Integrations**

- **Video Hosting**: Secure embeds from Vimeo or YouTube.
- **Analytics**: **Amazon CloudWatch**.

#### **Encryption**

- TLS/SSL for delivery; **AWS KMS** for storage.

---

## **9\. Testing and Validation**

- **Unit Tests**: Validate rendering with **AWS Amplify CLI**.
- **Integration Tests**: Confirm journey integration.
- **Accessibility Testing**: Lighthouse for WCAG compliance.
- **User Acceptance Testing (UAT)**: Usability testing with 65+ users.

### **Test Cases**

| Scenario               | Expected Outcome                       |
| ---------------------- | -------------------------------------- |
| Access Content Library | User browses and searches content      |
| Video Playback         | Video plays with captions and controls |
| Contextual Tooltip     | Tooltip appears on hover               |
| Interactive Avatar     | Avatar responds to queries             |
| Admin Uploads Content  | Content categorized and published      |
| Analytics Report       | Admin views engagement data            |

---

## **10\. Compliance and Security**

- **Data Privacy**: No personal data in content or analytics.
- **Content Security**: Malware scans via **Amazon GuardDuty**.
- **Accessibility**: WCAG 2.1 Level AA compliant.
- **Audit Trails**: Logged in **CloudWatch**.

---

## **Summary**

Powered by AWS Amplify, the Educational Content Delivery feature provides accessible, timely educational resources to guide users through estate planning. By integrating diverse content types into the user journey, it empowers users—especially those aged 65+—to navigate complex concepts confidently. Future white-labeled content capabilities will further enhance the platform through strategic cross-promotion, delivering a scalable, secure, and user-centric educational experience.
