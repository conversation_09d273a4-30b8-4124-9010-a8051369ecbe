/**
 * Content Detail Page
 *
 * Page for viewing a specific piece of educational content.
 */

'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEducationalContent } from '../../../../hooks/use-educational-content';
import { VideoPlayer } from '@/components/education/video-player';
import { ArticleViewer } from '@/components/education/article-viewer';
import { InfographicViewer } from '@/components/education/infographic-viewer';
import { AvatarAssistant } from '@/components/education/avatar-assistant';
import { TooltipContentComponent } from '@/components/education/tooltip-content';
import { ContentFeedback } from '@/components/education/content-feedback';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ContentDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ContentDetailPage({
  params,
}: ContentDetailPageProps) {
  const { id } = await params;
  const router = useRouter();
  const { contentById, trackContentView, isLoading } = useEducationalContent();

  // Get content by ID
  const content = contentById(id);

  // Track content view
  useEffect(() => {
    if (content) {
      trackContentView(content.id);
    }
  }, [content, trackContentView]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className='container mx-auto py-8 space-y-8'>
        <div className='flex items-center space-x-2 mb-4'>
          <Button variant='outline' size='sm' disabled>
            Back
          </Button>
          <div className='h-8 w-40 bg-gray-200 animate-pulse rounded'></div>
        </div>
        <div className='h-8 w-3/4 bg-gray-200 animate-pulse rounded'></div>
        <div className='h-4 w-1/2 bg-gray-200 animate-pulse rounded'></div>
        <div className='h-[400px] w-full bg-gray-200 animate-pulse rounded'></div>
      </div>
    );
  }

  // Render not found state
  if (!content) {
    return (
      <div className='container mx-auto py-8'>
        <div className='flex items-center space-x-2 mb-8'>
          <Button variant='outline' size='sm' onClick={() => router.back()}>
            Back
          </Button>
        </div>
        <Card>
          <CardContent className='py-12 text-center'>
            <h1 className='text-2xl font-bold mb-4'>Content Not Found</h1>
            <p className='text-muted-foreground mb-6'>
              The content you're looking for doesn't exist or has been removed.
            </p>
            <Link href='/dashboard/education' passHref>
              <Button>Return to Library</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render content based on type
  const renderContent = () => {
    switch (content.type) {
      case 'video':
        return <VideoPlayer video={content} />;
      case 'article':
        return <ArticleViewer article={content} />;
      case 'infographic':
        return <InfographicViewer infographic={content} />;
      case 'avatar':
        return <AvatarAssistant />;
      case 'tooltip':
        return (
          <div className='p-4 border rounded-lg'>
            <h3 className='font-semibold text-lg mb-2'>{content.title}</h3>
            <div dangerouslySetInnerHTML={{ __html: content.content }} />
          </div>
        );
      default:
        return <p>Content not available</p>;
    }
  };

  return (
    <div className='container mx-auto py-8 space-y-8'>
      {/* Navigation */}
      <div className='flex items-center space-x-2 mb-4'>
        <Button variant='outline' size='sm' onClick={() => router.back()}>
          Back
        </Button>
        <Link href='/dashboard/education' passHref>
          <Button variant='outline' size='sm'>
            Library
          </Button>
        </Link>
      </div>

      {/* Content Header */}
      <div>
        <h1 className='text-3xl font-bold mb-2'>{content.title}</h1>
        <p className='text-muted-foreground mb-4'>{content.description}</p>
        <div className='flex flex-wrap items-center gap-2 mb-6'>
          {content.tags.map(tag => (
            <Badge key={tag} variant='secondary'>
              {tag}
            </Badge>
          ))}
          <span className='text-xs text-muted-foreground ml-2'>
            Updated {formatDate(content.updatedAt)}
          </span>
        </div>
      </div>

      {/* Main Content */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
        <div className='md:col-span-2'>{renderContent()}</div>

        <div className='space-y-6'>
          {/* Content Feedback */}
          <ContentFeedback contentId={content.id} />

          {/* Related Content */}
          <Card>
            <CardContent className='p-4'>
              <h3 className='font-semibold text-lg mb-4'>Related Content</h3>
              <div className='space-y-4'>
                <div className='flex items-start space-x-3'>
                  <div className='w-12 h-12 bg-muted rounded flex items-center justify-center'>
                    🎬
                  </div>
                  <div>
                    <h4 className='font-medium'>Understanding Wills</h4>
                    <p className='text-sm text-muted-foreground'>
                      Learn what a will is and why it's important.
                    </p>
                  </div>
                </div>

                <div className='h-px w-full bg-gray-200 my-4'></div>

                <div className='flex items-start space-x-3'>
                  <div className='w-12 h-12 bg-muted rounded flex items-center justify-center'>
                    📄
                  </div>
                  <div>
                    <h4 className='font-medium'>Choosing an Executor</h4>
                    <p className='text-sm text-muted-foreground'>
                      Tips for selecting the best executor for your estate.
                    </p>
                  </div>
                </div>

                <div className='h-px w-full bg-gray-200 my-4'></div>

                <div className='flex items-start space-x-3'>
                  <div className='w-12 h-12 bg-muted rounded flex items-center justify-center'>
                    📊
                  </div>
                  <div>
                    <h4 className='font-medium'>Estate Planning Process</h4>
                    <p className='text-sm text-muted-foreground'>
                      A visual guide to the estate planning process.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
