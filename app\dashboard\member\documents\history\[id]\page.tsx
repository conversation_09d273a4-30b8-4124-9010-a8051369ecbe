'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DocumentVersionList,
  DocumentVersion,
} from '@/components/documents/document-version-list';
import { DocumentPreview } from '@/components/documents/document-preview';
import {
  Headline,
  Subhead,
} from '../../../../../../components/ui/brand/typography';
import { AlertCircle, ArrowLeft, Clock, Download, History } from 'lucide-react';

// Mock data for document versions
const getMockDocumentVersions = (documentId: string): DocumentVersion[] => {
  const today = new Date();
  const oneMonthAgo = new Date(today);
  oneMonthAgo.setMonth(today.getMonth() - 1);

  const sixMonthsAgo = new Date(today);
  sixMonthsAgo.setMonth(today.getMonth() - 6);

  const oneYearAgo = new Date(today);
  oneYearAgo.setFullYear(today.getFullYear() - 1);

  return [
    {
      id: 'v1.2',
      documentType: documentId.includes('will')
        ? 'Will'
        : documentId.includes('poa')
          ? 'Power of Attorney'
          : 'Living Trust',
      createdAt: today.toLocaleDateString(),
      status: 'current',
      changes: 'Updated primary beneficiary information',
    },
    {
      id: 'v1.1',
      documentType: documentId.includes('will')
        ? 'Will'
        : documentId.includes('poa')
          ? 'Power of Attorney'
          : 'Living Trust',
      createdAt: oneMonthAgo.toLocaleDateString(),
      status: 'archived',
      changes: 'Updated contact information and address',
    },
    {
      id: 'v1.0',
      documentType: documentId.includes('will')
        ? 'Will'
        : documentId.includes('poa')
          ? 'Power of Attorney'
          : 'Living Trust',
      createdAt: sixMonthsAgo.toLocaleDateString(),
      status: 'archived',
      changes: 'Initial document creation',
    },
  ];
};

// Mock document content for preview
const getMockDocumentContent = (documentId: string, versionId: string) => {
  return (
    <div className='space-y-4'>
      <h1 className='text-2xl font-bold'>
        {documentId.includes('will')
          ? 'Last Will and Testament'
          : documentId.includes('poa')
            ? 'Power of Attorney'
            : 'Living Trust'}
      </h1>
      <p className='text-[var(--custom-gray-medium)]'>
        Version: {versionId} | Created: {new Date().toLocaleDateString()}
      </p>
      <hr />
      <p>
        This is a mock preview of the document content for version {versionId}.
        In a real implementation, this would display the actual document content
        from the specified version.
      </p>
      <p>
        The content would include all the specific details of the document as
        they appeared in this version, allowing you to compare changes between
        versions.
      </p>
    </div>
  );
};

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function DocumentHistoryPage({ params }: PageProps) {
  const router = useRouter();
  const { id } = await params;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [documentType, setDocumentType] = useState<string>('');
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);

  useEffect(() => {
    // Simulate API call to fetch document versions
    const fetchDocumentVersions = async () => {
      try {
        // In a real implementation, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock document data based on ID
        const mockDocumentType = id.includes('will')
          ? 'Will'
          : id.includes('poa')
            ? 'Power of Attorney'
            : 'Living Trust';

        setDocumentType(mockDocumentType);
        setVersions(getMockDocumentVersions(id));
        setLoading(false);
      } catch (err) {
        setError('Failed to load document history. Please try again.');
        setLoading(false);
      }
    };

    fetchDocumentVersions();
  }, [id]);

  const handleViewVersion = (versionId: string) => {
    setSelectedVersion(versionId);
    setShowPreviewDialog(true);
  };

  const handleRegenerateVersion = (versionId: string) => {
    // In a real implementation, this would call an API to regenerate the document
    console.log('Regenerating document version:', versionId);

    // Show a success message (in a real app, this would happen after the API call succeeds)
    alert(`Document version ${versionId} has been regenerated.`);
  };

  const handleDownloadVersion = (versionId: string) => {
    // In a real implementation, this would download the document
    console.log('Downloading document version:', versionId);

    // Show a success message (in a real app, this would trigger a download)
    alert(`Document version ${versionId} is being downloaded.`);
  };

  const handleBack = () => {
    router.push('/dashboard/member/documents/history');
  };

  if (loading) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-8'>
            <Skeleton className='h-10 w-1/3 mb-2' />
            <Skeleton className='h-5 w-1/2' />
          </div>
          <Skeleton className='h-[400px] w-full rounded-md' />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='mb-8'>
            <Headline className='mb-2'>Document History</Headline>
            <Subhead className='text-muted-foreground'>
              View the version history of your document
            </Subhead>
          </div>

          <Alert variant='destructive' className='mb-6'>
            <AlertCircle className='h-4 w-4' />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <Button variant='outline' onClick={handleBack}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>{documentType} History</Headline>
          <Subhead className='text-muted-foreground'>
            View and manage previous versions of your{' '}
            {documentType.toLowerCase()}
          </Subhead>
        </div>

        <Card className='mb-6'>
          <CardHeader className='flex flex-row items-center justify-between'>
            <div>
              <CardTitle>Version History</CardTitle>
              <CardDescription>
                All versions of your {documentType.toLowerCase()} are stored
                securely
              </CardDescription>
            </div>
            <History className='h-6 w-6 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <DocumentVersionList
              versions={versions}
              onView={handleViewVersion}
              onRegenerate={handleRegenerateVersion}
              onDownload={handleDownloadVersion}
            />
          </CardContent>
        </Card>

        <div className='flex justify-between'>
          <Button variant='outline' onClick={handleBack}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Documents
          </Button>
        </div>

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100 mt-8'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Document Versions
          </h3>
          <p className='text-blue-700 mb-4'>
            Each time you update your document, a new version is created while
            previous versions are preserved.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              All versions are securely stored and can be accessed at any time
            </li>
            <li>You can view the changes made in each version</li>
            <li>You can download any version for your records</li>
            <li>The current version is always used for legal purposes</li>
          </ul>
        </div>
      </div>

      {/* Document Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className='max-w-4xl'>
          <DialogHeader>
            <DialogTitle>
              {documentType} - Version {selectedVersion}
            </DialogTitle>
            <DialogDescription>
              Document version from{' '}
              {versions.find(v => v.id === selectedVersion)?.createdAt}
            </DialogDescription>
          </DialogHeader>

          <div className='py-4'>
            <DocumentPreview
              documentType={documentType}
              documentContent={
                selectedVersion ? (
                  getMockDocumentContent(id, selectedVersion)
                ) : (
                  <></>
                )
              }
              showWatermark={selectedVersion !== versions[0]?.id} // Show watermark for non-current versions
            />
          </div>

          <div className='flex justify-end space-x-2'>
            <Button
              variant='outline'
              onClick={() => setShowPreviewDialog(false)}
            >
              Close
            </Button>
            <Button
              onClick={() => {
                handleDownloadVersion(selectedVersion || '');
                setShowPreviewDialog(false);
              }}
            >
              <Download className='h-4 w-4 mr-2' />
              Download
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
