'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { DocumentAccess } from '@/components/emergency/document-access';
import { VerificationForm } from '@/components/emergency/verification-form';
import { DocumentViewer } from '@/components/emergency/document-viewer';
import { Document, AccessTrigger } from '@/components/emergency/types';
import {
  AlertCircle,
  CheckCircle2,
  ShieldAlert,
  FileText,
  Lock,
  Users,
} from 'lucide-react';
import { Headline, Subhead } from '../../../components/ui/brand/typography';
import { useUserContext } from '@/components/welon-trust/user-context';

// Function to generate mock documents for a specific user
const generateUserDocuments = (
  userId: string,
  userName: string
): Document[] => {
  const documentTypes = [
    { title: 'Last Will and Testament', type: 'Legal' as const },
    { title: 'Power of Attorney', type: 'Legal' as const },
    { title: 'Healthcare Directive', type: 'Medical' as const },
    { title: 'Living Will', type: 'Medical' as const },
    { title: 'Pet Care Instructions', type: 'Personal' as const },
    { title: 'Digital Asset Instructions', type: 'Personal' as const },
    { title: 'Funeral Arrangements', type: 'Personal' as const },
    { title: 'Trust Agreement', type: 'Legal' as const },
    { title: 'Insurance Policies', type: 'Financial' as const },
    { title: 'Bank Account Information', type: 'Financial' as const },
  ];

  // Generate 3-6 random documents for each user
  const numDocuments = Math.floor(Math.random() * 4) + 3;
  const selectedDocs = documentTypes
    .sort(() => Math.random() - 0.5)
    .slice(0, numDocuments);

  return selectedDocs.map((doc, index) => ({
    id: `${userId}-doc-${index + 1}`,
    userId: userId,
    title: doc.title,
    type: doc.type,
    url: `/mock-${doc.title.toLowerCase().replace(/\s+/g, '-')}.pdf`,
    createdAt: new Date(
      Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000
    ).toISOString(),
    updatedAt: new Date(
      Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000
    ).toISOString(),
  }));
};

export default function EmergencyDocumentsPage() {
  const { selectedUser } = useUserContext();
  const [isVerified, setIsVerified] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [accessType, setAccessType] = useState<AccessTrigger>('Death');
  const [expiryDate, setExpiryDate] = useState<string | undefined>(
    new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString() // 180 days from now
  );
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [viewingDocument, setViewingDocument] = useState<Document | null>(null);

  // Load documents when user is selected
  useEffect(() => {
    if (selectedUser && isVerified) {
      const userDocuments = generateUserDocuments(
        selectedUser.id,
        selectedUser.name
      );
      setDocuments(userDocuments);
    } else {
      setDocuments([]);
    }
  }, [selectedUser, isVerified]);

  const handleVerify = (code: string) => {
    // In a real implementation, this would call an API to verify the code
    if (code === '123456') {
      // Mock verification
      setIsVerified(true);

      // Load documents for the selected user
      if (selectedUser) {
        const userDocuments = generateUserDocuments(
          selectedUser.id,
          selectedUser.name
        );
        setDocuments(userDocuments);
        setAlert({
          type: 'success',
          message: `Verification successful. You now have access to ${selectedUser.name}'s documents.`,
        });
      } else {
        setAlert({
          type: 'error',
          message: 'Please select a member first before accessing documents.',
        });
      }
    } else {
      setAlert({
        type: 'error',
        message: 'Invalid verification code. Please try again.',
      });
    }

    // Clear alert after 5 seconds
    setTimeout(() => setAlert(null), 5000);
  };

  const handleResendCode = () => {
    // In a real implementation, this would call an API to resend the code
    setAlert({
      type: 'success',
      message: 'A new verification code has been sent.',
    });

    // Clear alert after 5 seconds
    setTimeout(() => setAlert(null), 5000);
  };

  const handleViewDocument = (documentId: string) => {
    const document = documents.find(doc => doc.id === documentId);
    if (document) {
      setViewingDocument(document);
    }
  };

  const handleDownloadDocument = (documentId: string) => {
    const doc = documents.find(doc => doc.id === documentId);
    if (doc) {
      // Create a mock download
      const element = document.createElement('a');
      const file = new Blob(
        [
          `Mock content for ${doc.title}\n\nDocument ID: ${doc.id}\nType: ${doc.type}\nCreated: ${doc.createdAt}\nUpdated: ${doc.updatedAt}\n\nThis is a mock document for demonstration purposes.`,
        ],
        { type: 'text/plain' }
      );
      element.href = URL.createObjectURL(file);
      element.download = `${doc.title.replace(/\s+/g, '_')}.txt`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);

      setAlert({
        type: 'success',
        message: `Document "${doc.title}" has been downloaded.`,
      });

      // Clear alert after 3 seconds
      setTimeout(() => setAlert(null), 3000);
    }
  };

  const handleCloseViewer = () => {
    setViewingDocument(null);
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-3xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Emergency Document Access</Headline>
          <Subhead className='text-muted-foreground'>
            {selectedUser
              ? `${
                  accessType === 'Death'
                    ? 'Access to documents following verification of death certificate'
                    : 'Temporary access to documents during member incapacitation'
                } for ${selectedUser.name}.`
              : 'Select a member to access their emergency documents.'}
          </Subhead>
        </div>

        {/* No User Selected Warning */}
        {!selectedUser && (
          <Alert className='mb-6 bg-amber-50 text-amber-800 border-amber-200'>
            <Users className='h-4 w-4' />
            <AlertTitle>No Member Selected</AlertTitle>
            <AlertDescription>
              Please select a member from the dropdown above to access their
              emergency documents. You must choose a specific member before
              proceeding with document access.
            </AlertDescription>
          </Alert>
        )}

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        {selectedUser && !isVerified ? (
          <Card className='mb-8'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <ShieldAlert className='h-5 w-5 text-blue-600' />
                Secure Access for {selectedUser.name}
              </CardTitle>
              <CardDescription>
                This page provides{' '}
                {accessType === 'Death' ? 'permanent' : 'temporary'} access to{' '}
                {selectedUser.name}'s important documents. Please verify your
                identity to proceed.
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Access Information */}
              <div className='grid md:grid-cols-2 gap-4'>
                <div className='flex items-start gap-3'>
                  <div
                    className={`${accessType === 'Death' ? 'bg-destructive/10' : 'bg-blue-50'} p-2 rounded-full`}
                  >
                    {accessType === 'Death' ? (
                      <Lock
                        className={`h-5 w-5 ${accessType === 'Death' ? 'text-destructive' : 'text-blue-600'}`}
                      />
                    ) : (
                      <FileText className='h-5 w-5 text-blue-600' />
                    )}
                  </div>
                  <div>
                    <h3 className='font-medium mb-1'>Document Access</h3>
                    <p className='text-sm text-muted-foreground'>
                      {accessType === 'Death'
                        ? 'Permanent access following death certificate verification'
                        : 'Temporary access during member incapacitation'}
                    </p>
                  </div>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='bg-blue-50 p-2 rounded-full'>
                    <ShieldAlert className='h-5 w-5 text-blue-600' />
                  </div>
                  <div>
                    <h3 className='font-medium mb-1'>Security Required</h3>
                    <p className='text-sm text-muted-foreground'>
                      Identity verification protects sensitive information
                    </p>
                  </div>
                </div>
              </div>

              {/* Verification Section */}
              <div className='border-t pt-6'>
                <div className='mb-4'>
                  <h3 className='font-medium mb-2'>Verify Your Identity</h3>
                  <p className='text-sm text-muted-foreground'>
                    Enter the verification code sent to your registered email or
                    phone number.
                  </p>
                </div>

                <VerificationForm
                  onVerify={handleVerify}
                  onResendCode={handleResendCode}
                  embedded={true}
                />
              </div>
            </CardContent>
          </Card>
        ) : selectedUser && isVerified ? (
          <>
            <Card className='mb-8'>
              <CardHeader>
                <CardTitle>
                  Access Information for {selectedUser.name}
                </CardTitle>
                <CardDescription>
                  You have been granted{' '}
                  {accessType === 'Incapacitation' ? 'temporary' : 'permanent'}{' '}
                  access to {selectedUser.name}'s documents.
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {accessType === 'Incapacitation' && expiryDate && (
                  <div className='bg-blue-2157c/10 p-4 rounded-md'>
                    <p className='font-medium'>
                      Your access is temporary and will expire on{' '}
                      {new Date(expiryDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })}
                      .
                    </p>
                  </div>
                )}
                <p className='text-sm text-muted-foreground'>
                  These documents contain sensitive information. Please handle
                  them with care and respect the privacy of the individual.
                </p>
              </CardContent>
            </Card>

            <DocumentAccess
              documents={documents}
              accessType={accessType}
              expiryDate={
                accessType === 'Incapacitation' ? expiryDate : undefined
              }
              onView={handleViewDocument}
              onDownload={handleDownloadDocument}
            />
          </>
        ) : null}

        {/* Document Viewer Modal */}
        {viewingDocument && (
          <DocumentViewer
            document={viewingDocument}
            isOpen={!!viewingDocument}
            onClose={handleCloseViewer}
            onDownload={handleDownloadDocument}
          />
        )}
      </div>
    </div>
  );
}
