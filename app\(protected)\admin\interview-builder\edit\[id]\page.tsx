'use client';

import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';

export default function EditVersionPage() {
  const router = useRouter();
  const params = useParams();
  const versionId = params.id as string;

  useEffect(() => {
    // Redirect to the build page
    if (versionId) {
      router.replace(`/admin/interview-builder/build/${versionId}`);
    }
  }, [versionId, router]);

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
          <p className='mt-2 text-[var(--custom-gray-medium)]'>
            Redirecting to editor...
          </p>
        </div>
      </div>
    </div>
  );
}
