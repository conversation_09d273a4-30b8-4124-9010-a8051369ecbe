/**
 * Admin Content Analytics Page
 *
 * Page for administrators to view analytics for educational content.
 */

'use client';

import React, { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

// Loading fallback component
function ContentAnalyticsPageLoading() {
  return (
    <div className='container mx-auto py-8'>
      <div className='flex items-center space-x-2 mb-8'>
        <div className='h-8 w-24 bg-gray-200 rounded animate-pulse'></div>
      </div>
      <div className='h-96 bg-gray-100 rounded-lg animate-pulse'></div>
    </div>
  );
}

// Main component that uses useSearchParams
function ContentAnalyticsContent() {
  const searchParams = useSearchParams();
  const contentId = searchParams.get('id');

  const { contentById, contentAnalytics } = useEducationalContent();

  // Get content and analytics
  const content = contentId ? contentById(contentId) : undefined;
  const analytics = contentId ? contentAnalytics(contentId) : undefined;

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // If no content ID is provided
  if (!contentId) {
    return (
      <div className='container mx-auto py-8'>
        <div className='flex items-center space-x-2 mb-8'>
          <Link href='/admin/content' passHref>
            <Button variant='outline' size='sm'>
              Back to Content
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className='py-12 text-center'>
            <h1 className='text-2xl font-bold mb-4'>No Content Selected</h1>
            <p className='text-muted-foreground mb-6'>
              Please select a content item to view its analytics.
            </p>
            <Link href='/admin/content' passHref>
              <Button>Go to Content Management</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If content not found
  if (!content) {
    return (
      <div className='container mx-auto py-8'>
        <div className='flex items-center space-x-2 mb-8'>
          <Link href='/admin/content' passHref>
            <Button variant='outline' size='sm'>
              Back to Content
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className='py-12 text-center'>
            <h1 className='text-2xl font-bold mb-4'>Content Not Found</h1>
            <p className='text-muted-foreground mb-6'>
              The content you're looking for doesn't exist or has been removed.
            </p>
            <Link href='/admin/content' passHref>
              <Button>Go to Content Management</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-8'>
      <div className='flex items-center space-x-2 mb-4'>
        <Link href='/admin/content' passHref>
          <Button variant='outline' size='sm'>
            Back to Content
          </Button>
        </Link>
      </div>

      <div>
        <h1 className='text-3xl font-bold mb-2'>{content.title}</h1>
        <div className='flex items-center space-x-2'>
          <Badge>{content.type}</Badge>
          <Badge variant='outline'>{content.status}</Badge>
          <span className='text-sm text-muted-foreground'>
            Last updated: {formatDate(content.updatedAt)}
          </span>
        </div>
      </div>

      <div className='grid md:grid-cols-3 gap-6'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-2xl'>{analytics?.views || 0}</CardTitle>
            <CardDescription>Total Views</CardDescription>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground'>
              Number of times this content has been viewed.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-2xl'>
              {analytics?.completionRate || 0}%
            </CardTitle>
            <CardDescription>Completion Rate</CardDescription>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground'>
              Percentage of users who completed viewing the content.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-2xl'>
              {analytics?.averageRating.toFixed(1) || 0}
            </CardTitle>
            <CardDescription>Average Rating</CardDescription>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground'>
              Average rating from user feedback (out of 5).
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='feedback'>
        <TabsList>
          <TabsTrigger value='feedback'>User Feedback</TabsTrigger>
          <TabsTrigger value='engagement'>Engagement Metrics</TabsTrigger>
          <TabsTrigger value='distribution'>Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value='feedback' className='mt-6'>
          <Card>
            <CardHeader>
              <CardTitle>User Feedback</CardTitle>
              <CardDescription>
                Feedback submitted by users for this content.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Rating</TableHead>
                    <TableHead>Comment</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {analytics?.feedback && analytics.feedback.length > 0 ? (
                    analytics.feedback.map(feedback => (
                      <TableRow key={feedback.id}>
                        <TableCell>{formatDate(feedback.createdAt)}</TableCell>
                        <TableCell>
                          <div className='flex'>
                            {Array.from({ length: 5 }).map((_, i) => (
                              <span
                                key={i}
                                className={`text-lg ${
                                  i < feedback.rating
                                    ? 'text-yellow-400'
                                    : 'text-[var(--custom-gray-light)]'
                                }`}
                              >
                                ★
                              </span>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          {feedback.comment || 'No comment'}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className='text-center py-8'>
                        No feedback available for this content.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='engagement' className='mt-6'>
          <Card>
            <CardHeader>
              <CardTitle>Engagement Metrics</CardTitle>
              <CardDescription>
                Detailed metrics about how users engage with this content.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-12'>
                <p className='text-muted-foreground'>
                  Engagement metrics visualization would be displayed here.
                </p>
                <p className='text-sm text-muted-foreground mt-2'>
                  (This would include charts showing engagement over time,
                  drop-off points, etc.)
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='distribution' className='mt-6'>
          <Card>
            <CardHeader>
              <CardTitle>Content Distribution</CardTitle>
              <CardDescription>
                How this content is being distributed across the platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-6'>
                <div>
                  <h3 className='font-semibold mb-2'>Integration Points</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Location</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Engagement</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Education Library</TableCell>
                        <TableCell>843</TableCell>
                        <TableCell>76%</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Document Creation Flow</TableCell>
                        <TableCell>312</TableCell>
                        <TableCell>62%</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Onboarding</TableCell>
                        <TableCell>90</TableCell>
                        <TableCell>81%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div>
                  <h3 className='font-semibold mb-2'>Referral Sources</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Source</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Percentage</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Direct Link</TableCell>
                        <TableCell>523</TableCell>
                        <TableCell>42%</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Search</TableCell>
                        <TableCell>312</TableCell>
                        <TableCell>25%</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Related Content</TableCell>
                        <TableCell>245</TableCell>
                        <TableCell>20%</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Contextual Popup</TableCell>
                        <TableCell>165</TableCell>
                        <TableCell>13%</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Export the main component wrapped in Suspense
export default function ContentAnalyticsPage() {
  return (
    <Suspense fallback={<ContentAnalyticsPageLoading />}>
      <ContentAnalyticsContent />
    </Suspense>
  );
}
