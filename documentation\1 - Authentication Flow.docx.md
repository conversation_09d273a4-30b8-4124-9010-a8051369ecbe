# 1 \- Authentication Flow

# **Document 1 \- Functional Specification: Authentication Flow**

## **Overview**

The authentication flow for the Childfree Legacy Web Application ensures secure, compliant, and user-friendly access to sensitive estate planning data. Leveraging AWS Amplify, this specification outlines the processes for login, logout, password reset, and a support section, prioritizing robust security (e.g., HIPAA and SOC2 compliance), role-based access control (RBAC), and an intuitive experience designed for the 65+ demographic. Strong authentication mechanisms, including multi-factor authentication (MFA) with comprehensive failure handling, safeguard user privacy and foster trust in managing critical estate planning information.

---

## **1\. Login**

The login process securely authenticates users based on their roles, enforcing MFA where required and ensuring compliance with security standards.

### **Key Requirements**

#### **User Roles**

- **Members**: End users managing their estate plans.
- **Administrators**: System managers with elevated permissions.
- **Welon Trust**: Trust administrators executing estate plans.
- **Professionals**: Advisors and authorized individuals accessing member data.

#### **Authentication Method**

- Email and password, with passwords hashed and salted using AWS Cognito’s secure storage.

#### **Security Measures**

- **Rate Limiting**: Lock account for 15 minutes after 5 failed password attempts.
- **HTTPS**: Enforce TLS for all communications via Amplify’s default configuration.

#### **Multi-Factor Authentication (MFA)**

- **Optional for Members**: Encouraged with incentives (e.g., a security badge) to balance usability and security.
- **Mandatory for Administrators and Welon Trust**: Protects sensitive functions and data.
- **MFA Methods**: SMS or email, user-selectable via Cognito.
- **Code Expiry**: 5 minutes.
- **Failed Attempts**: Lock account for 15 minutes after 3 failed MFA attempts.

#### **Session Management**

- Sessions expire after 30 minutes of inactivity, managed by Cognito tokens.
- Users can view and terminate active sessions via a dashboard feature.

### **User Flow**

1. User navigates to /login.
2. Enters email and password in the Amplify Authenticator UI.
3. System validates credentials using AWS Cognito.
4. If MFA is enabled:
   - User selects MFA method (SMS or email) if not preconfigured.
   - Receives a 6-digit code via the chosen method.
   - Enters the code in the provided field.
5. Upon success, redirects to a role-specific dashboard:
   - Members: /member/dashboard
   - Administrators: /admin/dashboard
   - Welon Trust: /welon/dashboard
   - Professionals: /professional/dashboard

### **Edge Cases**

- **Invalid Credentials**: Display "Invalid email or password."
- **Account Lockout (Password)**: Lock after 5 failed attempts; notify via email using AWS SES.
- **Incorrect MFA Code**: Allow 3 attempts; display "Incorrect MFA code."
- **Expired MFA Code**: Prompt to request a new code with "Code expired."
- **MFA Code Not Received**: Offer "Resend Code" and method-switching options.
- **Account Lockout (MFA)**: Lock after 3 failed MFA attempts; notify via email.

### **Compliance Considerations**

- Safeguard credentials and session data per HIPAA and SOC2 using Cognito and AWS KMS encryption.
- Log all login attempts and MFA events in AWS CloudWatch for auditing.

### **UI Components**

| Element             | Description                          |
| ------------------- | ------------------------------------ |
| Email Field         | Required input for email             |
| Password Field      | Obscured password input              |
| "Login" Button      | Submits credentials via Amplify Auth |
| "Forgot Password?"  | Link to /forgot-password             |
| MFA Method Selector | Dropdown for SMS or email            |
| MFA Code Field      | Input for 6-digit code               |
| "Resend Code"       | Button to resend MFA code            |
| Error Message       | Displays validation errors           |

---

## **2\. Logout**

The logout feature securely terminates user sessions to prevent unauthorized access.

### **Key Requirements**

- **Session Termination**: Invalidate Cognito tokens server-side.
- **User Feedback**: Display "You have been successfully logged out."
- **Redirect**: Return to /login.

### **User Flow**

1. User clicks "Logout" in the navigation menu.
2. System calls Auth.signOut() from Amplify, terminating the session and clearing local cookies.
3. Redirects to /login with a confirmation message.

### **Edge Cases**

- **Post-Logout Access**: Redirect to /login for protected routes using Amplify’s route protection.
- **Inactivity Timeout**: Auto-logout after 30 minutes; warn at 25 minutes via a React timer component.

### **Compliance Considerations**

- Ensure complete session data clearance per HIPAA and SOC2 standards.

### **UI Components**

| Element              | Description                     |
| -------------------- | ------------------------------- |
| "Logout" Button      | In navigation menu              |
| Confirmation Message | Displayed on /login post-logout |

---

## **3\. Password Reset**

The password reset process securely restores account access for users who forget their passwords.

### **Key Requirements**

- **Initiation**: Triggered via registered email using AWS SES.
- **Security Measures**:
  - Single-use reset tokens generated by Cognito, expiring in 15 minutes.
  - Rate limit to 3 requests per hour; enforce CAPTCHA via Amplify after the first request.
- **Password Strength**:
  - Minimum 12 characters: uppercase, lowercase, numbers, special characters.
  - No reuse of the last 3 passwords, enforced by Cognito.
- **User Feedback**: Real-time password strength feedback via a custom React component.

### **User Flow**

1. User clicks "Forgot Password?" on /login, redirecting to /forgot-password.
2. Enters email and clicks "Submit."
3. System sends a reset code via AWS SES (e.g., Auth.forgotPassword()).
4. User enters the code and new password on /reset-password.
5. Submits new password; system updates it via Auth.forgotPasswordSubmit().
6. Confirms success and redirects to /login.

### **Edge Cases**

- **Expired Token**: Display "Code expired. Request a new one."
- **Unregistered Email**: Display "No account found."
- **Mismatched Passwords**: Display "Passwords do not match."

### **Compliance Considerations**

- Secure token generation and storage via Cognito.
- Log reset attempts in CloudWatch for SOC2 and HIPAA compliance.

### **UI Components**

| Element            | Description           |
| ------------------ | --------------------- |
| Email Field        | For registered email  |
| "Submit" Button    | Sends reset code      |
| Code Field         | Input for reset code  |
| New Password Field | For new password      |
| Confirm Password   | Matches new password  |
| Password Strength  | Real-time indicator   |
| "Reset Password"   | Submits new password  |
| Error/Success Msg  | Reset status feedback |

---

## **4\. Additional Authentication Features**

Optional features enhance security and usability with AWS Amplify integration.

- **Remember Me**:
  - Extends session duration to 30 days using Cognito refresh tokens and secure cookies.
  - Requires re-authentication for sensitive actions (e.g., updating estate plans).
- **Account Lockout**:
  - Locks account after 5 failed login attempts, configurable via Cognito settings.
- **Password Change**:
  - Available at /settings/security.
  - Requires current password verification via Auth.changePassword().
- **RBAC**:
  - Enforced via Cognito User Groups (e.g., Members, Admins).

### **Implementation Notes**

- Use HttpOnly and Secure flags for "Remember Me" cookies via Amplify configuration.
- Log password changes and lockouts in CloudWatch.

### **UI Components**

| Feature           | Component Description                                             |
| ----------------- | ----------------------------------------------------------------- |
| "Remember Me"     | Checkbox on login page                                            |
| "Change Password" | Button in settings with input fields                              |
| Active Sessions   | List with "Log out all" option via Auth.signOut({ global: true }) |

---

## **5\. Support Section**

A dedicated support section allows members to report issues, enhancing trust and reliability.

### **Key Requirements**

- **Access**: Available at /member/support via a "Help" icon.
- **Form**: Fields for description, screenshot (up to 10MB), and contact info (pre-filled).
- **Submission**: Logs tickets in DynamoDB via AppSync; optionally emails support via SES.
- **Admin Management**: View and manage tickets at /admin/support.

### **User Flow**

1. Member clicks "Help" icon.
2. Redirects to /member/support or opens a modal.
3. Submits form; system confirms receipt with "Ticket received."
4. Administrators manage tickets at /admin/support.

### **Ticketing/Error Submission**

- **Access**: "Report Issue" button on error pages or support section.
- **Form**: Captures description, screenshot, and context (e.g., URL, timestamp).
- **Submission**: Logs ticket via AppSync and notifies support.

### **Technical Specifications**

- **Route**: /member/support.
- **API**: POST /support/tickets and POST /support/errors via AppSync GraphQL.
- **Database**: support_tickets table in DynamoDB.

---

## **6\. Technical Specifications**

### **Frontend**

- **Framework**: React with Redux, hosted via Amplify Hosting.
- **Routes**:
  - /login
  - /forgot-password
  - /reset-password
  - /settings/security
  - /member/support

### **Backend**

- **APIs (GraphQL via AppSync)**:
  - login: Authenticates via Cognito; returns token and MFA status.
  - logout: Invalidates session.
  - forgotPassword: Sends reset code.
  - resetPassword: Updates password.
  - createSupportTicket: Logs ticket in DynamoDB.
  - reportError: Logs error report.
- **Encryption**: TLS/SSL enforced by Amplify.
- **Token Management**: Cognito refresh tokens for session renewal.

### **Database (DynamoDB)**

- **Table: users**
  - Columns: id, email, password_hash, role, mfa_enabled, last_login
- **Table: support_tickets**
  - Columns: id, member_id, description, attachment_url, status, created_at, updated_at

### **Logging**

- Track authentication events and support interactions in CloudWatch.

---

## **7\. Testing and Validation**

- **Unit Tests**: Validate Cognito integration, MFA, and ticket submission.
- **Integration Tests**: Verify end-to-end flows (login, logout, support).
- **Security Testing**: Penetration tests for vulnerabilities.
- **UAT**: Ensure usability for the 65+ demographic.

### **Test Cases**

| Scenario                    | Expected Outcome                       |
| --------------------------- | -------------------------------------- |
| Successful Login            | Redirect to dashboard                  |
| Failed Login (5x)           | Account locked, email sent             |
| Password Reset (Valid)      | Password updated, login succeeds       |
| Password Reset (Expired)    | Error, prompt for new code             |
| MFA Verification            | Code accepted, access granted          |
| MFA Verification Fail       | Code rejected, access denied           |
| Support Ticket Submission   | Ticket logged, confirmation shown      |
| Admin Views Support Tickets | Tickets listed with management options |

---

## **8\. Compliance and Security**

- **HIPAA**: Encrypt data with AWS KMS and TLS.
- **SOC2**: Enforce RBAC and logging via Cognito and CloudWatch.
- **State Laws**: Comply with CCPA for data privacy.
- **Audit Trails**: Log all events in CloudWatch.

---

## **Summary**

This authentication flow leverages AWS Amplify to deliver a secure, compliant, and accessible system for the Childfree Legacy platform. Tailored for Members, Administrators, Welon Trust, and Professionals, it balances stringent security (HIPAA, SOC2) with usability for the 65+ demographic. The integrated support section enhances reliability, fostering user trust in managing estate planning data.
