import { useState, useEffect } from 'react';
import { generateClient } from 'aws-amplify/api';
import { Schema } from '@/amplify/data/resource';
import { toast } from 'sonner';
import { uploadData, getUrl, remove } from 'aws-amplify/storage';
import { getCurrentUser } from 'aws-amplify/auth';
import { createNotification } from '@/lib/api/notifications';

// Use auto-generated Document type from Amplify schema
type DocumentType = Schema['Document']['type'];

export function useDocuments() {
  const [documents, setDocuments] = useState<DocumentType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const client = generateClient<Schema>();

  // Fetch documents from the API
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      const { data, errors } = await client.models.Document.list();
      if (errors) throw new Error(errors[0].message);
      setDocuments(data as DocumentType[]);
    } catch (err: any) {
      setError(err.message);
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Upload file to S3
  const uploadFile = async (file: File, documentId: string) => {
    const user = await getCurrentUser();
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    // Use identity-based path for proper access control
    const fileKey = `documents/{identity}/${documentId}/${timestamp}.${fileExtension}`;

    await uploadData({
      path: fileKey,
      data: file,
      options: { contentType: file.type },
    }).result;

    return {
      key: fileKey,
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      uploadedBy: user.userId,
    };
  };

  // Get a signed URL for a file
  const getFileUrl = async (fileKey: string): Promise<string> => {
    const result = await getUrl({
      path: fileKey,
      options: { validateObjectExistence: true },
    });
    return result.url.toString();
  };

  // Delete file from storage
  const deleteFile = async (fileKey: string): Promise<void> => {
    await remove({ path: fileKey });
  };

  // Create a new document with file upload
  const createDocumentWithFile = async (data: {
    title: string;
    type: 'Will' | 'Trust' | 'POA' | 'Other';
    userId: string;
    userCognitoId: string;
    memberName: string;
    memberEmail?: string;
    documentNotes?: string;
    assignedWelonTrustId?: string;
    file: File;
    status: 'approved' | 'rejected';
    rejectionReason?: string;
  }) => {
    try {

    // Create document record
    const documentData = {
      title: data.title,
      type: data.type,
      status: data.status,
      dateCreated: new Date().toISOString(),
      version: '1.0',
      content: JSON.stringify({
        memberName: data.memberName,
        memberEmail: data.memberEmail,
        documentNotes: data.documentNotes,
      }),
      userId: data.userId,
      assignedWelonTrustId: data.userCognitoId, // Set user's Cognito ID for read access
      signatureType: 'manual' as const,
      executionDate: new Date().toISOString(),
      rejectionReason: data.rejectionReason,
    };

    const result = await client.models.Document.create(documentData);
    if (!result.data) throw new Error('Failed to create document record');
    const documentId = result.data.id!;
    const fileInfo = await uploadFile(data.file, documentId);

    // Update document with file info
    const updatedContent = JSON.stringify({
      memberName: data.memberName,
      memberEmail: data.memberEmail,
      documentNotes: data.documentNotes,
      fileMetadata: {
        fileName: fileInfo.fileName,
        fileType: fileInfo.fileType,
        fileSize: fileInfo.fileSize,
        uploadedBy: fileInfo.uploadedBy,
      },
    });

    const updateResult = await client.models.Document.update({
      id: documentId,
      fileUrl: fileInfo.key,
      content: updatedContent,
      lastModified: new Date().toISOString(),
    });

    if (!updateResult.data) throw new Error('Failed to update document');

    // Update local state with the document from database
    if (updateResult.data) {
      setDocuments(prev => [updateResult.data!, ...prev]);
    }
    toast.success('Document uploaded successfully');

    // Send notification to the document owner (user), not the Welon Trust staff
    try {
      const notificationMessage = data.status === 'approved'
        ? `Your document "${data.title}" has been approved by Welon Trust!`
        : `Your document "${data.title}" has been rejected by Welon Trust. Reason: ${data.rejectionReason}`;

      // Send notification to user's Cognito ID (for GraphQL subscriptions)
      await createNotification(notificationMessage, data.userCognitoId);
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
    }

    return updateResult.data;
    } catch (err: any) {
      toast.error(`Failed to upload document: ${err.message}`);
      throw err;
    }
  };

  // Load documents on component mount
  useEffect(() => {
    fetchDocuments();
  }, []);

  const deleteAllDocuments = async (): Promise<void> => {
    for (const document of documents) {
      await client.models.Document.delete({ id: document.id });
      // Delete file from S3 if fileUrl exists
      if (document.fileUrl) {
        try {
          await remove({ path: document.fileUrl });
        } catch (fileError) {
          console.warn('Could not delete file from S3:', fileError);
        }
      }
    }

    setDocuments([]);
    toast.success('All documents deleted successfully');
  };

  return {
    documents,
    loading,
    error,
    fetchDocuments,
    createDocumentWithFile,
    getFileUrl,
    uploadFile,
    deleteFile,
    deleteAllDocuments,
  };
}
