import { useQuery } from '@tanstack/react-query';
import { getTemplateWithHistory } from '@/app/utils/templates';
import type { Schema } from '@/amplify/data/resource';

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

// Extended type for UI display that includes additional fields
interface TemplateVersionDisplay extends TemplateVersion {
  summary?: string;
  legalUpdateId?: string;
  startDate?: string;
  endDate?: string;
}

interface UseTemplateWithHistoryReturn {
  template: Template | null;
  versions: TemplateVersionDisplay[];
  isLoading: boolean;
  error: Error | null;
}

/**
 * Custom hook to fetch template with its version history using React Query
 * @param templateId - The ID of the template to fetch
 * @returns Query result with template data, versions, loading state, and error state
 */
export function useTemplateWithHistory(
  templateId: string
): UseTemplateWithHistoryReturn {
  const { data, isLoading, error } = useQuery({
    queryKey: ['template-with-history', templateId],
    queryFn: async () => {
      if (!templateId) {
        throw new Error('Template ID is required');
      }
      return await getTemplateWithHistory(templateId);
    },
    enabled: !!templateId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Transform the versions data to include display fields
  const transformedVersions: TemplateVersionDisplay[] =
    data?.versions?.map((version, index) => ({
      ...version,
      summary: `Version ${version.versionNumber}`, // You can enhance this with actual summary data
      legalUpdateId: undefined, // Add this field to your schema if needed
      startDate: version.createdAt,
      endDate:
        index === 0
          ? undefined
          : data.versions[index - 1]?.createdAt || undefined, // Current version has no end date
    })) || [];

  return {
    template: data?.template || null,
    versions: transformedVersions,
    isLoading,
    error: error as Error | null,
  };
}
