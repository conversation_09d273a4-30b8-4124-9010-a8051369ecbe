'use client';

import React from 'react';
import { cn } from '../../lib/utils';

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  as?: keyof JSX.IntrinsicElements;
}

/**
 * Container component that provides consistent responsive padding and max-width
 * across the application. Particularly useful for admin pages and content areas.
 */
export function Container({
  children,
  className,
  size = 'xl',
  padding = 'md',
  as: Component = 'div',
  ...props
}: ContainerProps) {
  // Max width classes based on size
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full',
  };

  // Responsive padding classes
  const paddingClasses = {
    none: '',
    sm: 'px-2 py-2 sm:px-4 lg:px-6 lg:py-4',
    md: 'px-4 py-4 sm:px-6 lg:px-8 lg:py-6',
    lg: 'px-6 py-6 sm:px-8 lg:px-12 lg:py-8',
  };

  return (
    <Component
      className={cn(
        'mx-auto w-full',
        sizeClasses[size],
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}

// Specialized container variants for common use cases
export function AdminContainer({
  children,
  className,
  ...props
}: Omit<ContainerProps, 'size' | 'padding'>) {
  return (
    <Container size='xl' padding='md' className={cn('', className)} {...props}>
      {children}
    </Container>
  );
}

export function ContentContainer({
  children,
  className,
  ...props
}: Omit<ContainerProps, 'size' | 'padding'>) {
  return (
    <Container size='lg' padding='md' className={cn('', className)} {...props}>
      {children}
    </Container>
  );
}

export function FormContainer({
  children,
  className,
  ...props
}: Omit<ContainerProps, 'size' | 'padding'>) {
  return (
    <Container size='md' padding='lg' className={cn('', className)} {...props}>
      {children}
    </Container>
  );
}

export function PageContainer({
  children,
  className,
  ...props
}: Omit<ContainerProps, 'size' | 'padding'>) {
  return (
    <Container
      size='xl'
      padding='md'
      className={cn('min-h-screen', className)}
      {...props}
    >
      {children}
    </Container>
  );
}
