'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  User,
} from 'lucide-react';
import { User as UserType } from '@/types/account';
import { Document } from '@/types/documents';

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedUser: UserType | null;
  onUploadSuccess: (document: Document) => void;
}

export function DocumentUploadModal({
  isOpen,
  onClose,
  selectedUser,
  onUploadSuccess,
}: DocumentUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<string>('');
  const [documentTitle, setDocumentTitle] = useState<string>('');
  const [comments, setComments] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  const handleFileSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          setUploadError('Please select a PDF, JPEG, or PNG file.');
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setUploadError('File size must be less than 10MB.');
          return;
        }

        setSelectedFile(file);
        setUploadError('');

        // Auto-generate title if not set
        if (!documentTitle) {
          const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '');
          setDocumentTitle(nameWithoutExtension);
        }
      }
    },
    [documentTitle]
  );

  const handleUpload = async () => {
    if (!selectedFile || !selectedUser || !documentType || !documentTitle) {
      setUploadError('Please fill in all required fields.');
      return;
    }

    setIsUploading(true);
    setUploadError('');

    try {
      // Simulate upload process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create mock document object
      const newDocument: Document = {
        id: `upload-${Date.now()}`,
        title: documentTitle,
        type: documentType as Document['type'],
        status: 'approved',
        dateCreated: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        version: '1.0',
        userId: selectedUser.id,
        fileUrl: `/documents/${selectedFile.name}`,
        signatureType: 'manual',
        executionDate: new Date().toISOString(),
      };

      onUploadSuccess(newDocument);
      handleClose();
    } catch (error) {
      setUploadError('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setDocumentType('');
    setDocumentTitle('');
    setComments('');
    setUploadError('');
    setIsUploading(false);
    onClose();
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setUploadError('Please select a PDF, JPEG, or PNG file.');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError('File size must be less than 10MB.');
        return;
      }

      setSelectedFile(file);
      setUploadError('');

      // Auto-generate title if not set
      if (!documentTitle) {
        const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, '');
        setDocumentTitle(nameWithoutExtension);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Upload className='h-5 w-5' />
            Upload Signed Document
          </DialogTitle>
          <DialogDescription>
            Upload a signed document for processing and approval.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Selected User Info */}
          {selectedUser && (
            <Card className='bg-blue-50 border-blue-200'>
              <CardContent className='p-4'>
                <div className='flex items-center gap-2'>
                  <User className='h-4 w-4 text-blue-600' />
                  <span className='font-medium text-blue-800'>
                    Uploading for: {selectedUser.name}
                  </span>
                  <Badge variant='outline' className='ml-auto'>
                    {selectedUser.status}
                  </Badge>
                </div>
                <p className='text-sm text-blue-600 mt-1'>
                  {selectedUser.email} • ID: {selectedUser.id}
                </p>
              </CardContent>
            </Card>
          )}

          {/* File Upload Area */}
          <div className='space-y-4'>
            <Label htmlFor='file-upload'>Document File *</Label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                selectedFile
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              {selectedFile ? (
                <div className='flex items-center justify-center gap-2'>
                  <CheckCircle className='h-5 w-5 text-green-600' />
                  <span className='text-green-800 font-medium'>
                    {selectedFile.name}
                  </span>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => setSelectedFile(null)}
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </div>
              ) : (
                <div>
                  <FileText className='h-8 w-8 text-[var(--custom-gray-medium)] mx-auto mb-2' />
                  <p className='text-[var(--custom-gray-medium)] mb-2'>
                    Drag and drop a file here, or click to select
                  </p>
                  <p className='text-sm text-[var(--custom-gray-medium)]'>
                    Supports PDF, JPEG, PNG (max 10MB)
                  </p>
                </div>
              )}
              <input
                id='file-upload'
                type='file'
                accept='.pdf,.jpg,.jpeg,.png'
                onChange={handleFileSelect}
                className='hidden'
              />
              {!selectedFile && (
                <Button
                  variant='outline'
                  className='mt-2'
                  onClick={() =>
                    document.getElementById('file-upload')?.click()
                  }
                >
                  Select File
                </Button>
              )}
            </div>
          </div>

          {/* Document Details */}
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='document-type'>Document Type *</Label>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder='Select document type' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Will'>Will</SelectItem>
                  <SelectItem value='Trust'>Trust</SelectItem>
                  <SelectItem value='POA'>Power of Attorney</SelectItem>
                  <SelectItem value='HealthcareDirective'>
                    Healthcare Directive
                  </SelectItem>
                  <SelectItem value='Other'>Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='document-title'>Document Title *</Label>
              <Input
                id='document-title'
                value={documentTitle}
                onChange={e => setDocumentTitle(e.target.value)}
                placeholder='Enter document title'
              />
            </div>
          </div>

          {/* Comments */}
          <div className='space-y-2'>
            <Label htmlFor='comments'>Comments (Optional)</Label>
            <Textarea
              id='comments'
              value={comments}
              onChange={e => setComments(e.target.value)}
              placeholder='Add any notes about this document...'
              rows={3}
            />
          </div>

          {/* Error Message */}
          {uploadError && (
            <div className='flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg'>
              <AlertCircle className='h-4 w-4 text-red-600' />
              <span className='text-red-800 text-sm'>{uploadError}</span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={handleClose}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={
              !selectedFile || !documentType || !documentTitle || isUploading
            }
          >
            {isUploading ? 'Uploading...' : 'Upload Document'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
