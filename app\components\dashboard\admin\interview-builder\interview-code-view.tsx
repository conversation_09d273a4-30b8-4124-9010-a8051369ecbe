'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Code,
  Copy,
  Download,
  FileText,
  Database,
  Settings,
  Eye,
  CheckCircle,
} from 'lucide-react';
import { Interview, InterviewQuestion } from '@/types/interview-builder';

interface InterviewCodeViewProps {
  interview: Interview;
  questions: InterviewQuestion[];
}

export function InterviewCodeView({
  interview,
  questions,
}: InterviewCodeViewProps) {
  const [activeTab, setActiveTab] = useState('yaml');
  const [copied, setCopied] = useState(false);

  // Get the current version
  const currentVersion = interview.versions.find(
    v => v.version === interview.currentVersion
  );
  if (!currentVersion) {
    return <div>Current version not found</div>;
  }

  const generateYAMLCode = () => {
    const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

    let yaml = `---
# Interview: ${currentVersion.name}
# Description: ${currentVersion.description}
# Generated by Interview Builder

metadata:
  title: ${currentVersion.name}
  short title: ${currentVersion.name}
  description: ${currentVersion.description}
  version: ${currentVersion.version}
  
---
# Interview Questions
`;

    sortedQuestions.forEach((question, index) => {
      yaml += `
question: |
  ${question.text}
`;

      if (question.helpText) {
        yaml += `subquestion: |
  ${question.helpText}
`;
      }

      if (question.type === 'radio' || question.type === 'select') {
        yaml += `field: ${question.templateMapping || `question_${index + 1}`}
choices:
`;
        question.options?.forEach(option => {
          yaml += `  - ${option.label}: ${option.value}
`;
        });
      } else if (question.type === 'checkbox') {
        yaml += `field: ${question.templateMapping || `question_${index + 1}`}
datatype: checkboxes
choices:
`;
        question.options?.forEach(option => {
          yaml += `  - ${option.label}: ${option.value}
`;
        });
      } else {
        yaml += `field: ${question.templateMapping || `question_${index + 1}`}
`;

        if (question.type === 'date') {
          yaml += `datatype: date
`;
        } else if (question.type === 'number') {
          yaml += `datatype: number
`;
        } else if (question.type === 'email') {
          yaml += `datatype: email
`;
        }
      }

      if (question.required) {
        yaml += `required: True
`;
      }

      if (question.validation && question.validation.length > 0) {
        yaml += `validation code: |
`;
        question.validation.forEach(rule => {
          if (rule.type === 'minLength') {
            yaml += `  if len(str(${question.templateMapping || `question_${index + 1}`})) < ${rule.value}:
    validation_error("${rule.message}")
`;
          }
        });
      }

      yaml += `---
`;
    });

    yaml += `
# Final screen
mandatory: True
question: |
  Thank you for completing the interview!
subquestion: |
  Your information has been collected and will be used to generate your documents.
buttons:
  - Exit: exit
`;

    return yaml;
  };

  const generateJSONCode = () => {
    const interviewData = {
      metadata: {
        title: currentVersion.name,
        description: currentVersion.description,
        version: currentVersion.version,
        estimatedDuration: currentVersion.estimatedDuration,
        targetTemplate: currentVersion.targetTemplate,
      },
      questions: questions.map((question, index) => ({
        id: question.id,
        order: question.order,
        text: question.text,
        type: question.type,
        category: question.category,
        required: question.required,
        helpText: question.helpText,
        placeholder: question.placeholder,
        options: question.options,
        validation: question.validation,
        templateMapping: question.templateMapping,
        conditionalLogic: question.conditionalLogic,
      })),
      variables: questions
        .filter(q => q.templateMapping)
        .map(q => ({
          name: q.templateMapping,
          type: getVariableType(q.type),
          source: q.id,
          required: q.required,
        })),
    };

    return JSON.stringify(interviewData, null, 2);
  };

  const generatePythonCode = () => {
    let python = `# Interview: ${currentVersion.name}
# Generated by Interview Builder

from docassemble.base.util import *

# Interview metadata
__all__ = ['interview_logic']

def interview_logic():
    """
    Main interview logic function
    """
    # Initialize variables
    variables = {}
    
`;

    questions.forEach((question, index) => {
      const varName = question.templateMapping || `question_${index + 1}`;

      python += `    # Question ${index + 1}: ${question.text}
    ${varName} = get_user_input(
        question="${question.text}",
        type="${question.type}",
        required=${question.required ? 'True' : 'False'}`;

      if (question.helpText) {
        python += `,
        help_text="${question.helpText}"`;
      }

      if (question.options && question.options.length > 0) {
        python += `,
        options=${JSON.stringify(question.options.map(opt => ({ label: opt.label, value: opt.value })))}`;
      }

      python += `
    )
    variables['${varName}'] = ${varName}
    
`;
    });

    python += `    # Return collected variables
    return variables

# Validation functions
def validate_input(value, validation_rules):
    """
    Validate user input based on rules
    """
    for rule in validation_rules:
        if rule['type'] == 'required' and not value:
            raise ValueError(rule['message'])
        elif rule['type'] == 'minLength' and len(str(value)) < rule['value']:
            raise ValueError(rule['message'])
        elif rule['type'] == 'email' and not is_valid_email(value):
            raise ValueError(rule['message'])
    
    return True

def is_valid_email(email):
    """
    Simple email validation
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None
`;

    return python;
  };

  const getVariableType = (questionType: string) => {
    switch (questionType) {
      case 'number':
        return 'number';
      case 'date':
        return 'date';
      case 'checkbox':
        return 'array';
      case 'radio':
      case 'select':
        return 'choice';
      default:
        return 'text';
    }
  };

  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleDownload = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getActiveContent = () => {
    switch (activeTab) {
      case 'yaml':
        return generateYAMLCode();
      case 'json':
        return generateJSONCode();
      case 'python':
        return generatePythonCode();
      default:
        return '';
    }
  };

  const getActiveFilename = () => {
    const baseName = currentVersion.name.toLowerCase().replace(/\s+/g, '_');
    switch (activeTab) {
      case 'yaml':
        return `${baseName}_interview.yml`;
      case 'json':
        return `${baseName}_interview.json`;
      case 'python':
        return `${baseName}_interview.py`;
      default:
        return `${baseName}_interview.txt`;
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center justify-between'>
            <span className='flex items-center'>
              <Code className='mr-2 h-5 w-5' />
              Generated Interview Code
            </span>
            <div className='flex items-center space-x-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => handleCopy(getActiveContent())}
              >
                {copied ? (
                  <>
                    <CheckCircle className='mr-2 h-4 w-4 text-green-600' />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className='mr-2 h-4 w-4' />
                    Copy
                  </>
                )}
              </Button>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  handleDownload(getActiveContent(), getActiveFilename())
                }
              >
                <Download className='mr-2 h-4 w-4' />
                Download
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            View and export your interview in different formats
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Code Tabs */}
      <Card>
        <CardContent className='p-0'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className='border-b px-6 pt-6'>
              <TabsList className='grid w-full grid-cols-3'>
                <TabsTrigger value='yaml' className='flex items-center'>
                  <FileText className='mr-2 h-4 w-4' />
                  YAML (DocAssemble)
                </TabsTrigger>
                <TabsTrigger value='json' className='flex items-center'>
                  <Database className='mr-2 h-4 w-4' />
                  JSON
                </TabsTrigger>
                <TabsTrigger value='python' className='flex items-center'>
                  <Code className='mr-2 h-4 w-4' />
                  Python
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value='yaml' className='p-6'>
              <div className='space-y-4'>
                <Alert>
                  <FileText className='h-4 w-4' />
                  <AlertDescription>
                    DocAssemble-compatible YAML format. This can be directly
                    imported into DocAssemble.
                  </AlertDescription>
                </Alert>
                <pre className='bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto border'>
                  <code>{generateYAMLCode()}</code>
                </pre>
              </div>
            </TabsContent>

            <TabsContent value='json' className='p-6'>
              <div className='space-y-4'>
                <Alert>
                  <Database className='h-4 w-4' />
                  <AlertDescription>
                    JSON format containing all interview data, questions, and
                    metadata.
                  </AlertDescription>
                </Alert>
                <pre className='bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto border'>
                  <code>{generateJSONCode()}</code>
                </pre>
              </div>
            </TabsContent>

            <TabsContent value='python' className='p-6'>
              <div className='space-y-4'>
                <Alert>
                  <Code className='h-4 w-4' />
                  <AlertDescription>
                    Python code for implementing the interview logic
                    programmatically.
                  </AlertDescription>
                </Alert>
                <pre className='bg-gray-50 p-4 rounded-lg text-sm overflow-x-auto border'>
                  <code>{generatePythonCode()}</code>
                </pre>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle className='text-lg flex items-center'>
            <Settings className='mr-2 h-5 w-5' />
            Export Options
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='p-4 border rounded'>
              <h4 className='font-medium mb-2'>DocAssemble Integration</h4>
              <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                Export as YAML for direct import into DocAssemble platform.
              </p>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  handleDownload(generateYAMLCode(), getActiveFilename())
                }
              >
                Export YAML
              </Button>
            </div>

            <div className='p-4 border rounded'>
              <h4 className='font-medium mb-2'>API Integration</h4>
              <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                JSON format for API consumption and data exchange.
              </p>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  handleDownload(generateJSONCode(), getActiveFilename())
                }
              >
                Export JSON
              </Button>
            </div>

            <div className='p-4 border rounded'>
              <h4 className='font-medium mb-2'>Custom Implementation</h4>
              <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                Python code template for custom interview implementations.
              </p>
              <Button
                variant='outline'
                size='sm'
                onClick={() =>
                  handleDownload(generatePythonCode(), getActiveFilename())
                }
              >
                Export Python
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
