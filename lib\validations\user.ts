import * as z from 'zod';
import { MasterRole } from '@/types/account';

// User roles and subroles
export const masterRoles = [
  'Member',
  'Administrator',
  'WelonTrust',
  'Professional',
] as const;

export const roleSubroles: Record<MasterRole, string[]> = {
  Member: ['Basic Member'],
  Administrator: ['Advanced', 'Basic', 'Reporting', 'Finance'],
  WelonTrust: ['Advanced', 'Basic', 'Emergency Service', 'Medical'],
  Professional: ['Attorney', 'Financial Advisor', 'Healthcare Provider'],
};

export const rolePermissions: Record<MasterRole, string[]> = {
  Member: [
    'View own documents',
    'Edit own documents',
    'Access emergency features',
  ],
  Administrator: [
    'User management',
    'Template management',
    'System settings',
    'Reporting',
    'Billing management',
  ],
  WelonTrust: [
    'View assigned documents',
    'Execute estate plans',
    'Emergency access',
    'Medical directives',
  ],
  Professional: [
    'View client documents',
    'Provide recommendations',
    'Document history',
  ],
};

// Base user form schema
const baseUserSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .trim(),

  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase()
    .trim(),

  role: z.enum(masterRoles, {
    required_error: 'Please select a role',
    invalid_type_error: 'Invalid role selected',
  }),

  subrole: z.string().min(1, 'Subrole is required'),

  permissions: z.array(z.string()),
});

// Extended schema with conditional Welon Trust fields
export const userFormSchema = baseUserSchema
  .extend({
    assignedWelonTrust: z.string().nullable().optional(),

    newWelonTrustEmail: z
      .string()
      .email('Please enter a valid email address')
      .optional()
      .or(z.literal('')),
  })
  .refine(
    data => {
      // If role is Member, assignedWelonTrust is required and cannot be null, empty, or 'none'
      if (data.role === 'Member') {
        return (
          data.assignedWelonTrust &&
          data.assignedWelonTrust !== 'none' &&
          data.assignedWelonTrust.trim() !== ''
        );
      }
      return true;
    },
    {
      message: 'Welon Trust assignment is required for Member role',
      path: ['assignedWelonTrust'],
    }
  )
  .refine(
    data => {
      // If role is Member and assignedWelonTrust is 'invite_new',
      // then newWelonTrustEmail is required
      if (data.role === 'Member' && data.assignedWelonTrust === 'invite_new') {
        return data.newWelonTrustEmail && data.newWelonTrustEmail.length > 0;
      }
      return true;
    },
    {
      message: 'Email is required when inviting new Welon Trust',
      path: ['newWelonTrustEmail'],
    }
  )
  .refine(
    data => {
      // Validate subrole is valid for the selected role
      const validSubroles = roleSubroles[data.role];
      return validSubroles.includes(data.subrole);
    },
    {
      message: 'Invalid subrole for selected role',
      path: ['subrole'],
    }
  );

export type UserFormData = z.infer<typeof userFormSchema>;

// Form props interface
export interface UserFormProps {
  user?: any | null; // Using any to match existing User type
  mode: 'create' | 'edit';
  onSave?: (userData: UserFormData) => void;
  isLoading?: boolean;
}

// Submission data interface
export interface UserSubmissionData extends UserFormData {
  id?: string;
}

// Helper function to get default values
export function getDefaultUserFormValues(
  user?: any,
  mode?: 'create' | 'edit'
): UserFormData {
  if (mode === 'edit' && user) {
    const userRole = user.role || 'Member';
    // If user has no subrole, set default subrole for their role
    let userSubrole = user.subrole;
    if (!userSubrole) {
      const availableSubroles = roleSubroles[userRole as MasterRole] || [];
      userSubrole = availableSubroles.length > 0 ? availableSubroles[0] : '';
    }

    return {
      name: user.name || '',
      email: user.email || '',
      role: userRole,
      subrole: userSubrole,
      permissions: [], // In a real app, we would fetch the user's permissions
      assignedWelonTrust: user.assignedWelonTrust?.welonTrustUserId || null,
      newWelonTrustEmail: '',
    };
  }

  return {
    name: '',
    email: '',
    role: 'Member',
    subrole: 'Basic Member',
    permissions: [],
    assignedWelonTrust: '', // Empty string instead of null for required field
    newWelonTrustEmail: '',
  };
}
