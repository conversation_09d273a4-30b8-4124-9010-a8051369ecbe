'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { DMSConfiguration, DMSStatus } from './types';
import {
  CheckCircle2,
  PauseCircle,
  AlertTriangle,
  Settings,
  PlayCircle,
  Clock,
  TestTube2,
} from 'lucide-react';
import { DMSConfigData } from '@/hooks/useDeadMansSwitch';

interface DMSStatusCardProps {
  config: DMSConfigData | null;
  onCheckIn: () => void;
  onPause: () => void;
  onResume: () => void;
  onTest: () => void;
  onConfigure: () => void;
}

export function DMSStatusCard({
  config,
  onCheckIn,
  onPause,
  onResume,
  onTest,
  onConfigure,
}: DMSStatusCardProps) {
  const getStatusBadge = (status: DMSStatus | null) => {
    switch (status) {
      case 'ACTIVE':
        return (
          <Badge variant='secondary' className='bg-green-2010c'>
            <CheckCircle2 className='mr-1 h-3 w-3' />
            Active
          </Badge>
        );
      case 'PAUSED':
        return (
          <Badge
            variant='outline'
            className='text-blue-2157c border-blue-2157c'
          >
            <PauseCircle className='mr-1 h-3 w-3' />
            Paused
          </Badge>
        );
      case 'DISABLED':
        return (
          <Badge variant='secondary'>
            <AlertTriangle className='mr-1 h-3 w-3' />
            Disabled
          </Badge>
        );
      default:
        return null;
    }
  };

  // Calculate days until next check-in
  const calculateDaysUntilNextCheckIn = () => {
    if (!config?.nextCheckIn) return null;

    const nextCheckIn = new Date(config.nextCheckIn);
    const now = new Date();
    const diffTime = nextCheckIn.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const daysUntilNextCheckIn = calculateDaysUntilNextCheckIn();

  // Calculate progress for the check-in timer
  const calculateProgress = () => {
    if (!config?.lastCheckIn || !config?.nextCheckIn) return 0;

    const lastCheckIn = new Date(config.lastCheckIn);
    const nextCheckIn = new Date(config.nextCheckIn);
    const now = new Date();

    const totalDuration = nextCheckIn.getTime() - lastCheckIn.getTime();
    const elapsed = now.getTime() - lastCheckIn.getTime();

    const progress = Math.min(
      100,
      Math.max(0, (elapsed / totalDuration) * 100)
    );
    return progress;
  };

  const checkInProgress = calculateProgress();

  // Format the frequency for display
  const formatFrequency = () => {
    if (config?.frequency === 'CUSTOM' && config?.customFrequencyDays) {
      return `Every ${config.customFrequencyDays} days`;
    }
    return config?.frequency;
  };

  return (
    <Card>
      <CardHeader className='pb-2'>
        <div className='flex justify-between items-center'>
          <CardTitle className='text-xl'>Dead Man's Switch Status</CardTitle>
          {getStatusBadge(config?.status || 'DISABLED')}
        </div>
      </CardHeader>
      <CardContent className='space-y-4'>
        {config?.status === 'ACTIVE' && (
          <>
            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Next check-in</span>
                <span className='font-medium'>
                  {daysUntilNextCheckIn === 1
                    ? 'Tomorrow'
                    : daysUntilNextCheckIn === 0
                      ? 'Today'
                      : `In ${daysUntilNextCheckIn} days`}
                </span>
              </div>
              <Progress value={checkInProgress} className='h-2' />
            </div>

            <div className='grid grid-cols-2 gap-4 pt-2'>
              <div>
                <p className='text-sm text-muted-foreground'>Frequency</p>
                <p className='font-medium'>{formatFrequency()}</p>
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>Communication</p>
                <p className='font-medium'>{config?.communicationMethod}</p>
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>Escalation</p>
                <p className='font-medium'>{config?.escalationProtocol}</p>
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>Last check-in</p>
                <p className='font-medium'>
                  {config?.lastCheckIn
                    ? new Date(config?.lastCheckIn).toLocaleDateString(
                        'en-US',
                        {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                        }
                      )
                    : 'Never'}
                </p>
              </div>
            </div>
          </>
        )}

        {config?.status === 'PAUSED' && (
          <div className='space-y-4'>
            <div className='bg-muted p-4 rounded-md'>
              <h3 className='font-medium flex items-center'>
                <Clock className='mr-2 h-4 w-4' />
                Paused until{' '}
                {config.pauseUntil
                  ? new Date(config.pauseUntil).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                  : 'further notice'}
              </h3>
              {config.pauseReason && (
                <p className='text-sm text-muted-foreground mt-2'>
                  Reason: {config.pauseReason}
                </p>
              )}
            </div>

            <div className='grid grid-cols-2 gap-4'>
              <div>
                <p className='text-sm text-muted-foreground'>Frequency</p>
                <p className='font-medium'>{formatFrequency()}</p>
              </div>
              <div>
                <p className='text-sm text-muted-foreground'>Communication</p>
                <p className='font-medium'>{config.communicationMethod}</p>
              </div>
            </div>
          </div>
        )}

        {config?.status === 'DISABLED' && (
          <div className='text-center py-4'>
            <AlertTriangle className='mx-auto h-12 w-12 text-muted-foreground mb-2' />
            <h3 className='font-medium text-lg'>
              Dead Man's Switch is disabled
            </h3>
            <p className='text-sm text-muted-foreground mt-1'>
              Configure and enable the switch to set up automated safety checks.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className='flex flex-wrap gap-2'>
        {config?.status === 'ACTIVE' && (
          <>
            <Button onClick={onCheckIn}>
              <CheckCircle2 className='mr-2 h-4 w-4' />
              Confirm I'm Okay
            </Button>
            {/* <Button variant='outline' onClick={onPause} className='flex-1'>
              <PauseCircle className='mr-2 h-4 w-4' />
              Pause Switch
            </Button> */}
          </>
        )}

        {/* {config?.status === 'PAUSED' && (
          <Button onClick={onResume}>
            <PlayCircle className='mr-2 h-4 w-4' />
            Resume Switch
          </Button>
        )} */}

        <Button variant='outline' onClick={onConfigure} className='flex-1'>
          <Settings className='mr-2 h-4 w-4' />
          Configure
        </Button>

        {/* {(config?.status === 'ACTIVE' || config?.status === 'PAUSED') && (
          <Button variant='outline' onClick={onTest} className='flex-1'>
            <TestTube2 className='mr-2 h-4 w-4' />
            Test Switch
          </Button>
        )} */}
      </CardFooter>
    </Card>
  );
}
