/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Both app and pages directories are supported at the same time
  // We'll keep the pages directory temporarily during migration

  // Disable ESLint during builds
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
