'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  getInterviewQuestions,
  getUserInterviewProgress,
  saveInterviewAnswer,
  resetUserInterviewProgress,
} from '@/utils/userInterviewProgress';
import type { Schema } from '@/amplify/data/resource';

// Use the real question type from the schema
export type Question = Schema['InterviewQuestion']['type'];

export interface Response {
  questionId: string;
  value: any;
  timestamp: number;
}

interface InterviewContextType {
  questions: Question[];
  responses: Record<string, Response>;
  currentQuestionIndex: number;
  progress: number;
  userProgress: Schema['UserInterviewProgress']['type'] | null;
  isLoading: boolean;
  setResponse: (questionId: string, value: any) => void;
  goToNextQuestion: () => void;
  goToPreviousQuestion: () => void;
  goToQuestion: (questionId: string) => void;
  resetInterview: () => Promise<void>;
  isComplete: boolean;
}

const InterviewContext = createContext<InterviewContextType | undefined>(
  undefined
);

export const InterviewProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [responses, setResponses] = useState<Record<string, Response>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userProgress, setUserProgress] = useState<
    Schema['UserInterviewProgress']['type'] | null
  >(null);
  const [isComplete, setIsComplete] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load real data from database
  useEffect(() => {
    async function loadData() {
      try {
        setIsLoading(true);

        // Fetch questions and user progress in parallel
        const [fetchedQuestions, progress] = await Promise.all([
          getInterviewQuestions(),
          getUserInterviewProgress(),
        ]);

        setQuestions(fetchedQuestions);
        setUserProgress(progress);

        // Parse answers from database
        let parsedAnswers = {};
        if (progress.answers && typeof progress.answers === 'object') {
          parsedAnswers = progress.answers;
        } else if (typeof progress.answers === 'string') {
          try {
            parsedAnswers = JSON.parse(progress.answers);
          } catch (e) {
            console.error('Error parsing user progress answers:', e);
          }
        }

        // Convert database answers to response format
        const databaseResponses: Record<string, Response> = {};
        Object.entries(parsedAnswers).forEach(([questionId, value]) => {
          databaseResponses[questionId] = {
            questionId,
            value,
            timestamp: Date.now(),
          };
        });

        setResponses(databaseResponses);

        // Set current question index based on progress
        if (progress.currentQuestionId && fetchedQuestions.length > 0) {
          const index = fetchedQuestions.findIndex(
            q => q.id === progress.currentQuestionId
          );
          if (index !== -1) {
            setCurrentQuestionIndex(index + 1);
          }
        }

        setIsComplete(progress.isComplete || false);
      } catch (error) {
        console.error('Error loading interview data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadData();
  }, []);

  // Calculate progress
  const progress =
    questions.length > 0 ? (currentQuestionIndex / questions.length) * 100 : 0;

  // Set a response for a question and save to database
  const setResponse = async (questionId: string, value: any) => {
    const newResponse = {
      questionId,
      value,
      timestamp: Date.now(),
    };

    setResponses(prev => ({
      ...prev,
      [questionId]: newResponse,
    }));

    try {
      // Determine if this is the last question
      const currentIndex = questions.findIndex(q => q.id === questionId);
      const isLastQuestion = currentIndex === questions.length - 1;

      // Save to database
      await saveInterviewAnswer(questionId, value, isLastQuestion);

      // Update user progress state
      const updatedProgress = await getUserInterviewProgress();
      setUserProgress(updatedProgress);

      if (isLastQuestion) {
        setIsComplete(true);
      }
    } catch (error) {
      console.error('Error saving answer to database:', error);
    }
  };

  // Navigate to the next question
  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      // End of interview
      setIsComplete(true);
    }
  };

  // Navigate to the previous question
  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Go to a specific question by ID
  const goToQuestion = (questionId: string) => {
    const questionIndex = questions.findIndex(q => q.id === questionId);
    if (questionIndex !== -1) {
      setCurrentQuestionIndex(questionIndex);
    }
  };

  // Reset the interview and clear all database data
  const resetInterview = async () => {
    try {
      // Reset database progress first
      const resetProgress = await resetUserInterviewProgress();

      // Update local state with the reset progress
      setUserProgress(resetProgress);
      setResponses({});
      setCurrentQuestionIndex(0);
      setIsComplete(false);

      console.log('Interview successfully reset');
    } catch (error) {
      console.error('Error resetting interview:', error);
      throw error; // Re-throw to allow calling components to handle the error
    }
  };

  return (
    <InterviewContext.Provider
      value={{
        questions,
        responses,
        currentQuestionIndex,
        progress,
        userProgress,
        isLoading,
        setResponse,
        goToNextQuestion,
        goToPreviousQuestion,
        goToQuestion,
        resetInterview,
        isComplete,
      }}
    >
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = () => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
