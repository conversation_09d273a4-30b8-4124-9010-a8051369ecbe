'use client';

import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';

export default function MemberLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole='Member' />
      <div className='flex-1'>
        <main>
          <AdminContainer>{children}</AdminContainer>
        </main>
      </div>
    </div>
  );
}
