import { defineAuth } from '@aws-amplify/backend';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { addUserToGroup } from '../functions/addUserToGroup/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';
import { addUserToGroupV2 } from '../functions/addUserToGroupV2/resource';

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
  },
  groups: ['ADMINS', 'WELONTRUST'],
  triggers: {
    postConfirmation: postSignUp,
  },
  access: allow => [
    allow.resource(activateDeactivateUser).to(['manageUsers', 'listUsers']),
    allow
      .resource(addUserToGroup)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
    allow
      .resource(addUserToGroupV2)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
    allow.resource(checkEmail).to(['manageUsers', 'listUsers']),
    allow.resource(trackLoginAttempt).to(['manageUsers', 'listUsers']),
    allow.resource(generateVerificationToken).to(['manageUsers', 'listUsers']),
    allow
      .resource(verifyEmailToken)
      .to(['manageUsers', 'listUsers', 'managePasswordRecovery']),
  ],
});

/**
 * You can replace the code above with the code below and it will use the external, preconfigured version
 * of Cognito.  I can provide a local copy of the ENV values that will connect you as well, but I couldn't
 * get this base code to pull them and needed to finish the SQL server
 *
 */

// import { defineAuth } from '@aws-amplify/backend';

// export const auth = referenceAuth({
// userPoolId: process.env.REACT_APP_CFLegacy_Dev_UserPoolID,
// identityPoolId: process.env.REACT_APP_CFLegacy_Dev_IdentityPoolID,
// authRoleArn: process.env.REACT_APP_CFLegacy_Dev_authRoleARN,
// unauthRoleArn: process.env.REACT_APP_CFLegacy_Dev_unauthRoleARN,
// userPoolClientId: process.env.REACT_APP_CFLegacy_Dev_userPoolClientID,
// });
