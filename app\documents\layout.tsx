'use client';

import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '@/components/ui/container';
import { useRole } from '@/lib/roles/role-context';
import {
  UserProvider,
  useUserContext,
} from '@/components/welon-trust/user-context';
import { UserSelector } from '@/components/welon-trust/user-selector';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/context/AuthContext';
import { AuthGuard } from '@/lib/auth/auth-guard';

interface DocumentsLayoutProps {
  children: React.ReactNode;
}

function DocumentsLayoutContent({ children }: DocumentsLayoutProps) {
  const { userRoles } = useAuth();

  const currentUserRole = userRoles[0] ?? 'member';

  // Determine if this is a Welon Trust user who needs user selection
  const isWelonTrust = currentUserRole === 'WELONTRUST';

  // Map role context to sidebar role format
  const getSidebarRole = ():
    | 'Member'
    | 'Administrator'
    | 'Welon Trust'
    | 'Professional' => {
    if (!currentUserRole) return 'Member';

    switch (currentUserRole) {
      case 'ADMINS':
        return 'Administrator';
      case 'WELONTRUST':
        return 'Welon Trust';
      case 'linked_account':
        return 'Member'; // Linked accounts use member-like interface but with limited nav
      default:
        return 'Member';
    }
  };

  const userRole = getSidebarRole();

  // For Welon Trust users, wrap with UserProvider and show UserSelector
  if (isWelonTrust) {
    return (
      <UserProvider>
        <WelonTrustDocumentsLayout userRole={userRole}>
          {children}
        </WelonTrustDocumentsLayout>
      </UserProvider>
    );
  }

  // For regular members, show normal layout
  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole={userRole} />
      <div className='flex-1'>
        <main>
          <AdminContainer>{children}</AdminContainer>
        </main>
      </div>
    </div>
  );
}

function WelonTrustDocumentsLayout({
  children,
  userRole,
}: {
  children: React.ReactNode;
  userRole: 'Member' | 'Administrator' | 'Welon Trust' | 'Professional';
}) {
  const { selectedUser, setSelectedUser, availableUsers } = useUserContext();

  return (
    <div className='min-h-screen flex bg-background'>
      <Sidebar userRole={userRole} />
      <div className='flex-1'>
        <main>
          <AdminContainer>
            {/* User Selection Header for Welon Trust */}
            <Card className='mb-6 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50'>
              <CardContent className='p-6'>
                <div className='max-w-md'>
                  <UserSelector
                    selectedUser={selectedUser}
                    onUserSelect={setSelectedUser}
                    users={availableUsers}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Main Content */}
            {children}
          </AdminContainer>
        </main>
      </div>
    </div>
  );
}

export default function DocumentsLayout({ children }: DocumentsLayoutProps) {
  return (
    <AuthGuard>
      <DocumentsLayoutContent>{children}</DocumentsLayoutContent>
    </AuthGuard>
  );
}
