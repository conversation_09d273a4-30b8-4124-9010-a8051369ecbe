'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import routes from '@/utils/routes';

function VerifyEmailContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = searchParams.get('token');
  const email = searchParams.get('email');
  const successParam = searchParams.get('success');
  const errorParam = searchParams.get('error');

  useEffect(() => {
    // Check if we have success/error from query params (from API route redirect)
    if (successParam === 'true') {
      setSuccess(true);
      setLoading(false);
      return;
    }

    if (errorParam) {
      setError(decodeURIComponent(errorParam));
      setLoading(false);
      return;
    }

    // Otherwise, verify the token directly
    const verifyEmail = async () => {
      if (!token || !email) {
        setError('Invalid verification link. Missing token or email.');
        setLoading(false);
        return;
      }

      try {
        const client = generateClient<Schema>({
          authMode: 'iam',
        });

        const result = await client.mutations.verifyEmailToken({
          email: decodeURIComponent(email),
          token,
          verificationType: 'accountConfirmation',
        });

        const resultData = result.data as any;

        // Try to parse if it's a string
        let parsedResultData = resultData;
        if (typeof resultData === 'string') {
          try {
            parsedResultData = JSON.parse(resultData);
            console.log('Parsed result data:', parsedResultData);
          } catch (e) {
            console.log('Failed to parse result data as JSON:', e);
          }
        }

        if (parsedResultData?.success) {
          setSuccess(true);
        } else {
          setError(parsedResultData?.error || 'Failed to verify email');
        }
      } catch (err) {
        console.error('Verification error:', err);
        setError('An error occurred while verifying your email');
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [token, email, successParam, errorParam]);

  const handleContinue = () => {
    router.push(routes.login);
  };

  const handleResendVerification = () => {
    // You could implement a resend verification function here
    router.push(routes.login);
  };

  if (loading) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <Card className='w-full max-w-md'>
          <CardContent className='flex flex-col items-center justify-center p-6'>
            <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
            <p className='text-center text-muted-foreground'>
              Verifying your email address...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50'>
      <Card className='w-full max-w-md'>
        <CardHeader className='text-center'>
          <div className='flex justify-center mb-4'>
            {success ? (
              <CheckCircle className='h-12 w-12 text-green-500' />
            ) : (
              <XCircle className='h-12 w-12 text-red-500' />
            )}
          </div>
          <CardTitle>
            {success ? 'Email Verified!' : 'Verification Failed'}
          </CardTitle>
          <CardDescription>
            {success
              ? 'Your email has been successfully verified. You can now sign in to your account.'
              : error || 'We were unable to verify your email address.'}
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          {success ? (
            <Button onClick={handleContinue} className='w-full'>
              Continue to Sign In
            </Button>
          ) : (
            <div className='space-y-2'>
              <Button onClick={handleResendVerification} className='w-full'>
                Back to Sign In
              </Button>
              <p className='text-xs text-center text-muted-foreground'>
                If you continue to have issues, please contact support.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-gray-50'>
          <Card className='w-full max-w-md'>
            <CardContent className='flex flex-col items-center justify-center p-6'>
              <Loader2 className='h-8 w-8 animate-spin text-primary mb-4' />
              <p className='text-center text-muted-foreground'>Loading...</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <VerifyEmailContent />
    </Suspense>
  );
}
