# Universal Data Table Components

A comprehensive, reusable data table system with advanced filtering, sorting, pagination, and URL state management.

## Features

- ✅ **URL State Management** - All table state persists in URL parameters
- ✅ **Advanced Filtering** - Search and faceted filters with real-time updates
- ✅ **Sorting** - Click column headers to sort data
- ✅ **Pagination** - Configurable page sizes with navigation controls
- ✅ **Column Visibility** - Show/hide columns dynamically
- ✅ **Row Selection** - Optional multi-row selection
- ✅ **Responsive Design** - Works on all screen sizes
- ✅ **TypeScript Support** - Fully typed for better DX

## Quick Start

```tsx
import {
  DataTable,
  DataTableConfig,
  DataTableColumnHeader,
} from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';

// Define your data type
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
}

// Configure the table
const tableConfig: DataTableConfig = {
  searchColumn: 'name',
  searchPlaceholder: 'Search users...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  defaultPageSize: 10,
};

// Define columns
const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Name' />
    ),
    cell: ({ row }) => <span>{row.getValue('name')}</span>,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => <span>{row.getValue('status')}</span>,
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
];

// Use the table
function MyTable({ data }: { data: User[] }) {
  return <DataTable columns={columns} data={data} config={tableConfig} />;
}
```

## Configuration Options

### DataTableConfig

```tsx
interface DataTableConfig {
  searchColumn?: string; // Column to search (default: 'name')
  searchPlaceholder?: string; // Search input placeholder
  filters?: DataTableFilterConfig[]; // Faceted filter configurations
  enableColumnVisibility?: boolean; // Show column visibility toggle
  enablePagination?: boolean; // Enable pagination
  enableRowSelection?: boolean; // Enable row selection
  defaultPageSize?: number; // Default page size (default: 10)
}
```

### Filter Configuration

```tsx
interface DataTableFilterConfig {
  id: string; // Column ID to filter
  title: string; // Filter display title
  options: {
    label: string; // Option display label
    value: string; // Option value
    icon?: React.ComponentType; // Optional icon component
  }[];
}
```

## URL Parameters

The table automatically manages these URL parameters:

- `search` - Search query
- `sort` - Sort column
- `order` - Sort direction (asc/desc)
- `page` - Current page number
- `pageSize` - Items per page
- `hidden` - Hidden column names (comma-separated)
- `[filterId]` - Filter values (comma-separated)

Example URL:

```
/users?search=john&status=active,pending&sort=name&order=asc&page=2&pageSize=20
```

## Advanced Usage

### Custom Toolbar

```tsx
import { DataTableToolbar } from '@/components/ui/data-table';

function CustomToolbar({ table }) {
  return (
    <div className='flex items-center justify-between'>
      <DataTableToolbar table={table} config={tableConfig} />
      <Button>Custom Action</Button>
    </div>
  );
}

<DataTable
  columns={columns}
  data={data}
  config={tableConfig}
  toolbar={CustomToolbar}
/>;
```

### Custom Pagination

```tsx
import { DataTablePagination } from '@/components/ui/data-table';

function CustomPagination({ table }) {
  return (
    <DataTablePagination
      table={table}
      pageSizeOptions={[5, 10, 25, 50]}
      showRowSelection={false}
    />
  );
}

<DataTable
  columns={columns}
  data={data}
  config={tableConfig}
  pagination={CustomPagination}
/>;
```

### Column Definitions

#### Sortable Column

```tsx
{
  accessorKey: 'name',
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="Name" />
  ),
  cell: ({ row }) => <span>{row.getValue('name')}</span>,
}
```

#### Filterable Column

```tsx
{
  accessorKey: 'status',
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title="Status" />
  ),
  cell: ({ row }) => <Badge>{row.getValue('status')}</Badge>,
  filterFn: (row, id, value) => {
    return value.includes(row.getValue(id));
  },
}
```

#### Actions Column

```tsx
{
  id: 'actions',
  header: 'Actions',
  cell: ({ row }) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem onClick={() => handleEdit(row.original)}>
          Edit
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  ),
}
```

## Components

- `DataTable` - Main table component
- `DataTableToolbar` - Search and filter toolbar
- `DataTableFacetedFilter` - Multi-select filter dropdown
- `DataTableViewOptions` - Column visibility toggle
- `DataTablePagination` - Pagination controls
- `DataTableColumnHeader` - Sortable column header

## Dependencies

- `@tanstack/react-table` - Table logic
- `next/navigation` - URL management
- `lucide-react` - Icons
- `@radix-ui/*` - UI primitives

## Examples

See `user-management-table-new.tsx` for a complete implementation example.
