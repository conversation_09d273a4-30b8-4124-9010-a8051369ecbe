'use client';

import { useAuth } from '@/context/AuthContext';
import { useMemo } from 'react';

export interface AdminAccessInfo {
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isWelonTrust: boolean;
  hasAnyAdminRole: boolean;
  userRoles: string[];
  canManageUsers: boolean;
  canManageRoles: boolean;
  canManageTemplates: boolean;
  canViewReports: boolean;
  canManageBilling: boolean;
  canAccessEmergency: boolean;
}

/**
 * Custom hook to check admin access and permissions
 * @returns AdminAccessInfo object with various admin access flags
 */
export function useAdminAccess(): AdminAccessInfo {
  const { userRoles } = useAuth();

  const adminAccess = useMemo(() => {
    const isAdmin = userRoles.includes('ADMINS');
    const isWelonTrust = userRoles.includes('WELONTRUST');
    const hasAnyAdminRole = isAdmin || isWelonTrust;

    // Super admin has full access (for future use)
    const isSuperAdmin = isAdmin; // Can be extended later for specific super admin roles

    // Define permissions based on roles
    const canManageUsers = isAdmin;
    const canManageRoles = isAdmin;
    const canManageTemplates = isAdmin;
    const canViewReports = hasAnyAdminRole;
    const canManageBilling = isAdmin;
    const canAccessEmergency = hasAnyAdminRole;

    return {
      isAdmin,
      isSuperAdmin,
      isWelonTrust,
      hasAnyAdminRole,
      userRoles,
      canManageUsers,
      canManageRoles,
      canManageTemplates,
      canViewReports,
      canManageBilling,
      canAccessEmergency,
    };
  }, [userRoles]);

  return adminAccess;
}

/**
 * Hook to check if user has specific admin role
 * @param requiredRole - The role to check for
 * @returns boolean indicating if user has the role
 */
export function useHasAdminRole(requiredRole: string): boolean {
  const { userRoles } = useAuth();
  return userRoles.includes(requiredRole);
}

/**
 * Hook to check if user has any of the specified admin roles
 * @param roles - Array of roles to check for
 * @returns boolean indicating if user has any of the roles
 */
export function useHasAnyAdminRole(roles: string[]): boolean {
  const { userRoles } = useAuth();
  return roles.some(role => userRoles.includes(role));
}

/**
 * Hook to check if user has all of the specified admin roles
 * @param roles - Array of roles to check for
 * @returns boolean indicating if user has all of the roles
 */
export function useHasAllAdminRoles(roles: string[]): boolean {
  const { userRoles } = useAuth();
  return roles.every(role => userRoles.includes(role));
}

/**
 * Hook to get admin-specific navigation items based on user roles
 * @returns Array of navigation items the user can access
 */
export function useAdminNavigation() {
  const { isAdmin, isWelonTrust, canManageUsers, canManageTemplates } =
    useAdminAccess();

  return useMemo(() => {
    const navItems = [];

    if (isAdmin || isWelonTrust) {
      navItems.push({
        title: 'Dashboard',
        href: '/admin',
        permission: 'dashboard',
      });
    }

    if (canManageUsers) {
      navItems.push({
        title: 'User Management',
        href: '/admin/users',
        permission: 'manage_users',
      });
    }

    if (isAdmin) {
      navItems.push({
        title: 'Role Management',
        href: '/admin/roles',
        permission: 'manage_roles',
      });
    }

    if (canManageTemplates) {
      navItems.push({
        title: 'Template Management',
        href: '/admin/templates',
        permission: 'manage_templates',
      });
    }

    if (isAdmin || isWelonTrust) {
      navItems.push({
        title: 'Reports',
        href: '/admin/reports',
        permission: 'view_reports',
      });
    }

    if (isWelonTrust) {
      navItems.push({
        title: 'Emergency Access',
        href: '/admin/emergency',
        permission: 'access_emergency',
      });
    }

    if (isAdmin) {
      navItems.push({
        title: 'Billing',
        href: '/admin/billing',
        permission: 'manage_billing',
      });
    }

    return navItems;
  }, [isAdmin, isWelonTrust, canManageUsers, canManageTemplates]);
}
