export interface Document {
  id: string;
  title: string;
  type: 'Will' | 'Trust' | 'POA' | 'HealthcareDirective' | 'Other';
  status:
    | 'draft'
    | 'ready_for_review'
    | 'under_review'
    | 'ready_for_signing'
    | 'signed'
    | 'shipped'
    | 'received'
    | 'approved'
    | 'rejected';
  dateCreated: string;
  lastModified: string;
  version: string;
  content?: string; // Populated template content
  userId: string;
  fileUrl?: string;
  signatureType?: 'electronic' | 'manual';
  notarizationRequired?: boolean;
  executionDate?: string;
  trackingNumber?: string;
  rejectionReason?: string;
}

export interface DocumentReviewData {
  documentId: string;
  reviewerId: string;
  reviewDate: string;
  comments?: string;
  approved: boolean;
}

export interface AttorneyReview {
  id: string;
  documentId: string;
  attorneyId: string;
  attorneyName: string;
  attorneyPhone: string;
  attorneyEmail: string;
  status: 'requested' | 'in_progress' | 'completed';
  requestDate: string;
  completionDate?: string;
  comments?: string;
}

export interface DocumentStatus {
  id: string;
  documentId: string;
  memberId: string;
  status: Document['status'];
  trackingLink?: string;
  receiptDate?: string;
  approvalDate?: string;
  updatedAt: string;
  statusMessage?: string;
}

export interface SigningPackage {
  id: string;
  documentId: string;
  packageUrl: string;
  instructions: string;
  shippingLabel: string;
  generatedAt: string;
  expiresAt: string;
}
