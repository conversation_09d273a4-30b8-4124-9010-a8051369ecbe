'use client';

import { useState, useEffect } from 'react';
import type { Schema } from '@/amplify/data/resource';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface InterviewQuestionProps {
  question: Schema['InterviewQuestion']['type'];
  onSubmit: (answer: string | string[] | boolean | number) => void;
  isSubmitting: boolean;
  savedAnswer?: string | string[] | boolean | number;
}

export function InterviewQuestion({
  question,
  onSubmit,
  isSubmitting,
  savedAnswer,
}: InterviewQuestionProps) {
  const [answer, setAnswer] = useState<string | string[] | boolean | number>(
    ''
  );
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  // Parse options if they exist
  const options = (() => {
    if (!question.options) return [];

    if (typeof question.options === 'string') {
      try {
        return JSON.parse(question.options);
      } catch (e) {
        console.error('Error parsing options:', e);
        return [];
      }
    }

    // If options is already an object (not a string), return it directly
    if (typeof question.options === 'object') {
      return question.options;
    }

    // Fallback to empty array
    return [];
  })();

  // Initialize answer from saved value if it exists
  useEffect(() => {
    if (savedAnswer !== undefined) {
      setAnswer(savedAnswer);

      // For checkbox type, initialize selected options
      if (question.type === 'checkbox' && Array.isArray(savedAnswer)) {
        setSelectedOptions(savedAnswer);
      }
    }
  }, [savedAnswer, question.type]);

  // Handle checkbox changes
  const handleCheckboxChange = (value: string) => {
    setSelectedOptions(prev => {
      const isSelected = prev.includes(value);

      if (isSelected) {
        // Remove the value
        const newSelected = prev.filter(item => item !== value);
        setAnswer(newSelected);
        return newSelected;
      } else {
        // Add the value
        const newSelected = [...prev, value];
        setAnswer(newSelected);
        return newSelected;
      }
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(answer);
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='space-y-2'>
        <h3 className='text-xl font-medium'>{question.questionTitle}</h3>
        {question.questionDescription && (
          <p className='text-muted-foreground'>
            {question.questionDescription}
          </p>
        )}
      </div>

      <div className='space-y-4'>
        {question.type === 'text' && (
          <Input
            value={answer as string}
            onChange={e => setAnswer(e.target.value)}
            placeholder='Type your answer here'
            disabled={isSubmitting}
          />
        )}

        {question.type === 'textarea' && (
          <Textarea
            value={answer as string}
            onChange={e => setAnswer(e.target.value)}
            placeholder='Type your answer here'
            rows={4}
            disabled={isSubmitting}
          />
        )}

        {question.type === 'select' && (
          <Select
            value={answer as string}
            onValueChange={setAnswer}
            disabled={isSubmitting}
          >
            <SelectTrigger>
              <SelectValue placeholder='Select an option' />
            </SelectTrigger>
            <SelectContent>
              {options.map((option: { value: string; label: string }) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {question.type === 'radio' && (
          <RadioGroup
            value={answer as string}
            onValueChange={setAnswer}
            className='space-y-3'
            disabled={isSubmitting}
          >
            {options.map((option: string) => (
              <div
                key={option}
                className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${
                  answer === option
                    ? 'bg-accent border-primary'
                    : 'hover:bg-accent/50'
                }`}
              >
                <RadioGroupItem value={option} id={option} />
                <Label htmlFor={option} className='flex-grow cursor-pointer'>
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )}

        {question.type === 'checkbox' && (
          <div className='space-y-3'>
            {options.map((option: { value: string; label: string }) => (
              <div
                key={option.value}
                className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${
                  selectedOptions.includes(option.value)
                    ? 'bg-accent border-primary'
                    : 'hover:bg-accent/50'
                }`}
              >
                <Checkbox
                  id={option.value}
                  checked={selectedOptions.includes(option.value)}
                  onCheckedChange={() => handleCheckboxChange(option.value)}
                  disabled={isSubmitting}
                />
                <Label
                  htmlFor={option.value}
                  className='flex-grow cursor-pointer'
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        )}
      </div>

      <Button
        type='submit'
        disabled={
          isSubmitting ||
          (question.type === 'text' && !(answer as string).trim()) ||
          (question.type === 'textarea' && !(answer as string).trim()) ||
          (question.type === 'select' && !(answer as string)) ||
          (question.type === 'radio' && !(answer as string)) ||
          (question.type === 'checkbox' &&
            (!Array.isArray(answer) || answer.length === 0))
        }
        className='w-full'
      >
        {isSubmitting ? 'Saving...' : 'Save Answer'}
      </Button>
    </form>
  );
}
