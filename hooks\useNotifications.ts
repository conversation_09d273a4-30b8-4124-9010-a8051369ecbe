import { useState, useEffect, useCallback } from 'react';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  getNotifications,
  markAsRead,
  subscribeToNotifications,
  deleteAllNotifications,
  type NotificationData
} from '@/lib/api/notifications';

export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [loading, setLoading] = useState(true);

  // Calculate unread count from notifications array
  const unreadCount = notifications.filter(n => !n.isRead).length;

  const fetchNotifications = useCallback(async () => {
    try {
      const user = await getCurrentUser();
      const data = await getNotifications(user.userId);
      setNotifications(data);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleMarkAsRead = useCallback(async (id: string) => {
    try {
      await markAsRead(id);
      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, isRead: true } : n)
      );
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  }, []);

  const handleDeleteAll = useCallback(async () => {
    try {
      await deleteAllNotifications();
      setNotifications([]);
    } catch (error) {
      console.error('Error deleting all notifications:', error);
    }
  }, []);

  useEffect(() => {
    let subscription: any;

    const setupSubscription = async () => {
      try {
        const user = await getCurrentUser();
        subscription = subscribeToNotifications(user.userId, (notification) => {
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(n => n.id === notification.id);
            if (exists) {
              return prev;
            }
            // Add new notification to the beginning
            return [notification, ...prev];
          });
        });
      } catch (error) {
        console.error('Error setting up subscription:', error);
      }
    };

    fetchNotifications();
    setupSubscription();

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead: handleMarkAsRead,
    deleteAll: handleDeleteAll,
    refetch: fetchNotifications,
  };
}
