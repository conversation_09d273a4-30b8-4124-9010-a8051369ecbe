'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Edit, ArrowLeft, Download, Clock } from 'lucide-react';
import { LivingDocument } from './living-document-card';

interface LivingDocumentViewerProps {
  document: LivingDocument & {
    content: string;
    createdAt: string;
    reviewFrequency: string;
  };
}

export function LivingDocumentViewer({ document }: LivingDocumentViewerProps) {
  const router = useRouter();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-green-2010c';
      case 'review-soon':
        return 'bg-amber-400 text-amber-900';
      case 'review-needed':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'current':
        return 'Current';
      case 'review-soon':
        return 'Review Soon';
      case 'review-needed':
        return 'Review Needed';
      default:
        return 'Unknown';
    }
  };

  const handleEdit = () => {
    router.push(`/dashboard/member/living-documents/update/${document.id}`);
  };

  const handleBack = () => {
    router.push('/dashboard/member/living-documents');
  };

  const handleDownload = () => {
    // In a real implementation, this would download the document
    alert('Downloading document...');
  };

  return (
    <div>
      <Button variant='outline' className='mb-6' onClick={handleBack}>
        <ArrowLeft className='h-4 w-4 mr-2' />
        Back to Documents
      </Button>

      <Card>
        <CardHeader>
          <div className='flex justify-between items-start'>
            <div>
              <CardTitle className='text-2xl'>{document.title}</CardTitle>
              <CardDescription>{document.description}</CardDescription>
            </div>
            <Badge className={getStatusColor(document.status)}>
              {getStatusText(document.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-6'>
            <div className='flex items-center'>
              <Calendar className='h-4 w-4 mr-2 text-[var(--custom-gray-medium)]' />
              <span className='text-sm'>Created: {document.createdAt}</span>
            </div>
            <div className='flex items-center'>
              <Calendar className='h-4 w-4 mr-2 text-[var(--custom-gray-medium)]' />
              <span className='text-sm'>
                Last updated: {document.lastUpdated}
              </span>
            </div>
            <div className='flex items-center'>
              <Clock className='h-4 w-4 mr-2 text-[var(--custom-gray-medium)]' />
              <span className='text-sm'>
                Review frequency: {document.reviewFrequency}
              </span>
            </div>
          </div>

          <div className='border rounded-lg p-6 bg-background'>
            <h3 className='text-lg font-medium mb-4'>Document Content</h3>
            <div className='whitespace-pre-wrap'>{document.content}</div>
          </div>
        </CardContent>
        <CardFooter className='flex justify-between'>
          <Button variant='outline' onClick={handleDownload}>
            <Download className='h-4 w-4 mr-2' />
            Download
          </Button>
          <Button onClick={handleEdit}>
            <Edit className='h-4 w-4 mr-2' />
            Update Document
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
