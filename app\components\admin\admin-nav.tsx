'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '../../../lib/utils';
import {
  Users,
  FileText,
  AlertTriangle,
  BarChart3,
  CreditCard,
  Settings,
  Home,
  Menu,
  X,
  BookOpen,
  Shield,
  FileCheck,
} from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import { Logo } from '@/components/ui/brand/logo';

// Define navigation item type
interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  children?: NavItem[];
}

// Define navigation items
const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: <Home className='h-5 w-5' />,
  },
  {
    title: 'User Management',
    href: '/admin/users',
    icon: <Users className='h-5 w-5' />,
  },
  {
    title: 'Templates',
    href: '/admin/templates',
    icon: <FileText className='h-5 w-5' />,
    children: [
      {
        title: 'Template Management',
        href: '/admin/templates',
        icon: <FileText className='h-5 w-5' />,
      },
      {
        title: 'Legal Updates',
        href: '/admin/legal-updates',
        icon: <AlertTriangle className='h-5 w-5' />,
      },
      {
        title: 'Quarterly Review',
        href: '/admin/quarterly-review',
        icon: <BarChart3 className='h-5 w-5' />,
      },
    ],
  },
  {
    title: 'Content',
    href: '/admin/content',
    icon: <BookOpen className='h-5 w-5' />,
    children: [
      {
        title: 'Content Management',
        href: '/admin/content',
        icon: <BookOpen className='h-5 w-5' />,
      },
      {
        title: 'Content Analytics',
        href: '/admin/content/analytics',
        icon: <BarChart3 className='h-5 w-5' />,
      },
    ],
  },
  {
    title: 'Reports',
    href: '/admin/reports/documents',
    icon: <BarChart3 className='h-5 w-5' />,
    children: [
      {
        title: 'Document Activity',
        href: '/admin/reports/documents',
        icon: <FileText className='h-5 w-5' />,
      },
    ],
  },
  {
    title: 'Emergency',
    href: '/admin/emergency',
    icon: <Shield className='h-5 w-5' />,
    children: [
      {
        title: 'Evidence Review',
        href: '/admin/emergency',
        icon: <FileCheck className='h-5 w-5' />,
      },
      {
        title: 'Death Certificates',
        href: '/admin/emergency?type=Death',
        icon: <FileCheck className='h-5 w-5' />,
      },
      {
        title: 'Incapacitation Evidence',
        href: '/admin/emergency?type=Incapacitation',
        icon: <AlertTriangle className='h-5 w-5' />,
      },
    ],
  },
  {
    title: 'Billing',
    href: '/admin/billing',
    icon: <CreditCard className='h-5 w-5' />,
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: <Settings className='h-5 w-5' />,
  },
];

export function AdminNav() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  return (
    <div className='bg-background border-b border-gray-200 sticky top-0 z-30'>
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center justify-between'>
          {/* Logo */}
          <div className='flex items-center'>
            <Link href='/admin' className='flex items-center mr-8'>
              <Logo variant='small' />
            </Link>

            {/* Desktop Navigation */}
            <div className='md:flex md:items-center md:space-x-1'>
              {navItems.map(item =>
                item.children ? (
                  <NavigationMenu key={item.href} className='relative'>
                    <NavigationMenuList>
                      <NavigationMenuItem>
                        <NavigationMenuTrigger
                          className={cn(
                            'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                            pathname.startsWith(item.href) &&
                              !pathname.startsWith(`${item.href}/`)
                              ? 'text-green-2010c bg-green-2010c/10'
                              : item.children.some(child =>
                                    pathname.startsWith(child.href)
                                  )
                                ? 'text-green-2010c bg-green-2010c/10'
                                : 'text-black-6c hover:text-green-2010c hover:bg-green-2010c/5'
                          )}
                        >
                          {item.icon}
                          <span className='ml-2'>{item.title}</span>
                        </NavigationMenuTrigger>
                        <NavigationMenuContent className='w-[400px]'>
                          <ul className='grid w-full gap-3 p-4'>
                            {item.children.map(child => (
                              <li key={child.href}>
                                <NavigationMenuLink asChild>
                                  <Link
                                    href={child.href}
                                    className={cn(
                                      'block select-none rounded-md p-3 leading-none no-underline outline-none transition-colors',
                                      pathname === child.href ||
                                        pathname.startsWith(`${child.href}/`)
                                        ? 'bg-green-2010c/10 text-green-2010c'
                                        : 'hover:bg-green-2010c/5 hover:text-green-2010c text-black-6c'
                                    )}
                                  >
                                    <div className='flex items-center'>
                                      {child.icon}
                                      <div className='ml-2 text-sm font-medium leading-none'>
                                        {child.title}
                                      </div>
                                    </div>
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </NavigationMenuItem>
                    </NavigationMenuList>
                  </NavigationMenu>
                ) : (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                      pathname === item.href ||
                        pathname.startsWith(`${item.href}/`)
                        ? 'text-green-2010c bg-green-2010c/10'
                        : 'text-black-6c hover:text-green-2010c hover:bg-green-2010c/5'
                    )}
                  >
                    {item.icon}
                    <span className='ml-2'>{item.title}</span>
                  </Link>
                )
              )}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className='md:hidden'>
            <button
              className='p-2 rounded-md text-[var(--custom-gray-medium)] hover:text-black-6c hover:bg-gray-100'
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className='h-6 w-6' />
              ) : (
                <Menu className='h-6 w-6' />
              )}
            </button>
          </div>

          {/* User menu (placeholder) */}
          <div className='hidden md:flex items-center space-x-4'>
            <div className='flex items-center space-x-2'>
              <div className='h-8 w-8 rounded-full bg-green-2010c text-white flex items-center justify-center'>
                A
              </div>
              <span className='text-sm font-medium'>Administrator</span>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className='md:hidden border-t border-gray-200'>
          <div className='container mx-auto px-4 py-2 space-y-1'>
            {navItems.map(item => (
              <React.Fragment key={item.href}>
                {item.children ? (
                  <>
                    <div className='px-3 py-2 text-sm font-medium text-[var(--custom-gray-medium)]'>
                      {item.title}
                    </div>
                    {item.children.map(child => (
                      <Link
                        key={child.href}
                        href={child.href}
                        className={cn(
                          'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ml-4',
                          pathname === child.href ||
                            pathname.startsWith(`${child.href}/`)
                            ? 'text-green-2010c bg-green-2010c/10'
                            : 'text-black-6c hover:text-green-2010c hover:bg-green-2010c/5'
                        )}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {child.icon}
                        <span className='ml-2'>{child.title}</span>
                      </Link>
                    ))}
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                      pathname === item.href ||
                        pathname.startsWith(`${item.href}/`)
                        ? 'text-green-2010c bg-green-2010c/10'
                        : 'text-black-6c hover:text-green-2010c hover:bg-green-2010c/5'
                    )}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.icon}
                    <span className='ml-2'>{item.title}</span>
                  </Link>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
