/**
 * InfographicViewer Component
 *
 * A component for displaying infographic content with zoom capabilities.
 */

'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { InfographicContent } from '@/types/education';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';

interface InfographicViewerProps {
  infographic: InfographicContent;
  onView?: () => void;
}

export function InfographicViewer({
  infographic,
  onView,
}: InfographicViewerProps) {
  const [isZoomed, setIsZoomed] = useState(false);

  // Call onView when component mounts
  React.useEffect(() => {
    if (onView) {
      onView();
    }
  }, [onView]);

  return (
    <div className='flex flex-col items-center space-y-4 w-full max-w-3xl mx-auto'>
      <div className='relative rounded-lg overflow-hidden border'>
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant='ghost'
              className='w-full h-full p-0 block'
              onClick={() => setIsZoomed(true)}
            >
              {/* Placeholder for the actual image */}
              <div className='relative w-full aspect-[16/9]'>
                <div className='absolute inset-0 flex items-center justify-center bg-muted'>
                  {/* In a real implementation, this would be a Next.js Image component */}
                  <p className='text-center p-4'>
                    [Infographic: {infographic.title}]
                    <br />
                    <span className='text-sm text-muted-foreground'>
                      Click to enlarge
                    </span>
                  </p>
                </div>
              </div>
            </Button>
          </DialogTrigger>
          <DialogContent className='max-w-4xl w-[90vw]'>
            <div className='relative w-full aspect-[16/9]'>
              <div className='absolute inset-0 flex items-center justify-center bg-muted'>
                {/* In a real implementation, this would be a Next.js Image component */}
                <p className='text-center p-4'>
                  [Enlarged Infographic: {infographic.title}]
                </p>
              </div>
            </div>
            <p className='text-sm text-muted-foreground mt-2'>
              {infographic.altText}
            </p>
          </DialogContent>
        </Dialog>
      </div>

      <div className='w-full'>
        <h3 className='font-semibold text-lg'>{infographic.title}</h3>
        <p className='text-sm text-muted-foreground mt-1'>
          {infographic.description}
        </p>
      </div>

      <div className='flex items-center justify-between w-full'>
        <p className='text-sm'>
          <span className='font-medium'>Tags:</span>{' '}
          {infographic.tags.join(', ')}
        </p>

        <Button
          variant='outline'
          size='sm'
          onClick={() => {
            // In a real implementation, this would download the infographic
            console.log(`Downloading infographic: ${infographic.id}`);
          }}
        >
          Download
        </Button>
      </div>
    </div>
  );
}
