'use client';

import { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON> } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Headline, Subhead } from '@/components/ui/brand/typography';
import { toast } from 'sonner';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import type { EmergencyContact } from '@/hooks/useEmergencyContacts';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

const STORAGE_KEY = 'emergency-contact-form-data';

// Define the form schema to match Omit<EmergencyContact, 'id'>
const contactFormSchema = z.object({
  fullName: z.string().min(1, 'Full name is required'),
  relationship: z.string().min(1, 'Relationship is required'),
  phoneNumber: z
    .string()
    .length(17, 'Phone number must be exactly 17 characters')
    .refine(val => /^\+\d{2}\(\d{3}\)-\d{3}-\d{4}$/.test(val), {
      message: 'Phone number must be in format +38**************',
    }),
  emailAddress: z.string().email('Please enter a valid email address'),
  contactType: z.enum(['Medical', 'Other']),
  isPrimaryForType: z.boolean().optional(),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

interface ContactFormProps {
  initialData?: Omit<EmergencyContact, 'id'>;
  onSubmit: (data: Omit<EmergencyContact, 'id'>) => Promise<void>;
  onChange?: (data: Omit<EmergencyContact, 'id'>) => void;
  isEditing: boolean;
  isLoading?: boolean;
  storageKey?: string;
}

export function ContactForm({
  initialData,
  onSubmit,
  onChange,
  isEditing,
  isLoading = false,
  storageKey,
}: ContactFormProps) {
  const router = useRouter();

  // Try to load saved data from localStorage first
  const loadSavedData = () => {
    if (typeof window !== 'undefined' && !isEditing) {
      try {
        const key = storageKey || STORAGE_KEY;
        const savedData = localStorage.getItem(key);
        if (savedData) {
          return JSON.parse(savedData);
        }
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
    return initialData;
  };

  const savedData = loadSavedData();

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      fullName: savedData?.fullName || initialData?.fullName || '',
      relationship: savedData?.relationship || initialData?.relationship || '',
      phoneNumber: savedData?.phoneNumber || initialData?.phoneNumber || '',
      emailAddress: savedData?.emailAddress || initialData?.emailAddress || '',
      contactType:
        savedData?.contactType || initialData?.contactType || 'Other',
      isPrimaryForType:
        savedData?.isPrimaryForType || initialData?.isPrimaryForType || false,
    },
  });

  // This function directly saves to localStorage
  const saveToLocalStorage = (data: ContactFormValues) => {
    if (typeof window !== 'undefined' && storageKey) {
      localStorage.setItem(storageKey, JSON.stringify(data));
      console.log(`Saved to localStorage (${storageKey}):`, data);

      if (onChange) {
        onChange(data);
      }
    }
  };

  // Watch for all form changes
  const formValues = form.watch();

  // Save form data whenever any value changes
  useEffect(() => {
    saveToLocalStorage(formValues);
  }, [formValues]);

  const formatPhoneNumber = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.length === 0) return '';
    if (digits.length <= 2) return `+${digits}`;
    if (digits.length <= 5) return `+${digits.slice(0, 2)}(${digits.slice(2)}`;
    if (digits.length <= 8)
      return `+${digits.slice(0, 2)}(${digits.slice(2, 5)})-${digits.slice(5)}`;
    return `+${digits.slice(0, 2)}(${digits.slice(2, 5)})-${digits.slice(
      5,
      8
    )}-${digits.slice(8, 12)}`;
  };

  const handleSubmit = async (data: ContactFormValues) => {
    try {
      await onSubmit(data);
      // Clear localStorage on successful submission
      if (typeof window !== 'undefined') {
        localStorage.removeItem(STORAGE_KEY);
      }
      toast.success(
        `${data.fullName} has been ${isEditing ? 'updated' : 'added'}.`
      );
      router.push('/dashboard/member/emergency-contacts');
    } catch (error: any) {
      toast.error(
        error.message ||
          `Failed to ${isEditing ? 'update' : 'add'} contact. Please try again.`
      );
      console.error(
        `Error ${isEditing ? 'updating' : 'adding'} contact:`,
        error
      );
    }
  };

  if (isLoading) {
    return (
      <div className='max-w-3xl mx-auto text-center py-12'>
        <div className='animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4'></div>
        <p>Loading contact information...</p>
      </div>
    );
  }

  return (
    <div className='max-w-3xl mx-auto'>
      <Card className='mb-6 p-6'>
        <Link
          href='/dashboard/member/emergency-contacts'
          className='flex items-center text-muted-foreground hover:text-foreground mb-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Emergency Contacts
        </Link>
        <Headline>{isEditing ? 'Update' : 'Add'} Emergency Contact</Headline>
        {!isEditing && (
          <Subhead className='text-muted-foreground'>
            Add a new trusted individual to your emergency contacts
          </Subhead>
        )}
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{isEditing ? 'Edit' : 'Contact'} Information</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className='space-y-6'
            >
              <div className='space-y-4'>
                {/* Full Name */}
                <FormField
                  control={form.control}
                  name='fullName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter contact's full name"
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Relationship */}
                <FormField
                  control={form.control}
                  name='relationship'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Relationship</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='e.g., Spouse, Child, Friend'
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone Number */}
                <FormField
                  control={form.control}
                  name='phoneNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='+38**************'
                          {...field}
                          onChange={e => {
                            const formatted = formatPhoneNumber(e.target.value);
                            field.onChange(formatted);
                            const currentValues = form.getValues();
                            saveToLocalStorage({
                              ...currentValues,
                              phoneNumber: formatted,
                            });
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Address */}
                <FormField
                  control={form.control}
                  name='emailAddress'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type='email'
                          placeholder='<EMAIL>'
                          {...field}
                          onChange={e => {
                            field.onChange(e);
                            const currentValues = form.getValues();
                            saveToLocalStorage(currentValues);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contact Type */}
                <FormField
                  control={form.control}
                  name='contactType'
                  render={({ field }) => (
                    <FormItem className='space-y-3'>
                      <FormLabel>Contact Type</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={value => {
                            field.onChange(value);
                            const currentValues = form.getValues();
                            saveToLocalStorage({
                              ...currentValues,
                              contactType: value as 'Medical' | 'Other',
                            });
                          }}
                          defaultValue={field.value}
                          className='flex flex-col space-y-1'
                        >
                          <FormItem className='flex items-center space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='Medical' />
                            </FormControl>
                            <FormLabel className='font-normal'>
                              Medical Contact
                            </FormLabel>
                          </FormItem>
                          <FormItem className='flex items-center space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='Other' />
                            </FormControl>
                            <FormLabel className='font-normal'>
                              Other Contact
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Primary Checkbox */}
                <FormField
                  control={form.control}
                  name='isPrimaryForType'
                  render={({ field }) => (
                    <FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4'>
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={checked => {
                            const boolValue = checked === true;
                            field.onChange(boolValue);

                            // Update localStorage with explicit boolean
                            const currentValues = form.getValues();
                            saveToLocalStorage({
                              ...currentValues,
                              isPrimaryForType: boolValue,
                            });
                          }}
                        />
                      </FormControl>
                      <div className='space-y-1 leading-none'>
                        <FormLabel>
                          Set as primary {form.watch('contactType')} contact
                        </FormLabel>
                        <p className='text-sm text-muted-foreground'>
                          This contact will be prioritized for{' '}
                          {form.watch('contactType').toLowerCase()} emergencies.
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <div className='flex justify-end space-x-4 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() =>
                    router.push('/dashboard/member/emergency-contacts')
                  }
                >
                  Cancel
                </Button>
                <Button type='submit' disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting
                    ? isEditing
                      ? 'Updating...'
                      : 'Adding...'
                    : isEditing
                      ? 'Update Contact'
                      : 'Add Contact'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
