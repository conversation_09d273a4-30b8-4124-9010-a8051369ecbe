/**
 * Utility functions for admin access validation and management
 */

export const ADMIN_ROLES = {
  ADMINS: 'ADMINS',
  WELONTRUST: 'WELONTRUST',
} as const;

export type AdminRole = (typeof ADMIN_ROLES)[keyof typeof ADMIN_ROLES];

/**
 * Check if a user has admin privileges
 * @param userRoles - Array of user roles from Cognito
 * @returns boolean indicating if user is an admin
 */
export function isAdmin(userRoles: string[]): boolean {
  return userRoles.includes(ADMIN_ROLES.ADMINS);
}

/**
 * Check if a user has Welon Trust privileges
 * @param userRoles - Array of user roles from Cognito
 * @returns boolean indicating if user is Welon Trust
 */
export function isWelonTrust(userRoles: string[]): boolean {
  return userRoles.includes(ADMIN_ROLES.WELONTRUST);
}

/**
 * Check if a user has any admin role (ADMINS or WELONTRUST)
 * @param userRoles - Array of user roles from Cognito
 * @returns boolean indicating if user has any admin role
 */
export function hasAnyAdminRole(userRoles: string[]): boolean {
  return isAdmin(userRoles) || isWelonTrust(userRoles);
}

/**
 * Check if a user has a specific admin role
 * @param userRoles - Array of user roles from Cognito
 * @param requiredRole - The specific role to check for
 * @returns boolean indicating if user has the required role
 */
export function hasAdminRole(
  userRoles: string[],
  requiredRole: AdminRole
): boolean {
  return userRoles.includes(requiredRole);
}

/**
 * Check if a user has any of the specified admin roles
 * @param userRoles - Array of user roles from Cognito
 * @param allowedRoles - Array of roles to check for
 * @returns boolean indicating if user has any of the allowed roles
 */
export function hasAnyOfAdminRoles(
  userRoles: string[],
  allowedRoles: AdminRole[]
): boolean {
  return allowedRoles.some(role => userRoles.includes(role));
}

/**
 * Check if a user has all of the specified admin roles
 * @param userRoles - Array of user roles from Cognito
 * @param requiredRoles - Array of roles that are all required
 * @returns boolean indicating if user has all required roles
 */
export function hasAllAdminRoles(
  userRoles: string[],
  requiredRoles: AdminRole[]
): boolean {
  return requiredRoles.every(role => userRoles.includes(role));
}

/**
 * Get admin permissions based on user roles
 * @param userRoles - Array of user roles from Cognito
 * @returns Object with permission flags
 */
export function getAdminPermissions(userRoles: string[]) {
  const userIsAdmin = isAdmin(userRoles);
  const userIsWelonTrust = isWelonTrust(userRoles);
  const userHasAnyAdminRole = hasAnyAdminRole(userRoles);

  return {
    canManageUsers: userIsAdmin,
    canManageRoles: userIsAdmin,
    canManageTemplates: userIsAdmin,
    canViewReports: userHasAnyAdminRole,
    canManageBilling: userIsAdmin,
    canAccessEmergency: userHasAnyAdminRole,
    canManageSettings: userIsAdmin,
    canViewAnalytics: userHasAnyAdminRole,
    canManageContent: userIsAdmin,
    canAccessAuditLogs: userIsAdmin,
  };
}

/**
 * Validate admin access for a specific action
 * @param userRoles - Array of user roles from Cognito
 * @param requiredPermission - The permission required for the action
 * @returns boolean indicating if user can perform the action
 */
export function canPerformAdminAction(
  userRoles: string[],
  requiredPermission: keyof ReturnType<typeof getAdminPermissions>
): boolean {
  const permissions = getAdminPermissions(userRoles);
  return permissions[requiredPermission];
}

/**
 * Get user-friendly role names
 * @param roles - Array of role identifiers
 * @returns Array of user-friendly role names
 */
export function getRoleDisplayNames(roles: string[]): string[] {
  const roleMap: Record<string, string> = {
    [ADMIN_ROLES.ADMINS]: 'Administrator',
    [ADMIN_ROLES.WELONTRUST]: 'Welon Trust',
  };

  return roles.map(role => roleMap[role] || role);
}

/**
 * Check if user can access a specific admin route
 * @param userRoles - Array of user roles from Cognito
 * @param route - The route to check access for
 * @returns boolean indicating if user can access the route
 */
export function canAccessAdminRoute(
  userRoles: string[],
  route: string
): boolean {
  const userHasAnyAdminRole = hasAnyAdminRole(userRoles);

  // Basic admin access required for all admin routes
  if (!userHasAnyAdminRole) {
    return false;
  }

  const userIsAdmin = isAdmin(userRoles);

  // Routes that require full admin privileges
  const adminOnlyRoutes = [
    '/admin/users',
    '/admin/roles',
    '/admin/billing',
    '/admin/settings',
  ];

  if (adminOnlyRoutes.some(adminRoute => route.startsWith(adminRoute))) {
    return userIsAdmin;
  }

  // All other admin routes are accessible to any admin role
  return userHasAnyAdminRole;
}

/**
 * Generate error message for unauthorized access
 * @param userRoles - Array of user roles from Cognito
 * @param requiredRole - The role that was required
 * @returns Error message string
 */
export function getUnauthorizedMessage(
  userRoles: string[],
  requiredRole?: AdminRole
): string {
  if (userRoles.length === 0) {
    return 'You need to be logged in with administrator privileges to access this page.';
  }

  if (requiredRole) {
    const requiredRoleName = getRoleDisplayNames([requiredRole])[0];
    const userRoleNames = getRoleDisplayNames(userRoles);
    return `You need ${requiredRoleName} privileges to access this page. Your current roles: ${userRoleNames.join(', ')}.`;
  }

  return 'You need administrator privileges to access this page.';
}
