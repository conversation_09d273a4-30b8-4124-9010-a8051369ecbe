'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DMSConfigForm } from '@/components/emergency/dms-configuration';
import { DMSStatusCard } from '@/components/emergency/dms-status';
import {
  CheckInFrequency,
  CommunicationMethod,
  DMSConfiguration,
  EscalationProtocol,
} from '@/components/emergency/types';
import { AlertCircle, CheckCircle2, Calendar, PauseCircle } from 'lucide-react';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';
import { useDeadMansSwitch, DMSConfigData } from '@/hooks/useDeadMansSwitch';

const MS_PER_DAY = 24 * 60 * 60 * 1000;

const FREQUENCY_TO_DAYS: Record<string, number> = {
  WEEKLY: 7,
  BIWEEKLY: 14,
  MONTHLY: 30, // можеш замінити на коректне додавання місяця, якщо хочеш
};

const calculateNextCheckInDate = (
  frequency?: string,
  customDays?: number | null,
  fromDate: Date = new Date()
): Date => {
  const freq = frequency?.toUpperCase();

  if (freq === 'CUSTOM' && customDays && customDays > 0) {
    return new Date(fromDate.getTime() + customDays * MS_PER_DAY);
  }

  if (freq === 'MONTHLY') {
    const nextMonth = new Date(fromDate);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    return nextMonth;
  }

  const days = FREQUENCY_TO_DAYS[freq ?? ''] ?? 7; // дефолт 7 днів

  return new Date(fromDate.getTime() + days * MS_PER_DAY);
};

const showTemporaryAlert = (
  setAlert: React.Dispatch<
    React.SetStateAction<{ type: 'success' | 'error'; message: string } | null>
  >,
  alert: { type: 'success' | 'error'; message: string },
  duration = 5000
) => {
  setAlert(alert);
  setTimeout(() => setAlert(null), duration);
};



export default function DeadMansSwitchPage() {
  const { config: dmsConfig, loading, saveConfig } = useDeadMansSwitch();
  const [showConfigForm, setShowConfigForm] = useState(false);
  const [showPauseDialog, setShowPauseDialog] = useState(false);
  const [pauseReason, setPauseReason] = useState('');
  const [pauseUntil, setPauseUntil] = useState('');
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // Map DMSConfigData to DMSConfiguration for the UI components
  const mapConfigToUI = (config: DMSConfigData | null): DMSConfiguration | null => {
    if (!config) return null;

    // If the switch is active but doesn't have check-in dates, set them
    let nextCheckIn = config.nextCheckIn;
    let lastCheckIn = config.lastCheckIn;

    if (config.status === 'ACTIVE' && (!nextCheckIn || !lastCheckIn)) {
      const now = new Date();
      lastCheckIn = lastCheckIn || now.toISOString();

      // Calculate next check-in based on frequency if not set
      if (!nextCheckIn) {
        let nextDate = new Date(now);
        const frequencyUpper = config.frequency.toUpperCase();
        const addDays = (days: number) => nextDate.setDate(nextDate.getDate() + days);

        switch (frequencyUpper) {
          case 'WEEKLY':
            addDays(7);
            break;
          case 'BIWEEKLY':
            addDays(14);
            break;
          case 'MONTHLY':
            nextDate.setMonth(nextDate.getMonth() + 1);
            break;
          case 'CUSTOM':
            if (config.customFrequencyDays) {
              addDays(config.customFrequencyDays);
            } else {
              addDays(7); // Default to weekly if custom days not set
            }
            break;
          default:
            addDays(7); // Default to weekly
        }

        nextCheckIn = nextDate.toISOString();
      }
    }

    return {
      id: config.id || '',
      userId: config.userId || '',
      frequency: config.frequency.charAt(0) + config.frequency.slice(1).toLowerCase() as any,
      communicationMethod: config.communicationMethod.charAt(0) + config.communicationMethod.slice(1).toLowerCase() as any,
      escalationProtocol: config.escalationProtocol.charAt(0) + config.escalationProtocol.slice(1).toLowerCase() as any,
      personalMessage: config.personalMessage || '',
      status: config.status === 'ACTIVE' ? 'Active' :
        config.status === 'PAUSED' ? 'Paused' : 'Disabled' as any,
      nextCheckIn: nextCheckIn || undefined,
      lastCheckIn: lastCheckIn || undefined,
    };
  };

  const handleUpdateConfig = async (newConfig: Partial<DMSConfiguration>) => {
    try {
      const now = new Date();

      const frequency = (newConfig.frequency?.toUpperCase() as CheckInFrequency) ?? 'WEEKLY';
      const communicationMethod = (newConfig.communicationMethod?.toUpperCase() as CommunicationMethod) ?? undefined;
      const escalationProtocol = (newConfig.escalationProtocol?.toUpperCase() as EscalationProtocol) ?? undefined;

      const nextCheckInDate = calculateNextCheckInDate(frequency, newConfig.customFrequencyDays, now);

      const amplifyUpdate: Partial<DMSConfigData> = {
        isEnable: true,
        frequency,
        communicationMethod,
        escalationProtocol,
        personalMessage: newConfig.personalMessage ?? undefined,
        status: 'ACTIVE',
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
      };


      if (frequency === 'CUSTOM' && newConfig.customFrequencyDays) {
        amplifyUpdate.customFrequencyDays = newConfig.customFrequencyDays;
      }

      const success = await saveConfig(amplifyUpdate);

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? "Dead Man's Switch configuration updated successfully."
          : "Failed to update configuration.",
      });

      if (success) {
        setShowConfigForm(false);
      }
    } catch (err) {
      console.error('Failed to save config:', err);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: "Failed to update configuration.",
      });
    }
  };

  const handleCheckIn = async () => {
    try {
      const now = new Date();
      const nextCheckInDate = calculateNextCheckInDate(dmsConfig?.frequency, dmsConfig?.customFrequencyDays, now);

      const success = await saveConfig({
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
        status: 'ACTIVE',
      });

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? "Check-in successful. Your Dead Man's Switch has been reset."
          : "Failed to check in. Please try again.",
      });
    } catch (error) {
      console.error('Check-in error:', error);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: "Failed to check in. Please try again.",
      });
    }
  };

  const handlePause = () => {
    setShowPauseDialog(true);
  };

  const handlePauseConfirm = async () => {
    try {
      const updateData: Partial<DMSConfigData> = {
        status: 'PAUSED',
        pauseReason: pauseReason || null
      };

      if (pauseUntil) {
        updateData.pauseUntil = new Date(pauseUntil).toISOString();
      }

      const success = await saveConfig(updateData);

      if (success) {
        setShowPauseDialog(false);
        setPauseReason('');
        setPauseUntil('');
        setAlert({
          type: 'success',
          message: "Dead Man's Switch paused successfully.",
        });
      } else {
        setAlert({
          type: 'error',
          message: "Failed to pause Dead Man's Switch. Please try again.",
        });
      }
    } catch (error) {
      console.error('Pause error:', error);
      setAlert({
        type: 'error',
        message: "Failed to pause Dead Man's Switch. Please try again.",
      });
    }

    setTimeout(() => setAlert(null), 5000);
  };

  const handleResume = async () => {
    try {
      const now = new Date();
      const nextCheckInDate = calculateNextCheckInDate(dmsConfig?.frequency, dmsConfig?.customFrequencyDays, now);

      const success = await saveConfig({
        status: 'ACTIVE',
        pauseReason: null,
        pauseUntil: null,
        lastCheckIn: now.toISOString(),
        nextCheckIn: nextCheckInDate.toISOString(),
      });

      showTemporaryAlert(setAlert, {
        type: success ? 'success' : 'error',
        message: success
          ? "Dead Man's Switch resumed successfully."
          : "Failed to resume Dead Man's Switch. Please try again.",
      });
    } catch (error) {
      console.error('Resume error:', error);
      showTemporaryAlert(setAlert, {
        type: 'error',
        message: "Failed to resume Dead Man's Switch. Please try again.",
      });
    }
  };

  const handleTest = () => {
    setAlert({
      type: 'success',
      message:
        'Test initiated. A test notification has been sent to your emergency contacts.',
    });

    setTimeout(() => setAlert(null), 5000);
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <Headline className='mb-2'>Dead Man's Switch</Headline>
        <Subhead className='text-muted-foreground'>
          Configure automated safety checks to detect potential incapacitation.
        </Subhead>
      </div>

      {alert && (
        <Alert
          className={`mb-6 ${
            alert.type === 'success'
              ? 'bg-green-50 text-green-800 border-green-200'
              : 'bg-destructive/10 text-destructive border-destructive/20'
          }`}
        >
          {alert.type === 'success' ? (
            <CheckCircle2 className='h-4 w-4' />
          ) : (
            <AlertCircle className='h-4 w-4' />
          )}
          <AlertTitle>
            {alert.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription>{alert.message}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <Card className="p-6">
          <div className="flex justify-center items-center h-40">
            <p>Loading Dead Man's Switch configuration...</p>
          </div>
        </Card>
      ) : (
        <>
          {!showConfigForm && (
            <>
              <Card className='mb-8'>
                <CardHeader>
                  <CardTitle>About Dead Man's Switch</CardTitle>
                  <CardDescription>
                    The Dead Man's Switch is an automated feature that triggers
                    notifications and potentially grants document access if you miss
                    check-ins.
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <h3 className='font-semibold mb-1'>How It Works</h3>
                    <p className='text-sm text-muted-foreground'>
                      You'll receive regular check-in notifications based on your
                      selected frequency. If you miss multiple check-ins, the system
                      will escalate according to your protocol.
                    </p>
                  </div>
                  <div>
                    <h3 className='font-semibold mb-1'>Escalation Process</h3>
                    <p className='text-sm text-muted-foreground'>
                      <strong>Standard (3 misses):</strong> First miss: Reminder to
                      you. Second miss: Alerts your contacts. Third miss: Triggers
                      incapacitation process.
                    </p>
                  </div>
                  <div className='pt-2'>
                    <p className='text-sm'>
                      <strong>Note:</strong> You can pause the switch at any time
                      for travel or other reasons.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <DMSStatusCard
                config={dmsConfig}
                onCheckIn={handleCheckIn}
                onPause={handlePause}
                onResume={handleResume}
                onTest={handleTest}
                onConfigure={() => setShowConfigForm(true)}
              />
            </>
          )}

          {showConfigForm && (
            <DMSConfigForm
              onSubmit={(data) => {
                // TODO: fix this
                const mappedData: Partial<DMSConfiguration> = {
                  ...data,
                  customFrequencyDays: data.customFrequencyDays ?? undefined,
                  customEscalationSteps: data.customEscalationSteps ?? undefined,
                  personalMessage: data.personalMessage ?? undefined,
                  pauseReason: data.pauseReason ?? undefined,
                  pauseUntil: data.pauseUntil ?? undefined,
                  nextCheckIn: data.nextCheckIn ?? undefined,
                  lastCheckIn: data.lastCheckIn ?? undefined
                };
                handleUpdateConfig(mappedData);
              }}
              onCancel={() => setShowConfigForm(false)}
              initialData={dmsConfig || {}}
            />
          )}
        </>
      )}

      <Dialog open={showPauseDialog} onOpenChange={setShowPauseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Pause Dead Man's Switch</DialogTitle>
            <DialogDescription>
              Your Dead Man's Switch will be paused and won't send check-in
              notifications until you resume it.
            </DialogDescription>
          </DialogHeader>

          <div className='space-y-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='pauseReason'>Reason for pausing (optional)</Label>
              <Textarea
                id='pauseReason'
                value={pauseReason}
                onChange={e => setPauseReason(e.target.value)}
                placeholder='e.g., Traveling, Hospital stay'
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='pauseUntil' className='flex items-center'>
                <Calendar className='mr-2 h-4 w-4' />
                Pause until (optional)
              </Label>
              <Input
                id='pauseUntil'
                type='date'
                value={pauseUntil}
                onChange={e => setPauseUntil(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                max={
                  new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split('T')[0]
                } // Max 90 days
              />
              <p className='text-xs text-muted-foreground'>
                If no date is selected, the switch will remain paused until you
                manually resume it.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant='outline' onClick={() => setShowPauseDialog(false)}>
              Cancel
            </Button>
            <Button
              variant='outline'
              onClick={handlePauseConfirm}
              className='bg-blue-2157c hover:bg-blue-2157c/90'
            >
              <PauseCircle className='mr-2 h-4 w-4' />
              Pause Switch
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
