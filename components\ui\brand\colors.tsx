'use client';

import React from 'react';

interface ColorSwatchProps {
  color: string;
  name: string;
  hex: string;
  className?: string;
}

export function ColorSwatch({
  color,
  name,
  hex,
  className = '',
}: ColorSwatchProps) {
  return (
    <div className={`flex flex-col ${className}`}>
      <div
        className={`w-20 h-20 rounded-md mb-2 border border-gray-200 ${color}`}
        style={{ backgroundColor: hex }}
      />
      <div className='text-sm font-bold'>{name}</div>
      <div className='text-xs text-[var(--custom-gray-medium)]'>{hex}</div>
    </div>
  );
}

export function CoreColorPalette() {
  return (
    <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
      <ColorSwatch color='bg-black-6c' name='Black 6C' hex='#101820' />
      <ColorSwatch color='bg-blue-2157c' name='2157C' hex='#7393B4' />
      <ColorSwatch color='bg-green-2010c' name='2010C' hex='#7FAD00' />
      <ColorSwatch color='bg-gray-6197c' name='6197C' hex='#D6D8D1' />
    </div>
  );
}

export function SecondaryColorPalette() {
  return (
    <div className='grid grid-cols-2 md:grid-cols-5 gap-4'>
      <ColorSwatch color='bg-light-green' name='Light Green' hex='#B7C69A' />
      <ColorSwatch color='bg-medium-green' name='Medium Green' hex='#87A16A' />
      <ColorSwatch color='bg-dark-blue' name='Dark Blue' hex='#223F57' />
      <ColorSwatch color='bg-warning' name='Warning' hex='#F15A29' />
      <ColorSwatch color='bg-off-white' name='Off White' hex='#EAEBE7' />
    </div>
  );
}
