import { signUp } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

export interface CustomSignUpRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  state: string;
  birthdayDate: Date;
  phone: string;
}

export interface CustomSignUpResponse {
  success: boolean;
  error?: string;
  message?: string;
}

/**
 * Custom sign-up function that creates user in Cognito but doesn't auto-confirm
 * Instead, it generates a verification token and sends a custom email
 */
export const customSignUp = async (
  request: CustomSignUpRequest
): Promise<CustomSignUpResponse> => {
  try {
    const client = generateClient<Schema>({
      authMode: 'iam',
    });

    // Format phone number to E.164 format for AWS Cognito
    const formattedPhone = request.phone.startsWith('+1')
      ? request.phone
      : `+1${request.phone.replace(/\D/g, '')}`;

    // Format date to YYYY-MM-DD for AWS Cognito
    const formattedBirthday = request.birthdayDate
      ? request.birthdayDate.toISOString().split('T')[0]
      : '';

    // Create user in Cognito (this will be unconfirmed)
    await signUp({
      username: request.email,
      password: request.password,
      options: {
        userAttributes: {
          email: request.email,
          given_name: request.firstName,
          family_name: request.lastName,
          address: request.state,
          birthdate: formattedBirthday,
          phone_number: formattedPhone,
          gender: 'male',
        },
        autoSignIn: false, // Don't auto sign in
      },
    });

    // Send verification email (token will be generated automatically on backend)
    const emailResult = await client.mutations.sendEmail({
      to: request.email,
      subject: 'Verify Your Email - Childfree',
      message: `Welcome to Childfree, ${request.firstName}!\n\nPlease verify your email address by clicking the link below to complete your registration.\n\nThis link will expire in 24 hours.`,
      emailType: 'accountConfirmation',
      isNewAccount: true,
    });

    const emailData = emailResult.data as any;

    // Try to parse if it's a string
    let parsedEmailData = emailData;
    if (typeof emailData === 'string') {
      try {
        parsedEmailData = JSON.parse(emailData);
        console.log('Parsed email data:', parsedEmailData);
      } catch (e) {
        console.log('Failed to parse email data as JSON:', e);
      }
    }

    if (!parsedEmailData?.success) {
      throw new Error(
        parsedEmailData?.error || 'Failed to send verification email'
      );
    }

    return {
      success: true,
      message:
        'Account created successfully. Please check your email to verify your account.',
    };
  } catch (error) {
    console.error('Custom sign-up error:', error);

    let errorMessage = 'Failed to create account';
    if (typeof error === 'object' && error !== null && 'message' in error) {
      errorMessage = (error as { message: string }).message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};
