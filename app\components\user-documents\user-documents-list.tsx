'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  Download,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  Archive,
  XCircle,
} from 'lucide-react';
import { useUserDocuments } from '@/hooks/useUserDocuments';
import { getUrl } from 'aws-amplify/storage';
import { toast } from 'sonner';

export function UserDocumentsList() {
  const { documents, loading, error, fetchUserDocuments } = useUserDocuments();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'rejected':
        return <XCircle className='h-4 w-4 text-red-600' />;
      case 'signed':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'shipped':
        return <Clock className='h-4 w-4 text-blue-600' />;
      case 'received':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'archived':
        return <Archive className='h-4 w-4 text-gray-600' />;
      default:
        return <FileText className='h-4 w-4 text-gray-600' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'signed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'shipped':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'received':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Document has been approved by Welon Trust';
      case 'rejected':
        return 'Document has been rejected by Welon Trust';
      case 'signed':
        return 'Document has been signed and is ready';
      case 'shipped':
        return 'Document has been shipped to you';
      case 'received':
        return 'Document has been received and processed';
      case 'archived':
        return 'Document has been archived';
      default:
        return 'Document status unknown';
    }
  };

  const handleDownload = async (document: any) => {
    try {
      if (!document.fileKey) {
        toast.error('File not available for download');
        return;
      }

      // Get signed URL from S3
      const result = await getUrl({
        path: document.fileKey,
        options: {
          validateObjectExistence: true,
        },
      });

      // Create a temporary link to download the file
      const link = window.document.createElement('a');
      link.href = result.url.toString();
      link.download = document.fileName || `${document.title}.pdf`;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

      toast.success(`Downloaded ${document.fileName}`);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Your Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
              <p className='text-muted-foreground'>Loading your documents...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Your Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant='destructive'>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={fetchUserDocuments}
            variant='outline'
            className='mt-4'
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <FileText className='h-5 w-5' />
          Your Documents
        </CardTitle>
        <p className='text-sm text-muted-foreground'>
          Documents that have been assigned to you by Welon Trust staff
        </p>
      </CardHeader>
      <CardContent className='max-h-96 overflow-y-auto'>
        {documents.length === 0 ? (
          <div className='text-center py-8'>
            <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
            <h3 className='text-lg font-medium text-muted-foreground mb-2'>
              No documents available
            </h3>
            <p className='text-muted-foreground'>
              Documents assigned to you will appear here.
            </p>
          </div>
        ) : (
          <div className='space-y-4'>
            {documents.map(document => (
              <div
                key={document.id}
                className='border rounded-lg p-4 hover:bg-muted/50 transition-colors'
              >
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-2'>
                      <FileText className='h-5 w-5 text-blue-600' />
                      <h4 className='font-medium'>{document.title}</h4>
                      <Badge className={getStatusColor(document.status)}>
                        <div className='flex items-center gap-1'>
                          {getStatusIcon(document.status)}
                          {document.status.replace('_', ' ').toUpperCase()}
                        </div>
                      </Badge>
                    </div>

                    <div className='flex items-center gap-4 text-sm text-muted-foreground mb-2'>
                      <div className='flex items-center gap-1'>
                        <User className='h-4 w-4' />
                        <span>{document.type}</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Calendar className='h-4 w-4' />
                        <span>Created {formatDate(document.dateCreated)}</span>
                      </div>
                    </div>

                    {/* Rejection reason */}
                    {document.status === 'rejected' &&
                      document.rejectionReason && (
                        <div className='bg-red-50 border border-red-200 rounded-lg p-3 mb-2'>
                          <div className='flex items-start gap-2 text-sm'>
                            <XCircle className='h-4 w-4 text-red-600 mt-0.5' />
                            <div>
                              <span className='font-medium text-red-800'>
                                Rejection Reason:
                              </span>
                              <p className='text-red-700 mt-1'>
                                {document.rejectionReason}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                    <p className='text-sm text-muted-foreground'>
                      {getStatusMessage(document.status)}
                    </p>
                  </div>

                  <div className='flex items-center gap-2 ml-4'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleDownload(document)}
                      className='flex items-center gap-2'
                    >
                      <Download className='h-4 w-4' />
                      Download
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
