'use client';

import React from 'react';
import { useInterview } from './interview-context';

export const ProgressBar: React.FC = () => {
  const { progress, isComplete } = useInterview();
  // Round to nearest whole number
  const roundedProgress = Math.round(isComplete ? 100 : progress);

  return (
    <div className='w-full mb-8'>
      <div className='flex justify-between mb-1 text-sm'>
        <span>Progress</span>
        <span>{roundedProgress}% Complete</span>
      </div>
      <div className='w-full h-3 bg-gray-6197c rounded-full overflow-hidden'>
        <div
          className='h-full bg-[#7FAD00] rounded-full transition-all duration-300 ease-in-out'
          style={{ width: `${roundedProgress}%` }}
        />
      </div>
    </div>
  );
};
