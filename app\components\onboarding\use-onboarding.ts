import { useReducer, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  onboardingReducer,
  initialState,
  isStepComplete,
} from './onboarding-state';

export function useOnboarding() {
  const router = useRouter();
  const [state, dispatch] = useReducer(onboardingReducer, initialState);

  const setChildfree = useCallback((value: string) => {
    dispatch({ type: 'SET_CHILDFREE', payload: value });
  }, []);

  const setAge = useCallback((value: string) => {
    dispatch({ type: 'SET_AGE', payload: value });
  }, []);

  const setEstateSize = useCallback((value: string) => {
    dispatch({ type: 'SET_ESTATE_SIZE', payload: value });
  }, []);

  const handleNext = useCallback(() => {
    if (state.currentStep < 3) {
      dispatch({ type: 'NEXT_STEP' });
    } else if (isStepComplete(state)) {
      // First set loading state
      dispatch({ type: 'SET_LOADING', payload: true });

      // Log the data being saved (for debugging)
      console.log('Saving onboarding data:', {
        isChildfree: state.isChildfree,
        age: state.age,
        estateSize: state.estateSize,
      });

      // Simulate API call to save data
      setTimeout(() => {
        dispatch({ type: 'COMPLETE' });
        // Navigate to dashboard
        router.push('/dashboard');
      }, 1500);
    }
  }, [state, router]);

  const handleBack = useCallback(() => {
    dispatch({ type: 'PREV_STEP' });
  }, []);

  const canProceed = useCallback(() => {
    // Make sure we're checking if the current step is complete, not all steps
    const currentStepComplete = isStepComplete(state);
    console.log('Step complete check:', {
      currentStep: state.currentStep,
      isComplete: currentStepComplete,
      stepData:
        state.currentStep === 1
          ? state.isChildfree
          : state.currentStep === 2
            ? state.age
            : state.currentStep === 3
              ? state.estateSize
              : null,
    });
    return currentStepComplete && !state.loading;
  }, [state]);

  return {
    state,
    setChildfree,
    setAge,
    setEstateSize,
    handleNext,
    handleBack,
    canProceed,
  };
}
