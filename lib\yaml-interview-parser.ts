import yaml from 'js-yaml';
import {
  InterviewQuestion,
  QuestionType,
  QuestionCategory,
} from '@/types/interview-builder';

export interface YAMLQuestion {
  id: string;
  question: string;
  subquestion?: string;
  field: string;
  datatype?:
    | 'text'
    | 'date'
    | 'number'
    | 'email'
    | 'phone'
    | 'yesno'
    | 'checkboxes'
    | 'currency'
    | 'area';
  choices?: Array<{ label: string; value: string }>;
  required?: boolean;
  validation?: string;
  condition?: string;
  buttons?: Array<{ label: string; action: string }>;
  mandatory?: boolean;
  continue_button_field?: string;
  under?: string;
  depends_on?: string;
  show_if?: string;
}

export interface YAMLMetadata {
  title: string;
  short_title?: string;
  description: string;
  version: number;
}

export interface ParsedInterview {
  metadata: YAMLMetadata;
  questions: YAMLQuestion[];
  flow: InterviewFlow;
}

export interface InterviewFlow {
  nodes: FlowNode[];
  edges: FlowEdge[];
}

export interface FlowNode {
  id: string;
  type: 'start' | 'question' | 'condition' | 'end' | 'action';
  position: { x: number; y: number };
  data: any;
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  condition?: string;
  animated?: boolean;
  style?: any;
}

export class YAMLInterviewParser {
  static parseYAML(yamlContent: string): ParsedInterview {
    try {
      // Split YAML into documents
      const documents = yamlContent.split('---').filter(doc => doc.trim());

      let metadata: YAMLMetadata | null = null;
      const questions: YAMLQuestion[] = [];

      documents.forEach((doc, index) => {
        try {
          const parsed = yaml.load(doc.trim()) as any;

          if (parsed?.metadata) {
            metadata = parsed.metadata;
          } else if (parsed?.question || parsed?.mandatory) {
            const question = this.parseQuestion(parsed, index);
            if (question) {
              questions.push(question);
            }
          } else if (parsed?.code) {
            // Handle code blocks - these don't create questions but may affect flow
            console.log('Code block found:', parsed.code);
          }
          // Skip empty documents or comments
        } catch (err) {
          console.warn(`Failed to parse YAML document ${index}:`, err);
        }
      });

      if (!metadata) {
        throw new Error('No metadata found in YAML');
      }

      const flow = this.generateFlow(questions);

      return {
        metadata,
        questions,
        flow,
      };
    } catch (error) {
      console.error('Failed to parse YAML:', error);
      throw new Error(`YAML parsing failed: ${error}`);
    }
  }

  private static parseQuestion(
    parsed: any,
    index: number
  ): YAMLQuestion | null {
    if (!parsed.question && !parsed.mandatory) {
      return null;
    }

    const question: YAMLQuestion = {
      id: `q_${index}`,
      question: parsed.question || '',
      field: parsed.field || `field_${index}`,
      required: parsed.required || false,
      mandatory: parsed.mandatory || false,
    };

    if (parsed.subquestion) {
      question.subquestion = parsed.subquestion;
    }

    if (parsed.datatype) {
      question.datatype = parsed.datatype;
    }

    if (parsed.choices) {
      question.choices = this.parseChoices(parsed.choices);
    }

    if (parsed['validation code']) {
      question.validation = parsed['validation code'];
    }

    if (parsed.buttons) {
      question.buttons = this.parseButtons(parsed.buttons);
    }

    if (parsed.under) {
      question.under = parsed.under;
    }

    if (parsed.depends_on || parsed['depends on']) {
      question.depends_on = parsed.depends_on || parsed['depends on'];
    }

    if (parsed.show_if || parsed['show if']) {
      question.show_if = parsed.show_if || parsed['show if'];
    }

    if (parsed.continue_button_field) {
      question.continue_button_field = parsed.continue_button_field;
    }

    return question;
  }

  private static parseChoices(
    choices: any
  ): Array<{ label: string; value: string }> {
    if (Array.isArray(choices)) {
      return choices.map(choice => {
        if (typeof choice === 'string') {
          return { label: choice, value: choice };
        } else if (typeof choice === 'object') {
          const key = Object.keys(choice)[0];
          return { label: key, value: choice[key] };
        }
        return { label: String(choice), value: String(choice) };
      });
    }
    return [];
  }

  private static parseButtons(
    buttons: any
  ): Array<{ label: string; action: string }> {
    if (Array.isArray(buttons)) {
      return buttons.map(button => {
        if (typeof button === 'string') {
          return { label: button, action: button.toLowerCase() };
        } else if (typeof button === 'object') {
          const key = Object.keys(button)[0];
          return { label: key, action: button[key] };
        }
        return { label: String(button), action: String(button) };
      });
    }
    return [];
  }

  private static generateFlow(questions: YAMLQuestion[]): InterviewFlow {
    const nodes: FlowNode[] = [];
    const edges: FlowEdge[] = [];

    if (questions.length === 0) {
      return { nodes, edges };
    }

    // Build dependency maps for conditional logic
    const questionMap = new Map<string, YAMLQuestion & { index: number }>();
    const conditionalDependencies = new Map<string, string[]>(); // field -> dependent question fields
    const choiceBranches = new Map<
      string,
      { choice: any; dependentFields: string[] }[]
    >();

    // First pass: map all questions and analyze dependencies
    questions.forEach((question, index) => {
      questionMap.set(question.field, { ...question, index });

      // Analyze conditional dependencies
      if (question.show_if) {
        const dependsOnField = this.extractFieldFromCondition(question.show_if);
        if (dependsOnField) {
          if (!conditionalDependencies.has(dependsOnField)) {
            conditionalDependencies.set(dependsOnField, []);
          }
          conditionalDependencies.get(dependsOnField)!.push(question.field);
        }
      }

      // Analyze choice-based branching
      if (question.choices && question.choices.length > 0) {
        const branches = question.choices.map(choice => ({
          choice,
          dependentFields: this.findDependentFields(
            question.field,
            choice.value,
            questions
          ),
        }));
        choiceBranches.set(question.field, branches);
      }
    });

    return this.buildConditionalFlow(
      questions,
      questionMap,
      conditionalDependencies,
      choiceBranches
    );
  }

  private static extractFieldFromCondition(condition: string): string | null {
    // Extract field name from conditions like "marital_status == 'married'" or "has_children"
    const matches = condition.match(/(\w+)\s*(?:==|!=|>|<|>=|<=)/);
    if (matches) {
      return matches[1];
    }
    // Handle simple boolean conditions
    const boolMatch = condition.match(/^(\w+)$/);
    return boolMatch ? boolMatch[1] : null;
  }

  private static findDependentFields(
    field: string,
    choiceValue: string,
    questions: YAMLQuestion[]
  ): string[] {
    // Find all questions that depend on this field having this specific value
    const dependentQuestions = questions.filter(q => {
      if (!q.show_if) return false;
      // More precise matching: field name must be at word boundary and exact value match
      const fieldPattern = new RegExp(`\\b${field}\\b`);
      const valuePattern = new RegExp(`["']${choiceValue}["']`);
      return fieldPattern.test(q.show_if) && valuePattern.test(q.show_if);
    });

    // Sort by their original order in the questions array to maintain sequence
    return dependentQuestions
      .sort((a, b) => {
        const aIndex = questions.findIndex(q => q.field === a.field);
        const bIndex = questions.findIndex(q => q.field === b.field);
        return aIndex - bIndex;
      })
      .map(q => q.field);
  }

  private static buildConditionalFlow(
    questions: YAMLQuestion[],
    questionMap: Map<string, YAMLQuestion & { index: number }>,
    conditionalDependencies: Map<string, string[]>,
    choiceBranches: Map<string, { choice: any; dependentFields: string[] }[]>
  ): InterviewFlow {
    const nodes: FlowNode[] = [];
    const edges: FlowEdge[] = [];

    const baseX = 400;
    const baseY = 100;
    const ySpacing = 200;
    const xSpacing = 350;

    let currentY = baseY;
    const processedQuestions = new Set<string>();
    const nodePositions = new Map<string, { x: number; y: number }>();

    // Start node
    const startNodeId = 'start';
    nodes.push({
      id: startNodeId,
      type: 'start',
      position: { x: baseX, y: currentY },
      data: { label: 'Start Interview' },
    });
    currentY += ySpacing;

    // Process questions in order, handling branching
    let previousMainNode = startNodeId;

    questions.forEach((question, index) => {
      if (processedQuestions.has(question.field)) return;

      const nodeId = question.id;
      const isConditional = !!(question.show_if || question.depends_on);

      // Position main question node
      const questionX = baseX;
      const questionY = currentY;

      nodes.push({
        id: nodeId,
        type: 'question',
        position: { x: questionX, y: questionY },
        data: {
          question: question.question,
          field: question.field,
          datatype: question.datatype || 'text',
          required: question.required || false,
          conditional: isConditional,
          subquestion: question.subquestion,
          choices: question.choices || [],
        },
      });

      nodePositions.set(question.field, { x: questionX, y: questionY });
      processedQuestions.add(question.field);

      // Connect to previous main flow node
      if (!isConditional) {
        edges.push({
          id: `${previousMainNode}-${nodeId}`,
          source: previousMainNode,
          target: nodeId,
          animated: true,
        });
        previousMainNode = nodeId;
      } else {
        // For conditional questions, connect based on their conditions
        const dependsOnField = this.extractFieldFromCondition(
          question.show_if || ''
        );
        if (dependsOnField && nodePositions.has(dependsOnField)) {
          edges.push({
            id: `q_${dependsOnField}-${nodeId}`,
            source: `q_${dependsOnField}`,
            target: nodeId,
            animated: true,
            label: `If: ${this.formatCondition(question.show_if || '')}`,
            condition: question.show_if,
          });
        }
      }

      // Handle choice-based branching - only create branches for immediate dependents
      if (choiceBranches.has(question.field)) {
        const branches = choiceBranches.get(question.field)!;

        branches.forEach((branch, branchIndex) => {
          if (branch.dependentFields.length > 0) {
            // Only connect to the FIRST dependent question in each branch
            // The rest will be handled as a sequential flow within that branch
            const firstDepField = branch.dependentFields[0];
            const depQuestion = questionMap.get(firstDepField);

            if (depQuestion && !processedQuestions.has(firstDepField)) {
              const branchX =
                questionX +
                (branchIndex - (branches.length - 1) / 2) * xSpacing;
              const branchY = questionY + ySpacing;
              const depNodeId = `q_${firstDepField}`;

              nodes.push({
                id: depNodeId,
                type: 'question',
                position: { x: branchX, y: branchY },
                data: {
                  question: depQuestion.question,
                  field: depQuestion.field,
                  datatype: depQuestion.datatype || 'text',
                  required: depQuestion.required || false,
                  conditional: true,
                  subquestion: depQuestion.subquestion,
                  choices: depQuestion.choices || [],
                },
              });

              // Connect choice to first dependent question
              edges.push({
                id: `${nodeId}-${depNodeId}`,
                source: nodeId,
                target: depNodeId,
                animated: true,
                label: branch.choice.label,
                condition: `${question.field} == "${branch.choice.value}"`,
              });

              processedQuestions.add(firstDepField);
              nodePositions.set(firstDepField, { x: branchX, y: branchY });

              // Handle subsequent questions in this branch as a sequential flow
              let prevBranchNode = depNodeId;
              let branchCurrentY = branchY + ySpacing;

              for (let i = 1; i < branch.dependentFields.length; i++) {
                const nextDepField = branch.dependentFields[i];
                const nextDepQuestion = questionMap.get(nextDepField);

                if (nextDepQuestion && !processedQuestions.has(nextDepField)) {
                  const nextDepNodeId = `q_${nextDepField}`;

                  nodes.push({
                    id: nextDepNodeId,
                    type: 'question',
                    position: { x: branchX, y: branchCurrentY },
                    data: {
                      question: nextDepQuestion.question,
                      field: nextDepQuestion.field,
                      datatype: nextDepQuestion.datatype || 'text',
                      required: nextDepQuestion.required || false,
                      conditional: true,
                      subquestion: nextDepQuestion.subquestion,
                      choices: nextDepQuestion.choices || [],
                    },
                  });

                  // Connect sequentially within the branch
                  edges.push({
                    id: `${prevBranchNode}-${nextDepNodeId}`,
                    source: prevBranchNode,
                    target: nextDepNodeId,
                    animated: true,
                  });

                  processedQuestions.add(nextDepField);
                  nodePositions.set(nextDepField, {
                    x: branchX,
                    y: branchCurrentY,
                  });
                  prevBranchNode = nextDepNodeId;
                  branchCurrentY += ySpacing;
                }
              }
            }
          }
        });
      }

      currentY += ySpacing;
    });

    // End node
    const endNodeId = 'end';
    nodes.push({
      id: endNodeId,
      type: 'end',
      position: { x: baseX, y: currentY + 100 },
      data: { label: 'Complete Interview' },
    });

    // Connect main flow to end
    if (previousMainNode !== startNodeId) {
      edges.push({
        id: `${previousMainNode}-${endNodeId}`,
        source: previousMainNode,
        target: endNodeId,
        animated: true,
      });
    }

    // Connect any unconnected conditional branches to end
    nodes.forEach(node => {
      if (node.type === 'question' && node.data.conditional) {
        const hasOutgoingEdge = edges.some(edge => edge.source === node.id);
        if (!hasOutgoingEdge) {
          edges.push({
            id: `${node.id}-${endNodeId}`,
            source: node.id,
            target: endNodeId,
            animated: true,
            style: { strokeDasharray: '5,5', stroke: '#9ca3af' },
          });
        }
      }
    });

    // Validate that all edges have valid source and target nodes
    const nodeIds = new Set(nodes.map(n => n.id));
    const validEdges = edges.filter(edge => {
      const hasValidSource = nodeIds.has(edge.source);
      const hasValidTarget = nodeIds.has(edge.target);
      if (!hasValidSource || !hasValidTarget) {
        console.warn(`Invalid edge: ${edge.source} -> ${edge.target}`, {
          hasValidSource,
          hasValidTarget,
          availableNodes: Array.from(nodeIds),
        });
        return false;
      }
      return true;
    });

    console.log('Generated conditional flow:', {
      nodes: nodes.length,
      edges: edges.length,
      validEdges: validEdges.length,
      conditionalDependencies: Array.from(conditionalDependencies.entries()),
      choiceBranches: Array.from(choiceBranches.entries()).map(
        ([field, branches]) => ({
          field,
          branchCount: branches.length,
          totalDependents: branches.reduce(
            (sum, b) => sum + b.dependentFields.length,
            0
          ),
        })
      ),
    });
    console.log(
      'Nodes:',
      nodes.map(n => ({
        id: n.id,
        type: n.type,
        conditional: n.data.conditional,
      }))
    );
    console.log(
      'Valid Edges:',
      validEdges.map(e => ({
        id: e.id,
        source: e.source,
        target: e.target,
        label: e.label,
        condition: e.condition,
      }))
    );

    return { nodes, edges: validEdges };
  }

  private static formatCondition(condition: string): string {
    // Clean up condition text for display
    return (
      condition
        .replace(/==/g, ' equals ')
        .replace(/!=/g, ' not equals ')
        .replace(/&&/g, ' and ')
        .replace(/\|\|/g, ' or ')
        .replace(/"/g, '')
        .substring(0, 30) + (condition.length > 30 ? '...' : '')
    );
  }

  static generateYAML(
    metadata: YAMLMetadata,
    questions: YAMLQuestion[]
  ): string {
    let yamlContent = `---
# Interview: ${metadata.title}
# Description: ${metadata.description}
# Generated by Interview Builder

metadata:
  title: ${metadata.title}
  short title: ${metadata.short_title || metadata.title}
  description: ${metadata.description}
  version: ${metadata.version}
  
---
# Interview Questions
`;

    questions.forEach(question => {
      if (question.mandatory) {
        yamlContent += `
mandatory: True
question: |
  ${question.question}`;

        if (question.subquestion) {
          yamlContent += `
subquestion: |
  ${question.subquestion}`;
        }

        if (question.buttons) {
          yamlContent += `
buttons:`;
          question.buttons.forEach(button => {
            yamlContent += `
  - ${button.label}: ${button.action}`;
          });
        }
      } else {
        yamlContent += `
question: |
  ${question.question}`;

        if (question.subquestion) {
          yamlContent += `
subquestion: |
  ${question.subquestion}`;
        }

        yamlContent += `
field: ${question.field}`;

        if (question.datatype && question.datatype !== 'text') {
          yamlContent += `
datatype: ${question.datatype}`;
        }

        if (question.choices && question.choices.length > 0) {
          yamlContent += `
choices:`;
          question.choices.forEach(choice => {
            yamlContent += `
  - ${choice.label}: ${choice.value}`;
          });
        }

        if (question.required) {
          yamlContent += `
required: True`;
        }

        if (question.validation) {
          yamlContent += `
validation code: |
  ${question.validation}`;
        }

        if (question.show_if) {
          yamlContent += `
show if: ${question.show_if}`;
        }

        if (question.depends_on) {
          yamlContent += `
depends on: ${question.depends_on}`;
        }

        if (question.under) {
          yamlContent += `
under: ${question.under}`;
        }
      }

      yamlContent += `
---`;
    });

    return yamlContent;
  }

  // Convert YAML questions to InterviewQuestion format for the Questions tab
  static convertToInterviewQuestions(
    yamlQuestions: YAMLQuestion[]
  ): InterviewQuestion[] {
    return yamlQuestions.map((yamlQ, index) => {
      // Map YAML datatype to QuestionType
      const getQuestionType = (datatype?: string): QuestionType => {
        switch (datatype) {
          case 'date':
            return 'date';
          case 'number':
            return 'number';
          case 'email':
            return 'email';
          case 'phone':
            return 'phone';
          case 'yesno':
            return 'radio'; // Map yesno to radio with Yes/No options
          case 'checkboxes':
            return 'checkbox';
          default:
            if (yamlQ.choices && yamlQ.choices.length > 0) {
              return 'radio';
            }
            return 'text';
        }
      };

      // Determine category based on field name or question content
      const getCategory = (
        field: string,
        question: string
      ): QuestionCategory => {
        const fieldLower = field.toLowerCase();
        const questionLower = question.toLowerCase();

        if (
          fieldLower.includes('name') ||
          fieldLower.includes('birth') ||
          fieldLower.includes('marital') ||
          fieldLower.includes('spouse')
        ) {
          return 'personal';
        }
        if (
          fieldLower.includes('child') ||
          fieldLower.includes('family') ||
          fieldLower.includes('guardian')
        ) {
          return 'personal';
        }
        if (
          fieldLower.includes('asset') ||
          fieldLower.includes('business') ||
          fieldLower.includes('property') ||
          fieldLower.includes('financial') ||
          fieldLower.includes('retirement') ||
          fieldLower.includes('insurance')
        ) {
          return 'financial';
        }
        if (
          fieldLower.includes('healthcare') ||
          fieldLower.includes('medical') ||
          fieldLower.includes('health')
        ) {
          return 'medical';
        }
        if (
          fieldLower.includes('trust') ||
          fieldLower.includes('estate') ||
          fieldLower.includes('planning') ||
          fieldLower.includes('charitable')
        ) {
          return 'estate';
        }
        if (
          fieldLower.includes('emergency') ||
          fieldLower.includes('contact')
        ) {
          return 'emergency';
        }

        // Fallback based on question content
        if (
          questionLower.includes('healthcare') ||
          questionLower.includes('medical')
        ) {
          return 'medical';
        }
        if (
          questionLower.includes('trust') ||
          questionLower.includes('estate')
        ) {
          return 'estate';
        }
        if (
          questionLower.includes('asset') ||
          questionLower.includes('business') ||
          questionLower.includes('property')
        ) {
          return 'financial';
        }
        if (
          questionLower.includes('child') ||
          questionLower.includes('family')
        ) {
          return 'personal';
        }

        return 'personal';
      };

      const questionType = getQuestionType(yamlQ.datatype);
      const category = getCategory(yamlQ.field, yamlQ.question);

      const interviewQuestion: InterviewQuestion = {
        id: yamlQ.id,
        text: yamlQ.question,
        type: questionType,
        category: category,
        required: yamlQ.required || false,
        helpText: yamlQ.subquestion || '',
        order: index + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Add options if they exist
      if (yamlQ.choices && yamlQ.choices.length > 0) {
        interviewQuestion.options = yamlQ.choices.map(
          (choice, choiceIndex) => ({
            id: `${yamlQ.id}_opt_${choiceIndex}`,
            label: choice.label,
            value: choice.value,
          })
        );
      }

      // Add conditional logic if it exists
      if (yamlQ.show_if) {
        // Parse the show_if condition into ConditionalLogic format
        // For now, we'll create a simple conditional logic entry
        interviewQuestion.conditionalLogic = [
          {
            condition: 'equals',
            value: true,
            nextQuestionId: null, // Will be determined by the flow
          },
        ];
      }

      // Add template mapping
      interviewQuestion.templateMapping = yamlQ.field;

      return interviewQuestion;
    });
  }

  // Parse YAML and return both parsed structure and converted questions
  static parseYAMLWithQuestions(yamlContent: string): {
    parsed: ParsedInterview;
    interviewQuestions: InterviewQuestion[];
  } {
    const parsed = this.parseYAML(yamlContent);
    const interviewQuestions = this.convertToInterviewQuestions(
      parsed.questions
    );

    return {
      parsed,
      interviewQuestions,
    };
  }
}
