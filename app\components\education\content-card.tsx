/**
 * ContentCard Component
 *
 * A card component for displaying educational content in the library.
 */

import React from 'react';
import Link from 'next/link';
import { Content, ContentType } from '@/types/education';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface ContentCardProps {
  content: Content;
  onClick?: () => void;
}

export function ContentCard({ content, onClick }: ContentCardProps) {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get content type icon
  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case ContentType.VIDEO:
        return '🎬';
      case ContentType.ARTICLE:
        return '📄';
      case ContentType.INFOGRAPHIC:
        return '📊';
      case ContentType.AVATAR:
        return '🤖';
      case ContentType.TOOLTIP:
        return '💬';
      default:
        return '📚';
    }
  };

  // Get content type label
  const getContentTypeLabel = (type: ContentType) => {
    switch (type) {
      case ContentType.VIDEO:
        return 'Video';
      case ContentType.ARTICLE:
        return 'Article';
      case ContentType.INFOGRAPHIC:
        return 'Infographic';
      case ContentType.AVATAR:
        return 'Interactive Assistant';
      case ContentType.TOOLTIP:
        return 'Quick Definition';
      default:
        return 'Content';
    }
  };

  // Get content duration or length
  const getContentDuration = (content: Content) => {
    switch (content.type) {
      case ContentType.VIDEO:
        const minutes = Math.floor(content.duration / 60);
        const seconds = content.duration % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')} min`;
      case ContentType.ARTICLE:
        return `${content.readingTime} min read`;
      default:
        return null;
    }
  };

  return (
    <Card className='overflow-hidden transition-all hover:shadow-md'>
      <Link href={`/dashboard/education/${content.id}`} onClick={onClick}>
        <CardHeader className='p-4 pb-2'>
          <div className='flex justify-between items-start'>
            <Badge variant='outline' className='mb-2'>
              {getContentTypeIcon(content.type)}{' '}
              {getContentTypeLabel(content.type)}
            </Badge>
            {getContentDuration(content) && (
              <span className='text-xs text-muted-foreground'>
                {getContentDuration(content)}
              </span>
            )}
          </div>
          <h3 className='font-semibold text-lg line-clamp-2'>
            {content.title}
          </h3>
        </CardHeader>
        <CardContent className='p-4 pt-0'>
          <p className='text-sm text-muted-foreground line-clamp-3'>
            {content.description}
          </p>
        </CardContent>
      </Link>
      <CardFooter className='p-4 pt-0 flex flex-wrap gap-2 justify-between items-center'>
        <div className='flex flex-wrap gap-1'>
          {content.tags.slice(0, 3).map(tag => (
            <Badge key={tag} variant='secondary' className='text-xs'>
              {tag}
            </Badge>
          ))}
          {content.tags.length > 3 && (
            <Badge variant='secondary' className='text-xs'>
              +{content.tags.length - 3}
            </Badge>
          )}
        </div>
        <span className='text-xs text-muted-foreground'>
          Updated {formatDate(content.updatedAt)}
        </span>
      </CardFooter>
    </Card>
  );
}
