'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import {
  MoreHorizontal,
  Plus,
  Phone,
  Mail,
  MapPin,
  Trash2,
} from 'lucide-react';
import { Attorney } from '@/types/attorney-reviews';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { AttorneyManagementTableSkeleton } from '@/components/ui/data-table/data-table-skeleton';
import { useAttorneys } from '@/hooks/useAttorneys';
import { useModal } from '@/hooks/use-modal';
import { toast } from 'sonner';

interface AttorneyManagementTableProps {
  onEdit?: (attorney: Attorney) => void;
  onCreateNew?: () => void;
  compact?: boolean;
  className?: string;
}

// Define filter configurations
const tableConfig: DataTableConfig = {
  searchColumn: 'name',
  searchPlaceholder: 'Filter attorneys...',
  filters: [
    {
      id: 'isActive',
      title: 'Status',
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
    {
      id: 'state',
      title: 'State',
      options: [
        { value: 'CA', label: 'California' },
        { value: 'NY', label: 'New York' },
        { value: 'TX', label: 'Texas' },
        { value: 'FL', label: 'Florida' },
        { value: 'IL', label: 'Illinois' },
        // Add more states as needed
      ],
    },
    {
      id: 'isPreferred',
      title: 'Preferred',
      options: [
        { value: 'true', label: 'Preferred' },
        { value: 'false', label: 'Standard' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  defaultPageSize: 10,
};

export function AttorneyManagementTable({
  onEdit,
  onCreateNew,
  compact = false,
  className = '',
}: AttorneyManagementTableProps) {
  // Use the custom hook to fetch and manage attorney data
  const { attorneys, loading, error, updateAttorneyStatus, removeAttorney } =
    useAttorneys();

  // Use modal hook for delete confirmation
  const deleteModal = useModal();

  // Handle attorney actions
  const handleActivate = async (attorney: Attorney) => {
    try {
      await updateAttorneyStatus(attorney.id, true);
      toast.success('Attorney activated successfully');
    } catch (err) {
      console.error('Failed to activate attorney:', err);
      toast.error('Failed to activate attorney');
    }
  };

  const handleDeactivate = async (attorney: Attorney) => {
    try {
      await updateAttorneyStatus(attorney.id, false);
      toast.success('Attorney deactivated successfully');
    } catch (err) {
      console.error('Failed to deactivate attorney:', err);
      toast.error('Failed to deactivate attorney');
    }
  };

  const handleDeleteClick = (attorney: Attorney) => {
    deleteModal.open(attorney);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteModal.data) return;

    await deleteModal.confirm(async () => {
      await removeAttorney(deleteModal.data.id);
      toast.success(`Attorney ${deleteModal.data.name} has been deleted`);
    });
  };

  // Define columns for the data table
  const columns: ColumnDef<Attorney>[] = [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Attorney Name' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <div className='flex flex-col'>
            <span className='font-medium'>{attorney.name}</span>
            {attorney.firm && (
              <span className='text-sm text-muted-foreground'>
                {attorney.firm}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Contact' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <div className='flex flex-col space-y-1'>
            <div className='flex items-center text-sm'>
              <Phone className='h-3 w-3 mr-1 text-muted-foreground' />
              <span>{attorney.phone}</span>
            </div>
            <div className='flex items-center text-sm'>
              <Mail className='h-3 w-3 mr-1 text-muted-foreground' />
              <span>{attorney.email}</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'state',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Location' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <div className='flex items-center text-sm'>
            <MapPin className='h-3 w-3 mr-1 text-muted-foreground' />
            <span>
              {attorney.city ? `${attorney.city}, ` : ''}
              {attorney.state}
            </span>
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'specialties',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Specialties' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <div className='flex flex-wrap gap-1'>
            {attorney.specialties?.slice(0, 2).map((specialty, index) => (
              <Badge key={index} variant='secondary' className='text-xs'>
                {specialty}
              </Badge>
            ))}
            {attorney.specialties && attorney.specialties.length > 2 && (
              <Badge variant='outline' className='text-xs'>
                +{attorney.specialties.length - 2} more
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'yearsExperience',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Experience' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <div className='text-sm'>
            {attorney.yearsExperience
              ? `${attorney.yearsExperience} years`
              : 'N/A'}
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <Badge variant={attorney.isActive ? 'default' : 'secondary'}>
            {attorney.isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isActive = row.getValue(id) as boolean;
        return value.includes(isActive.toString());
      },
    },
    {
      accessorKey: 'isPreferred',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Preferred' />
      ),
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <Badge
            variant={attorney.isPreferred ? 'default' : 'outline'}
            className='text-xs'
          >
            {attorney.isPreferred ? 'Preferred' : 'Standard'}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const isPreferred = row.getValue(id) as boolean;
        return value.includes(isPreferred.toString());
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const attorney = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(attorney)}>
                  Edit
                </DropdownMenuItem>
              )}
              {attorney.isActive ? (
                <DropdownMenuItem
                  onClick={() => handleDeactivate(attorney)}
                  className='text-orange-600'
                >
                  Deactivate
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  onClick={() => handleActivate(attorney)}
                  className='text-green-600'
                >
                  Activate
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={() => handleDeleteClick(attorney)}
                className='text-red-600'
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Show skeleton while loading
  if (loading) {
    return <AttorneyManagementTableSkeleton />;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>
            Attorney Management
          </h2>
        </div>
        {onCreateNew && (
          <Button onClick={onCreateNew} size={compact ? 'sm' : 'default'}>
            <Plus className='mr-2 h-4 w-4' />
            Create Attorney
          </Button>
        )}
      </div>

      {/* Universal Data Table */}
      <DataTable
        columns={columns}
        data={attorneys}
        config={tableConfig}
        loading={loading}
        error={error}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        {...deleteModal.modalProps}
        title='Delete Attorney'
        description={
          deleteModal.data
            ? `Are you sure you want to delete attorney "${deleteModal.data.name}"? This action cannot be undone.`
            : 'Are you sure you want to delete this attorney?'
        }
        confirmLabel={deleteModal.loading ? 'Deleting...' : 'Delete'}
        cancelLabel='Cancel'
        confirmVariant='destructive'
        onConfirm={handleDeleteConfirm}
        icon={<Trash2 className='h-6 w-6 text-red-600' />}
      />
    </div>
  );
}
