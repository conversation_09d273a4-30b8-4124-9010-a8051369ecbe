import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface DashboardContentSkeletonProps {
  showHeader?: boolean;
  cardCount?: number;
  showAdditionalContent?: boolean;
}

export function DashboardContentSkeleton({
  showHeader = true,
  cardCount = 6,
  showAdditionalContent = true,
}: DashboardContentSkeletonProps) {
  return (
    <div className='flex-1'>
      <main className='p-8'>
        <div className='max-w-7xl mx-auto'>
          {/* Page Header Skeleton */}
          {showHeader && (
            <div className='mb-8'>
              <Skeleton className='h-8 w-48 mb-2' />
              <Skeleton className='h-4 w-64' />
            </div>
          )}

          {/* Content Cards Skeleton */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'>
            {Array.from({ length: cardCount }).map((_, i) => (
              <Card key={i} className='p-6'>
                <CardHeader className='pb-3'>
                  <Skeleton className='h-5 w-32 mb-2' />
                  <Skeleton className='h-4 w-24' />
                </CardHeader>
                <CardContent>
                  <Skeleton className='h-20 w-full mb-4' />
                  <Skeleton className='h-9 w-20' />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Content Skeleton */}
          {showAdditionalContent && (
            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
              <Card className='p-6'>
                <CardHeader>
                  <Skeleton className='h-6 w-40 mb-2' />
                  <Skeleton className='h-4 w-32' />
                </CardHeader>
                <CardContent>
                  <div className='space-y-3'>
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className='flex items-center space-x-3'>
                        <Skeleton className='h-4 w-4' />
                        <Skeleton className='h-4 flex-1' />
                        <Skeleton className='h-4 w-16' />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className='p-6'>
                <CardHeader>
                  <Skeleton className='h-6 w-36 mb-2' />
                  <Skeleton className='h-4 w-28' />
                </CardHeader>
                <CardContent>
                  <Skeleton className='h-32 w-full mb-4' />
                  <div className='flex space-x-2'>
                    <Skeleton className='h-9 w-20' />
                    <Skeleton className='h-9 w-24' />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
